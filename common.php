<?php

use addons\dsassistant\library\ContentHelper;

if (!function_exists('ds_content')) {
    /**
     * 获取内容
     * 
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @param string $key 内容键名
     * @param mixed $default 默认值
     * @return mixed
     */
    function ds_content($page, $section, $key, $default = '')
    {
        return ContentHelper::content($page, $section, $key, $default);
    }
}

if (!function_exists('ds_config')) {
    /**
     * 获取配置值
     * 
     * @param string $name 配置名称
     * @param mixed $default 默认值
     * @return mixed
     */
    function ds_config($name, $default = '')
    {
        return ContentHelper::config($name, $default);
    }
}

if (!function_exists('ds_group_config')) {
    /**
     * 获取分组配置
     * 
     * @param string $group 分组名称
     * @return array
     */
    function ds_group_config($group)
    {
        return ContentHelper::groupConfig($group);
    }
}

if (!function_exists('ds_page_contents')) {
    /**
     * 获取页面所有内容
     * 
     * @param string $page 页面标识
     * @return array
     */
    function ds_page_contents($page)
    {
        return ContentHelper::pageContents($page);
    }
}

if (!function_exists('ds_section_contents')) {
    /**
     * 获取页面区块内容
     * 
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @return array
     */
    function ds_section_contents($page, $section)
    {
        return ContentHelper::sectionContents($page, $section);
    }
}
