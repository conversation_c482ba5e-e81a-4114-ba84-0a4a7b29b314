<?php

namespace addons\dsassistant;

use app\common\library\Menu;
use think\Addons;
use think\Config;
/**
 * 景区DeepSeek智能助理插件
 */
class Dsassistant extends Addons
{
    /**
     * 插件安装方法
     * @return bool
     */
    public function install()
    {
        $menu = require(__DIR__ . "/inc/backendmenu.php");
        Menu::create($menu);

        // 安装SQL
        $sqlFile = __DIR__ . '/install.sql';
        if (is_file($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            $sql = str_replace("\r", "\n", $sql);
            $sql = explode(";\n", $sql);
            $sql = array_filter($sql);
            \think\Db::startTrans();
            try {
                foreach ($sql as $item) {
                    \think\Db::execute($item);
                }
                \think\Db::commit();
            } catch (\Exception $e) {
                \think\Db::rollback();
                \think\Log::error('安装SQL失败: ' . $e->getMessage());
                return false;
            }
        }

        // 安装LLM模型表
        $llmModelsSql = __DIR__ . '/sql/install_llm_models.sql';
        if (is_file($llmModelsSql)) {
            $sql = file_get_contents($llmModelsSql);
            $sql = str_replace("\r", "\n", $sql);
            $sql = explode(";\n", $sql);
            $sql = array_filter($sql);
            \think\Db::startTrans();
            try {
                foreach ($sql as $item) {
                    \think\Db::execute($item);
                }
                \think\Db::commit();
            } catch (\Exception $e) {
                \think\Db::rollback();
                \think\Log::error('安装LLM模型表失败: ' . $e->getMessage());
                return false;
            }
        }

        // 更新LLM模型的API密钥
        try {
            // 获取DeepSeek API密钥
            $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
            $apiKey = $config['deepseek_api_key'] ?? '';

            if (!empty($apiKey)) {
                // 更新所有DeepSeek模型的API密钥
                \think\Db::name('ds_llm_models')
                    ->where('provider', 'deepseek')
                    ->update([
                        'api_key' => $apiKey,
                        'updatetime' => time()
                    ]);
                \think\Log::info('更新LLM模型API密钥成功');
            }
        } catch (\Exception $e) {
            \think\Log::error('更新LLM模型API密钥失败: ' . $e->getMessage());
            // 不返回false，因为这不是致命错误
        }

        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall()
    {
        Menu::delete('dsassistant');

        return true;
    }

    /**
     * 插件启用方法
     * @return bool
     */
    public function enable()
    {
        Menu::enable('dsassistant');
        return true;
    }

    /**
     * 插件禁用方法
     * @return bool
     */
    public function disable()
    {
        Menu::disable('dsassistant');
        return true;
    }


    public function appInit(){

        if (request()->isCli()) {
            \think\Console::addDefaultCommands([
                'addons\dsassistant\command\IndexKnowledgeBase',
                'addons\dsassistant\command\CleanExpiredSessions',
                'addons\dsassistant\command\WarningPush',
                'addons\dsassistant\command\Test',
                'addons\dsassistant\command\TestDb',
                'addons\dsassistant\command\TestRag',
                'addons\dsassistant\command\TestRagPerformance',
                'addons\dsassistant\library\command\MigrateLLMConfig',
                'addons\dsassistant\library\command\InitLlmModels',
            ]);
        } else {
            // 注册模板标签库
            $config = Config::get('template');
            $taglib = isset($config['taglib_pre_load']) ? $config['taglib_pre_load'] : '';
            $taglib = explode(',', $taglib);
            $taglib[] = 'addons\\dsassistant\\library\\Ds';
            $taglib = array_filter(array_unique($taglib));
            Config::set('template.taglib_pre_load', implode(',', $taglib));
        }
    }
}
