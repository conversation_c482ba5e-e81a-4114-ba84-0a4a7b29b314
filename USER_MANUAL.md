# 景区DeepSeek智能助理插件使用文档

## 1. 插件简介

景区DeepSeek智能助理插件是基于FastAdmin框架开发的一款智能服务插件，集成了DeepSeek AI技术，为景区提供智能问答、实时预警、电子导览和失物招领等功能。本插件支持微信公众号、小程序和网站三种使用方式，能够全方位提升游客服务体验。

## 2. 安装与配置

### 2.1 安装插件

1. 在FastAdmin后台进入插件管理页面
2. 点击"上传插件"按钮，选择插件压缩包
3. 等待系统自动安装完成

### 2.2 基础配置

安装完成后，进入插件配置页面，需要配置以下内容：

#### 向量嵌入服务配置
选择一个主要服务提供商和一个备用服务提供商，并配置对应的API密钥：

1. **百度文心大模型**
   - 开通方式：访问 https://cloud.baidu.com/product/wenxinworkshop
   - 创建应用并获取API Key和Secret Key
   - 在插件配置中填写对应的密钥

2. **讯飞星火大模型**
   - 开通方式：访问 https://xinghuo.xfyun.cn/
   - 创建应用并获取AppID、API Key和API Secret
   - 在插件配置中填写对应的密钥

3. **阿里云通义千问**
   - 开通方式：访问 https://help.aliyun.com/document_detail/2399480.html
   - 开通服务并获取API Key
   - 在插件配置中填写API Key

4. **腾讯云混元大模型**
   - 开通方式：访问 https://cloud.tencent.com/product/hunyuan
   - 开通服务并获取SecretId和SecretKey
   - 在插件配置中填写对应的密钥

#### 性能配置
- **缓存时间**：问答结果缓存时间，单位为秒
- **置信度阈值**：AI回答的置信度阈值，低于此值将转人工客服

#### 微信配置
- **微信公众号AppID**：微信公众号的AppID
- **微信公众号AppSecret**：微信公众号的AppSecret
- **微信公众号Token**：微信公众号的Token

#### 地图配置
- **高德地图Key**：高德地图开发者Key，用于地图显示和导航功能

#### 基础配置
- **客服电话**：当AI无法回答问题时显示的客服电话

## 3. 功能使用说明

### 3.1 后台管理

#### 3.1.1 知识库管理

路径：`后台 > 景区助理 > 知识库管理`

功能：
- 添加、编辑、删除知识库条目
- 设置问题、答案、分类、关键词和权重
- 启用/禁用知识库条目

操作步骤：
1. 点击"添加"按钮创建新的知识库条目
2. 填写问题、答案、分类等信息
3. 设置权重（权重越高，匹配优先级越高）
4. 点击"确定"保存

#### 3.1.2 聊天记录管理

路径：`后台 > 景区助理 > 聊天记录`

功能：
- 查看用户与智能助理的聊天记录
- 按平台、来源、时间等条件筛选记录
- 导出聊天记录数据

#### 3.1.3 预警管理

路径：`后台 > 景区助理 > 预警管理`

功能：
- 添加、编辑、删除预警信息
- 设置预警类型、级别、有效时间
- 推送预警信息到各平台

操作步骤：
1. 点击"添加"按钮创建新的预警信息
2. 填写标题、内容、选择类型和级别
3. 设置开始时间和结束时间
4. 点击"确定"保存
5. 选择预警信息，点击"推送"按钮将预警推送到各平台

#### 3.1.4 景点管理

路径：`后台 > 景区助理 > 景点管理`

功能：
- 添加、编辑、删除景点信息
- 设置景点位置、描述、开放时间等
- 上传景点图片

操作步骤：
1. 点击"添加"按钮创建新的景点
2. 填写名称、描述、地址等基本信息
3. 设置经纬度坐标（可通过地图选点获取）
4. 上传景点图片
5. 点击"确定"保存

#### 3.1.5 失物招领管理

路径：`后台 > 景区助理 > 失物招领`

功能：
- 查看、编辑、删除失物招领信息
- 更新处理状态（待处理、已处理、已关闭）
- 查看联系人信息

操作步骤：
1. 在列表中查看所有失物招领信息
2. 点击"编辑"按钮修改信息或更新状态
3. 处理完成后将状态更改为"已处理"或"已关闭"

### 3.2 前台功能

#### 3.2.1 网站集成

网站首页提供以下功能入口：
- 智能问答
- 景点导览
- 失物招领
- 预警信息

##### 智能问答页面

功能：
- 与AI助手进行自然语言对话
- 查询景区相关信息
- 查看历史对话记录

使用方法：
1. 在输入框中输入问题
2. 点击发送按钮或按回车键
3. 等待AI助手回复

##### 景点导览页面

功能：
- 查看景区地图和景点分布
- 浏览景点详情和图片
- 获取导航指引

使用方法：
1. 在地图上查看景点位置
2. 点击景点图标或列表中的景点查看详情
3. 点击"导航"按钮获取到达该景点的路线

##### 失物招领页面

功能：
- 浏览失物招领信息
- 发布失物或招领信息
- 联系物品失主或拾获者

使用方法：
1. 浏览失物招领列表
2. 点击"登记物品"按钮发布新信息
3. 填写物品信息、联系方式等
4. 点击提交按钮发布

##### 预警信息页面

功能：
- 查看当前有效的预警信息
- 按类型和级别筛选预警
- 获取预警详情

#### 3.2.2 微信公众号

配置步骤：
1. 登录微信公众平台
2. 进入"开发 > 基本配置"
3. 设置服务器配置：
   - URL：`https://您的域名/addons/dsassistant/wechat`
   - Token：与插件配置中的Token保持一致
4. 进入"自定义菜单"配置菜单项

功能：
- 发送文字消息进行智能问答
- 通过菜单访问景点导览、失物招领等功能
- 接收预警推送通知

#### 3.2.3 小程序

配置步骤：
1. 将插件目录下的`uniapp/dsassistant`导入HBuilderX
2. 修改`main.js`中的API地址配置
3. 编译发布小程序

功能：
- 首页：展示景区概况和热门景点
- 问答：与AI助手进行对话
- 导览：查看景点地图和详情
- 失物招领：浏览和发布失物招领信息
- 预警：查看最新预警信息

## 4. 常见问题

### 4.1 AI无法回答问题

可能原因：
- DeepSeek API密钥配置错误
- 网络连接问题
- 问题内容违规被API拦截

解决方法：
- 检查API密钥配置
- 检查服务器网络连接
- 调整系统提示词

### 4.2 微信公众号配置失败

可能原因：
- URL配置错误
- Token不匹配
- 服务器无法被微信服务器访问

解决方法：
- 确认URL格式正确
- 确保Token与插件配置一致
- 检查服务器防火墙设置

### 4.3 地图显示异常

可能原因：
- 高德地图Key配置错误
- 景点经纬度坐标错误

解决方法：
- 检查高德地图Key配置
- 重新设置景点经纬度坐标

## 5. 技术支持

如遇到使用问题，请通过以下方式获取支持：

- 官方文档：[链接到文档]
- 技术支持邮箱：<EMAIL>
- 客服电话：400-123-4567（工作时间：周一至周五 9:00-18:00）

---

本文档最后更新时间：2024年5月25日
