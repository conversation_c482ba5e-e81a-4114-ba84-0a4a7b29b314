<script>
	export default {
		onLaunch: function() {
			console.log('App Launch');

			// 检查用户登录状态
			this.checkLoginStatus();

			// 获取系统信息
			this.getSystemInfo();
		},
		onShow: function() {
			console.log('App Show');
		},
		onHide: function() {
			console.log('App Hide');
		},
		methods: {
			// 检查登录状态
			checkLoginStatus() {
				const token = uni.getStorageSync('token');
				if (token) {
					// 验证token有效性
					uni.request({
						url: this.$baseUrl + '/api/checkToken',
						method: 'POST',
						data: { token: token },
						success: (res) => {
							if (res.data.code !== 1) {
								// token无效，清除存储
								uni.removeStorageSync('token');
								uni.removeStorageSync('userInfo');
							}
						}
					});
				}
			},

			// 获取系统信息
			getSystemInfo() {
				uni.getSystemInfo({
					success: (res) => {
						// 存储系统信息
						this.globalData.systemInfo = res;

						// 计算安全区域
						this.globalData.safeAreaBottom = res.safeAreaInsets ? res.safeAreaInsets.bottom : 0;
					}
				});
			}
		},
		globalData: {
			systemInfo: null,
			safeAreaBottom: 0,
			userInfo: null
		}
	}
</script>

<style>
	/* 引入ColorUI样式库 */
	@import "colorui/main.css";
	@import "colorui/icon.css";
	
	/* 全局样式 */
	page {
		background-color: #F5F5F5;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	/* 安全区域适配 */
	.safe-area-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>
