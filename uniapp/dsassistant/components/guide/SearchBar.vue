<template>
	<view class="search-bar">
		<view class="search-input-box">
			<text class="cuIcon-search"></text>
			<input
				class="search-input"
				type="text"
				:value="keyword"
				placeholder="搜索景点"
				confirm-type="search"
				@input="onInput"
				@confirm="onSearch"
			/>
			<text class="cuIcon-close" v-if="keyword" @tap="onClear"></text>
		</view>
		<view class="filter-btn" @tap="onFilter">
			<text class="cuIcon-filter"></text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'SearchBar',
		props: {
			keyword: {
				type: String,
				default: ''
			}
		},
		methods: {
			onInput(e) {
				this.$emit('input', e.detail.value);
			},
			onSearch() {
				this.$emit('search');
			},
			onClear() {
				this.$emit('clear');
			},
			onFilter() {
				this.$emit('filter');
			}
		}
	}
</script>

<style>
	/* 搜索栏 */
	.search-bar {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		position: relative;
		z-index: 10;
	}
	
	.search-input-box {
		flex: 1;
		height: 70rpx;
		background-color: #f5f5f5;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
	}
	
	.search-input-box .cuIcon-search {
		font-size: 36rpx;
		color: #999999;
		margin-right: 10rpx;
	}
	
	.search-input-box .cuIcon-close {
		font-size: 32rpx;
		color: #999999;
		padding: 10rpx;
	}
	
	.search-input {
		flex: 1;
		height: 70rpx;
		font-size: 28rpx;
	}
	
	.filter-btn {
		width: 70rpx;
		height: 70rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
	}
	
	.filter-btn .cuIcon-filter {
		font-size: 40rpx;
		color: #333333;
	}
</style>
