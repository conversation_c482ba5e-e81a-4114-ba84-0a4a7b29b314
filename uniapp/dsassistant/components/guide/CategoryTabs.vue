<template>
	<scroll-view class="category-tabs" scroll-x>
		<view
			class="category-tab"
			v-for="(item, index) in categories"
			:key="index"
			:class="{ active: activeCategory === item.value }"
			@tap="onCategoryTap(item.value)"
		>
			<text>{{ item.label }}</text>
		</view>
	</scroll-view>
</template>

<script>
	export default {
		name: 'CategoryTabs',
		props: {
			categories: {
				type: Array,
				default: () => []
			},
			activeCategory: {
				type: String,
				default: 'all'
			}
		},
		methods: {
			onCategoryTap(category) {
				this.$emit('category-change', category);
			}
		}
	}
</script>

<style>
	/* 分类标签 */
	.category-tabs {
		white-space: nowrap;
		padding: 15rpx 30rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		flex-shrink: 0; /* 防止被压缩 */
		height: 90rpx; /* 固定高度 */
		box-sizing: border-box;
	}

	.category-tab {
		display: inline-block;
		padding: 12rpx 30rpx;
		margin-right: 20rpx;
		font-size: 28rpx;
		color: #666666;
		border-radius: 30rpx;
		background-color: #f5f5f5;
		transition: all 0.3s;
	}

	.category-tab.active {
		background-color: #0081ff;
		color: #ffffff;
		font-weight: 500;
	}
</style>
