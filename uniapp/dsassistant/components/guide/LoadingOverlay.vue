<template>
	<view class="loading-overlay" v-if="show">
		<view class="loading-content">
			<text class="cuIcon-loading2 loading-icon"></text>
			<text>{{text}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'LoadingOverlay',
		props: {
			show: {
				type: <PERSON><PERSON>an,
				default: false
			},
			text: {
				type: String,
				default: '加载中...'
			}
		}
	}
</script>

<style>
	/* 全局加载指示器 */
	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}
	
	.loading-content {
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 12rpx;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.loading-content text {
		color: #ffffff;
		font-size: 26rpx;
		margin-top: 20rpx;
	}
	
	.loading-icon {
		font-size: 50rpx;
		color: #ffffff;
		animation: rotate 1s linear infinite;
	}
	
	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
