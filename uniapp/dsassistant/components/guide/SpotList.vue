<template>
	<scroll-view
		class="spot-list"
		scroll-y
		@scrolltolower="onLoadMore"
		@refresherrefresh="onRefresh"
		@scroll="onScroll"
		refresher-enabled
		:refresher-triggered="refreshing"
		:scroll-top="scrollTop"
		:scroll-with-animation="true"
	>
		<spot-item
			v-for="(item, index) in spots"
			:key="index"
			:spot="item"
			:spot-types="spotTypes"
			@spot-tap="onSpotTap"
			@detail-tap="onDetailTap"
			@nav-tap="onNavTap"
		></spot-item>

		<!-- 加载更多 -->
		<view class="loading-more" v-if="hasMore && spots.length > 0">
			<text class="cuIcon-loading2 loading-icon"></text>
			<text>加载更多...</text>
		</view>

		<!-- 无数据提示 -->
		<view class="empty-tip" v-if="spots.length === 0 && !loading">
			<image src="/static/images/empty.png" mode="aspectFit"></image>
			<text>没有找到符合条件的景点</text>
			<button class="refresh-btn" @tap="onResetFilter">重置筛选</button>
		</view>
	</scroll-view>

	<!-- 返回顶部按钮 -->
	<view class="back-to-top" v-if="showBackToTop" @tap="scrollToTop">
		<text class="cuIcon-top"></text>
	</view>
</template>

<script>
	import SpotItem from './SpotItem.vue';

	export default {
		name: 'SpotList',
		components: {
			SpotItem
		},
		props: {
			spots: {
				type: Array,
				default: () => []
			},
			spotTypes: {
				type: Array,
				default: () => []
			},
			hasMore: {
				type: Boolean,
				default: false
			},
			loading: {
				type: Boolean,
				default: false
			},
			refreshing: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				scrollTop: 0,
				showBackToTop: false,
				scrollThreshold: 300, // 滚动多少距离显示返回顶部按钮
				scrollTimer: null
			}
		},
		methods: {
			onLoadMore() {
				this.$emit('load-more');
			},
			onRefresh() {
				this.$emit('refresh');
			},
			onSpotTap(spot) {
				this.$emit('spot-tap', spot);
			},
			onDetailTap(id) {
				this.$emit('detail-tap', id);
			},
			onNavTap(spot) {
				this.$emit('nav-tap', spot);
			},
			onResetFilter() {
				this.$emit('reset-filter');
			},
			// 滚动事件处理
			onScroll(e) {
				// 防抖处理
				if (this.scrollTimer) {
					clearTimeout(this.scrollTimer);
				}

				this.scrollTimer = setTimeout(() => {
					const scrollTop = e.detail.scrollTop;
					this.showBackToTop = scrollTop > this.scrollThreshold;
				}, 100);
			},
			// 滚动到顶部
			scrollToTop() {
				this.scrollTop = 0;
				// 300ms后重置，以便下次仍然可以触发滚动到顶部
				setTimeout(() => {
					this.scrollTop = -1;
				}, 300);
			}
		}
	}
</script>

<style>
	/* 景点列表 */
	.spot-list {
		flex: 1;
		padding: 20rpx;
		height: 100% !important; /* 强制设置高度为100% */
		box-sizing: border-box;
		overflow-y: auto; /* 确保可以垂直滚动 */
	}

	/* 加载更多 */
	.loading-more {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30rpx 0;
		font-size: 24rpx;
		color: #999999;
	}

	.loading-icon {
		font-size: 28rpx;
		margin-right: 10rpx;
		animation: rotate 1s linear infinite;
	}

	/* 空数据提示 */
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}

	.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.5;
	}

	.empty-tip text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 30rpx;
	}

	.refresh-btn {
		background-color: #0081ff;
		color: #ffffff;
		font-size: 26rpx;
		padding: 10rpx 40rpx;
		border-radius: 30rpx;
		border: none;
	}

	.refresh-btn::after {
		border: none;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 返回顶部按钮 */
	.back-to-top {
		position: fixed;
		right: 30rpx;
		bottom: 120rpx;
		width: 80rpx;
		height: 80rpx;
		background-color: rgba(0, 129, 255, 0.8);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
		z-index: 99;
		transition: all 0.3s;
	}

	.back-to-top text {
		font-size: 36rpx;
		color: #ffffff;
	}
</style>
