<template>
	<view class="filter-panel" v-if="show">
		<view class="filter-header">
			<text>筛选</text>
			<text class="cuIcon-close" @tap="onClose"></text>
		</view>
		<view class="filter-content">
			<view class="filter-section">
				<view class="filter-title">景点类型</view>
				<view class="filter-options">
					<view
						class="filter-option"
						v-for="(item, index) in spotTypes"
						:key="index"
						:class="{ active: selectedType === item.value }"
						@tap="onSelectType(item.value)"
					>
						<text>{{ item.label }}</text>
					</view>
				</view>
			</view>
			<view class="filter-section">
				<view class="filter-title">距离排序</view>
				<view class="filter-options">
					<view
						class="filter-option"
						:class="{ active: sortBy === 'distance' }"
						@tap="onSelectSort('distance')"
					>
						<text>距离最近</text>
					</view>
					<view
						class="filter-option"
						:class="{ active: sortBy === 'popularity' }"
						@tap="onSelectSort('popularity')"
					>
						<text>人气最高</text>
					</view>
				</view>
			</view>
		</view>
		<view class="filter-footer">
			<button class="filter-btn reset-btn" @tap="onReset">重置</button>
			<button class="filter-btn apply-btn" @tap="onApply">应用</button>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'FilterPanel',
		props: {
			show: {
				type: Boolean,
				default: false
			},
			spotTypes: {
				type: Array,
				default: () => []
			},
			selectedType: {
				type: String,
				default: 'all'
			},
			sortBy: {
				type: String,
				default: 'distance'
			}
		},
		data() {
			return {
				localSelectedType: this.selectedType,
				localSortBy: this.sortBy
			}
		},
		watch: {
			selectedType(val) {
				this.localSelectedType = val;
			},
			sortBy(val) {
				this.localSortBy = val;
			}
		},
		methods: {
			onSelectType(type) {
				this.localSelectedType = type;
				this.$emit('update:selectedType', type);
			},
			onSelectSort(sort) {
				this.localSortBy = sort;
				this.$emit('update:sortBy', sort);
			},
			onReset() {
				this.$emit('reset');
			},
			onApply() {
				this.$emit('apply');
			},
			onClose() {
				this.$emit('close');
			}
		}
	}
</script>

<style>
	/* 筛选面板 */
	.filter-panel {
		position: absolute;
		top: 110rpx;
		right: 0;
		width: 80%;
		background-color: #ffffff;
		border-radius: 12rpx 0 0 12rpx;
		box-shadow: -4rpx 4rpx 20rpx rgba(0, 0, 0, 0.1);
		z-index: 100;
		animation: slideIn 0.3s ease;
	}
	
	.filter-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.filter-header text:first-child {
		font-size: 32rpx;
		font-weight: 500;
	}
	
	.filter-header .cuIcon-close {
		font-size: 36rpx;
		color: #999999;
		padding: 10rpx;
	}
	
	.filter-content {
		padding: 20rpx 30rpx;
	}
	
	.filter-section {
		margin-bottom: 30rpx;
	}
	
	.filter-title {
		font-size: 28rpx;
		font-weight: 500;
		margin-bottom: 20rpx;
	}
	
	.filter-options {
		display: flex;
		flex-wrap: wrap;
	}
	
	.filter-option {
		padding: 12rpx 24rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		font-size: 26rpx;
		color: #666666;
	}
	
	.filter-option.active {
		background-color: #0081ff;
		color: #ffffff;
	}
	
	.filter-footer {
		display: flex;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #f0f0f0;
	}
	
	.filter-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		margin: 0 10rpx;
	}
	
	.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
	}
	
	.apply-btn {
		background-color: #0081ff;
		color: #ffffff;
	}
	
	@keyframes slideIn {
		from {
			transform: translateX(100%);
		}
		to {
			transform: translateX(0);
		}
	}
</style>
