<template>
	<view class="spot-item" @tap="onSpotTap">
		<image
			class="spot-image"
			:src="spot.cover || '/static/images/no-image.jpg'"
			mode="aspectFill"
		></image>
		<view class="spot-tag" v-if="spot.type">{{ typeLabel }}</view>
		<view class="spot-info">
			<view class="spot-name-row">
				<view class="spot-name">{{spot.name}}</view>
				<view class="spot-distance" v-if="spot.distance">
					<text class="cuIcon-location"></text>
					<text>{{formatDistance(spot.distance)}}</text>
				</view>
			</view>
			<view class="spot-desc">{{spot.description || '暂无描述'}}</view>
			<view class="spot-meta">
				<view class="spot-meta-item">
					<text class="cuIcon-time"></text>
					<text>{{spot.opening_hours || '暂无开放时间'}}</text>
				</view>
				<view class="spot-meta-item">
					<text class="cuIcon-ticket"></text>
					<text>{{spot.ticket_price || '暂无票价信息'}}</text>
				</view>
			</view>
			<view class="spot-actions">
				<button class="spot-btn detail-btn" @tap.stop="onDetailTap">
					<text class="cuIcon-info"></text>
					<text>详情</text>
				</button>
				<button class="spot-btn nav-btn" @tap.stop="onNavTap">
					<text class="cuIcon-location"></text>
					<text>导航</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'SpotItem',
		props: {
			spot: {
				type: Object,
				required: true
			},
			spotTypes: {
				type: Array,
				default: () => []
			}
		},
		computed: {
			typeLabel() {
				const typeObj = this.spotTypes.find(item => item.value === this.spot.type);
				return typeObj ? typeObj.label : '';
			}
		},
		methods: {
			onSpotTap() {
				this.$emit('spot-tap', this.spot);
			},
			onDetailTap() {
				this.$emit('detail-tap', this.spot.id);
			},
			onNavTap() {
				this.$emit('nav-tap', this.spot);
			},
			formatDistance(distance) {
				if (distance < 1000) {
					return Math.round(distance) + 'm';
				} else {
					return (distance / 1000).toFixed(1) + 'km';
				}
			}
		}
	}
</script>

<style>
	.spot-item {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
		position: relative;
	}
	
	.spot-image {
		width: 100%;
		height: 300rpx;
	}
	
	.spot-tag {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		background-color: rgba(0, 0, 0, 0.6);
		color: #ffffff;
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		border-radius: 20rpx;
	}
	
	.spot-info {
		padding: 20rpx;
	}
	
	.spot-name-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.spot-name {
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		flex: 1;
	}
	
	.spot-distance {
		font-size: 24rpx;
		color: #0081ff;
		display: flex;
		align-items: center;
	}
	
	.spot-distance text:first-child {
		margin-right: 6rpx;
	}
	
	.spot-desc {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 16rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		line-height: 1.5;
	}
	
	.spot-meta {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 20rpx;
	}
	
	.spot-meta-item {
		font-size: 24rpx;
		color: #999999;
		margin-right: 30rpx;
		margin-bottom: 10rpx;
		display: flex;
		align-items: center;
	}
	
	.spot-meta-item text:first-child {
		margin-right: 8rpx;
	}
	
	.spot-actions {
		display: flex;
		justify-content: flex-end;
		border-top: 1rpx solid #f5f5f5;
		padding-top: 20rpx;
	}
	
	.spot-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 30rpx;
		height: 70rpx;
		border-radius: 35rpx;
		font-size: 26rpx;
		margin-left: 20rpx;
		border: none;
	}
	
	.spot-btn::after {
		border: none;
	}
	
	.spot-btn text {
		margin-right: 6rpx;
	}
	
	.detail-btn {
		background-color: #f5f5f5;
		color: #666666;
	}
	
	.nav-btn {
		background-color: #0081ff;
		color: #ffffff;
	}
</style>
