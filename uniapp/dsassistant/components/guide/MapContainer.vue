<template>
	<view class="map-container" :style="{ height: mapHeight }">
		<map
			id="map"
			class="map"
			:latitude="Number(latitude)"
			:longitude="Number(longitude)"
			:markers="markers"
			:scale="scale"
			:show-location="true"
			:enable-rotate="true"
			:enable-overlooking="true"
			:enable-3D="true"
			@markertap="onMarkerTap"
			@callouttap="onCalloutTap"
			@regionchange="onMapRegionChange"
		></map>

		<!-- 地图控制按钮 -->
		<view class="map-controls">
			<view class="map-control-btn" @tap="onZoomIn">
				<text class="cuIcon-add"></text>
			</view>
			<view class="map-control-btn" @tap="onZoomOut">
				<text class="cuIcon-move"></text>
			</view>
			<view class="map-control-btn" @tap="onResetMapView">
				<text class="cuIcon-refresh"></text>
			</view>
			<view class="map-control-btn" @tap="onLocateUser">
				<text class="cuIcon-location"></text>
			</view>
			<!-- 退出全屏按钮 - 仅在全屏模式显示 -->
			<view class="map-control-btn exit-fullscreen-btn" v-if="isFullscreen" @tap="onExitFullscreen">
				<text class="cuIcon-back"></text>
			</view>
		</view>

		<!-- 地图/列表切换按钮 - 仅在非全屏模式显示 -->
		<view class="view-toggle-btn" v-if="!isFullscreen" @tap="onEnterFullscreen">
			<text>全屏地图</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'MapContainer',
		props: {
			latitude: {
				type: [Number, String],
				required: true,
				validator: function(value) {
					// 确保可以转换为数字
					return !isNaN(Number(value));
				}
			},
			longitude: {
				type: [Number, String],
				required: true,
				validator: function(value) {
					// 确保可以转换为数字
					return !isNaN(Number(value));
				}
			},
			scale: {
				type: Number,
				default: 15
			},
			markers: {
				type: Array,
				default: () => []
			},
			isFullscreen: {
				type: Boolean,
				default: false
			},
			mapHeight: {
				type: String,
				default: '400rpx'
			}
		},
		data() {
			return {
				mapContext: null
			}
		},
		mounted() {
			// 获取地图上下文
			this.mapContext = uni.createMapContext('map');
		},
		methods: {
			onMarkerTap(e) {
				this.$emit('marker-tap', e);
			},
			onCalloutTap(e) {
				this.$emit('callout-tap', e);
			},
			onMapRegionChange(e) {
				this.$emit('region-change', e);
			},
			onZoomIn() {
				this.$emit('zoom-in');
			},
			onZoomOut() {
				this.$emit('zoom-out');
			},
			onResetMapView() {
				this.$emit('reset-view');
			},
			onLocateUser() {
				this.$emit('locate-user');
			},
			onEnterFullscreen() {
				this.$emit('enter-fullscreen');
			},
			onExitFullscreen() {
				this.$emit('exit-fullscreen');
			}
		}
	}
</script>

<style>
	/* 地图容器 */
	.map-container {
		width: 100%;
		position: relative;
		transition: height 0.3s ease;
	}

	.map {
		width: 100%;
		height: 100%;
	}

	/* 地图控制按钮 */
	.map-controls {
		position: absolute;
		right: 30rpx;
		bottom: 120rpx;
		display: flex;
		flex-direction: column;
	}

	.map-control-btn {
		width: 80rpx;
		height: 80rpx;
		background-color: #ffffff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.map-control-btn text {
		font-size: 36rpx;
		color: #333333;
	}

	/* 退出全屏按钮 */
	.exit-fullscreen-btn {
		background-color: #0081ff;
	}

	.exit-fullscreen-btn text {
		color: #ffffff;
	}

	/* 视图切换按钮 */
	.view-toggle-btn {
		position: absolute;
		left: 50%;
		bottom: 30rpx;
		transform: translateX(-50%);
		background-color: #ffffff;
		border-radius: 30rpx;
		padding: 15rpx 30rpx;
		font-size: 26rpx;
		color: #333333;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
</style>
