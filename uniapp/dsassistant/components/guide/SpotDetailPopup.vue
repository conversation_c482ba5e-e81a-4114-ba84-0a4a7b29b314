<template>
	<view class="spot-detail-popup" v-if="show">
		<view class="popup-mask" @tap="onClose"></view>
		<view class="popup-content">
			<view class="popup-header">
				<text class="popup-title">{{spot.name}}</text>
				<text class="cuIcon-close popup-close" @tap="onClose"></text>
			</view>
			<scroll-view class="popup-body" scroll-y>
				<image
					class="popup-image"
					:src="spot.cover || '/static/images/no-image.jpg'"
					mode="aspectFill"
				></image>
				<view class="popup-info">
					<view class="popup-section">
						<view class="popup-section-title">
							<text class="cuIcon-info"></text>
							<text>景点简介</text>
						</view>
						<view class="popup-section-content">
							<text>{{spot.description || '暂无描述'}}</text>
						</view>
					</view>
					<view class="popup-section">
						<view class="popup-section-title">
							<text class="cuIcon-time"></text>
							<text>开放时间</text>
						</view>
						<view class="popup-section-content">
							<text>{{spot.opening_hours || '暂无开放时间信息'}}</text>
						</view>
					</view>
					<view class="popup-section">
						<view class="popup-section-title">
							<text class="cuIcon-ticket"></text>
							<text>票价信息</text>
						</view>
						<view class="popup-section-content">
							<text>{{spot.ticket_price || '暂无票价信息'}}</text>
						</view>
					</view>
					<view class="popup-section" v-if="spot.address">
						<view class="popup-section-title">
							<text class="cuIcon-location"></text>
							<text>地址</text>
						</view>
						<view class="popup-section-content">
							<text>{{spot.address}}</text>
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="popup-footer">
				<button class="popup-btn detail-btn" @tap="onDetailTap">
					<text class="cuIcon-info"></text>
					<text>查看详情</text>
				</button>
				<button class="popup-btn nav-btn" @tap="onNavTap">
					<text class="cuIcon-location"></text>
					<text>导航前往</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'SpotDetailPopup',
		props: {
			show: {
				type: Boolean,
				default: false
			},
			spot: {
				type: Object,
				default: () => ({})
			}
		},
		methods: {
			onClose() {
				this.$emit('close');
			},
			onDetailTap() {
				this.$emit('detail-tap', this.spot.id);
			},
			onNavTap() {
				this.$emit('nav-tap', this.spot);
			}
		}
	}
</script>

<style>
	/* 景点详情弹窗 */
	.spot-detail-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1000;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.popup-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
	}
	
	.popup-content {
		position: relative;
		width: 90%;
		max-height: 80vh;
		background-color: #ffffff;
		border-radius: 16rpx;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		animation: popIn 0.3s ease;
	}
	
	.popup-header {
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.popup-title {
		font-size: 32rpx;
		font-weight: 500;
	}
	
	.popup-close {
		font-size: 36rpx;
		color: #999999;
		padding: 10rpx;
	}
	
	.popup-body {
		flex: 1;
		max-height: 60vh;
	}
	
	.popup-image {
		width: 100%;
		height: 400rpx;
	}
	
	.popup-info {
		padding: 30rpx;
	}
	
	.popup-section {
		margin-bottom: 30rpx;
	}
	
	.popup-section-title {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
		margin-bottom: 16rpx;
	}
	
	.popup-section-title text:first-child {
		margin-right: 10rpx;
		color: #0081ff;
	}
	
	.popup-section-content {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.6;
	}
	
	.popup-footer {
		display: flex;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #f0f0f0;
	}
	
	.popup-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		margin: 0 10rpx;
		border: none;
	}
	
	.popup-btn::after {
		border: none;
	}
	
	.popup-btn text {
		margin-right: 6rpx;
	}
	
	.detail-btn {
		background-color: #f5f5f5;
		color: #666666;
	}
	
	.nav-btn {
		background-color: #0081ff;
		color: #ffffff;
	}
	
	@keyframes popIn {
		from {
			transform: scale(0.9);
			opacity: 0;
		}
		to {
			transform: scale(1);
			opacity: 1;
		}
	}
</style>
