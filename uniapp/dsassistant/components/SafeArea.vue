<template>
	<view class="safe-area" :style="{ height: safeAreaHeight + 'px' }"></view>
</template>

<script>
	export default {
		name: 'SafeArea',
		data() {
			return {
				safeAreaHeight: 0
			}
		},
		created() {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			
			// 计算底部安全区域高度
			if (systemInfo.safeAreaInsets) {
				// 优先使用 safeAreaInsets（更准确）
				this.safeAreaHeight = systemInfo.safeAreaInsets.bottom;
			} else if (systemInfo.safeArea) {
				// 兼容方式：通过安全区域和屏幕高度计算
				const safeAreaBottom = systemInfo.safeArea.bottom;
				const screenHeight = systemInfo.screenHeight;
				this.safeAreaHeight = screenHeight - safeAreaBottom;
			} else {
				// 默认值，适用于大多数非全面屏设备
				this.safeAreaHeight = 0;
			}
			
			// 如果是 iOS 且高度小于某个值，设置最小高度
			if (systemInfo.platform === 'ios' && this.safeAreaHeight < 20) {
				this.safeAreaHeight = 20;
			}
			
			// 调试信息
			console.log('SafeArea height:', this.safeAreaHeight);
		}
	}
</script>

<style>
	.safe-area {
		width: 100%;
		background-color: transparent;
	}
</style>
