<template>
	<view class="chat-input-container safe-area-bottom">
		<view class="chat-input-wrapper">
			<input
				class="chat-input"
				type="text"
				v-model="inputValue"
				:placeholder="placeholder"
				confirm-type="send"
				@confirm="sendMessage"
				:adjust-position="false"
				:cursor-spacing="20"
			/>
			<view
				class="chat-send-btn"
				:class="inputValue.trim() ? 'active' : ''"
				@click="sendMessage"
			>
				<text class="cuIcon-send"></text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ChatInput',
		props: {
			placeholder: {
				type: String,
				default: '请输入您的问题...'
			}
		},
		data() {
			return {
				inputValue: ''
			}
		},
		methods: {
			sendMessage() {
				if (!this.inputValue.trim()) return;
				
				// 发送消息事件
				this.$emit('send', this.inputValue);
				
				// 清空输入框
				this.inputValue = '';
			}
		}
	}
</script>

<style>
	/* 输入区域 */
	.chat-input-container {
		background-color: #fff;
		border-top: 1rpx solid #eee;
		padding: 20rpx 30rpx;
		position: relative;
	}

	.chat-input-wrapper {
		display: flex;
		align-items: center;
	}

	.chat-input {
		flex: 1;
		height: 80rpx;
		background-color: #f5f7fa;
		border-radius: 40rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
	}

	.chat-send-btn {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
		transition: all 0.3s;
	}

	.chat-send-btn.active {
		background-color: #0081ff;
	}

	.chat-send-btn text {
		font-size: 40rpx;
		color: #fff;
	}
	
	/* 安全区域适配 */
	.safe-area-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>
