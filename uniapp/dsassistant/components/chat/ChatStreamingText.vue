<template>
	<view>
		<rich-text class="markdown-content" :nodes="parsedText"></rich-text>
		<view class="typing-cursor" v-if="isStreaming"></view>
	</view>
</template>

<script>
	import {marked} from 'marked';
	
	export default {
		name: 'ChatStreamingText',
		props: {
			text: {
				type: String,
				default: ''
			},
			fullText: {
				type: String,
				default: ''
			},
			isStreaming: {
				type: Boolean,
				default: false
			},
			autoStart: {
				type: Boolean,
				default: true
			},
			baseDelay: {
				type: Number,
				default: 30
			}
		},
		data() {
			return {
				currentText: '',
				parsedText: '',
				streaming: false,
				charIndex: 0,
				chars: []
			}
		},
		watch: {
			fullText: {
				immediate: true,
				handler(newVal) {
					if (newVal && this.autoStart) {
						this.startStreaming();
					}
				}
			},
			text: {
				immediate: true,
				handler(newVal) {
					if (!this.streaming) {
						this.currentText = newVal;
						this.parsedText = this.parseMarkdown(newVal);
					}
				}
			}
		},
		methods: {
			startStreaming() {
				if (!this.fullText || this.streaming) return;
				
				this.streaming = true;
				this.$emit('update:isStreaming', true);
				
				// 重置状态
				this.currentText = '';
				this.charIndex = 0;
				this.chars = this.fullText.split('');
				
				// 开始流式显示
				this.processNextChar();
			},
			
			stopStreaming() {
				this.streaming = false;
				this.$emit('update:isStreaming', false);
				
				// 直接显示完整文本
				this.currentText = this.fullText;
				this.parsedText = this.parseMarkdown(this.fullText);
				this.$emit('update:text', this.fullText);
			},
			
			// 处理下一个字符
			processNextChar() {
				// 如果已经处理完所有字符或停止流式显示，结束处理
				if (this.charIndex >= this.chars.length || !this.streaming) {
					this.stopStreaming();
					return;
				}
				
				// 当前字符
				const currentChar = this.chars[this.charIndex];
				
				// 添加当前字符
				this.currentText += currentChar;
				this.charIndex++;
				
				// 更新解析后的文本
				this.parsedText = this.parseMarkdown(this.currentText);
				
				// 发出更新事件
				this.$emit('update:text', this.currentText);
				
				// 发出进度事件
				this.$emit('progress', {
					current: this.charIndex,
					total: this.chars.length,
					percent: Math.floor((this.charIndex / this.chars.length) * 100)
				});
				
				// 计算下一个字符的延迟
				let nextDelay = this.baseDelay;
				
				// 标点符号后面增加延迟，模拟自然停顿
				if (['.', '。', '!', '！', '?', '？'].includes(currentChar)) {
					nextDelay = this.baseDelay * 4; // 句号等主要标点后面停顿更长
				} else if ([',', '，', ';', '；', ':', '：'].includes(currentChar)) {
					nextDelay = this.baseDelay * 2; // 逗号等次要标点后面停顿较短
				}
				
				// 安排处理下一个字符
				setTimeout(this.processNextChar, nextDelay);
			},
			
			// 解析Markdown
			parseMarkdown(text) {
				if (!text) return '';
				
				try {
					// 使用marked.js解析Markdown
					let html = marked(text);
					
					// 处理HTML以适应小程序rich-text组件
					// 为HTML元素添加类名，以便应用样式
					html = html
						.replace(/<h1/g, '<h1 class="markdown-h1"')
						.replace(/<h2/g, '<h2 class="markdown-h2"')
						.replace(/<h3/g, '<h3 class="markdown-h3"')
						.replace(/<h4/g, '<h4 class="markdown-h4"')
						.replace(/<h5/g, '<h5 class="markdown-h5"')
						.replace(/<h6/g, '<h6 class="markdown-h6"')
						.replace(/<p/g, '<p class="markdown-p"')
						.replace(/<ul/g, '<ul class="markdown-ul"')
						.replace(/<ol/g, '<ol class="markdown-ol"')
						.replace(/<li/g, '<li class="markdown-li"')
						.replace(/<blockquote/g, '<blockquote class="markdown-blockquote"')
						.replace(/<pre/g, '<pre class="markdown-pre"')
						.replace(/<code/g, '<code class="markdown-code"')
						.replace(/<a/g, '<a class="markdown-a"')
						.replace(/<img/g, '<img class="markdown-img"')
						.replace(/<table/g, '<table class="markdown-table"')
						.replace(/<th/g, '<th class="markdown-th"')
						.replace(/<td/g, '<td class="markdown-td"');
					
					return html;
				} catch (error) {
					console.error('Markdown解析错误:', error);
					return text;
				}
			}
		}
	}
</script>

<style>
	/* 打字光标 */
	.typing-cursor {
		display: inline-block;
		width: 2rpx;
		height: 24rpx;
		background-color: #333;
		margin-left: 4rpx;
		vertical-align: middle;
		animation: cursor-blink 0.8s infinite;
	}
	
	@keyframes cursor-blink {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: 0;
		}
	}
	
	/* Markdown内容样式 */
	.markdown-content {
		font-size: 28rpx;
		line-height: 1.6;
		display: inline;
	}
</style>
