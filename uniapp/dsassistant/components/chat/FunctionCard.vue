<template>
  <view class="function-card" @tap="handleCardTap">
    <view class="card-icon">
      <image :src="iconSrc" mode="aspectFit"></image>
    </view>
    <view class="card-content">
      <view class="card-title">{{ title }}</view>
      <view class="card-description">{{ description }}</view>
    </view>
    <view class="card-action">
      <text>{{ actionText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FunctionCard',
  props: {
    functionName: {
      type: String,
      required: true
    },
    parameters: {
      type: Object,
      required: true
    }
  },
  computed: {
    iconSrc() {
      const icons = {
        'showOnMap': '/static/images/map-icon.svg',
        'showScenicSpotDetails': '/static/images/info-icon.svg'
      };
      return icons[this.functionName] || '/static/images/default-icon.svg';
    },
    title() {
      if (this.functionName === 'showOnMap') {
        return `${this.parameters.spotName}的位置`;
      } else if (this.functionName === 'showScenicSpotDetails') {
        return `${this.parameters.spotName}详情`;
      }
      return '查看详情';
    },
    description() {
      if (this.functionName === 'showOnMap') {
        return '点击查看地图位置';
      } else if (this.functionName === 'showScenicSpotDetails') {
        return '点击查看景点详细信息';
      }
      return '点击查看';
    },
    actionText() {
      const actions = {
        'showOnMap': '查看地图',
        'showScenicSpotDetails': '查看详情'
      };
      return actions[this.functionName] || '查看';
    }
  },
  methods: {
    handleCardTap() {
      if (this.functionName === 'showOnMap') {
        uni.navigateTo({
          url: `/pages/map/map?spotId=${this.parameters.spotId || ''}&spotName=${this.parameters.spotName || ''}&latitude=${this.parameters.latitude || ''}&longitude=${this.parameters.longitude || ''}`
        });
      } else if (this.functionName === 'showScenicSpotDetails') {
        uni.navigateTo({
          url: `/pages/scenic/detail?spotId=${this.parameters.spotId || ''}&spotName=${this.parameters.spotName || ''}`
        });
      }
    }
  }
}
</script>

<style>
.function-card {
  margin-top: 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background-color: #e6f7ff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.card-icon image {
  width: 48rpx;
  height: 48rpx;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.card-description {
  font-size: 24rpx;
  color: #666;
}

.card-action {
  padding: 10rpx 20rpx;
  background-color: #0081ff;
  border-radius: 30rpx;
}

.card-action text {
  font-size: 24rpx;
  color: #fff;
}
</style>
