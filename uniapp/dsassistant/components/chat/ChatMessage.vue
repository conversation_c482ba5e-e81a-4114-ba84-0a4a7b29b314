<template>
	<view class="chat-item" :class="isUser ? 'chat-right' : 'chat-left'">
		<!-- 助手头像 -->
		<view class="chat-avatar" v-if="!isUser">
			<image src="/static/images/assistant-avatar.svg" mode="aspectFill"></image>
		</view>

		<!-- 消息内容 -->
		<view class="chat-content">
			<!-- 用户消息 -->
			<view class="chat-bubble user-bubble" v-if="isUser">
				<text>{{message}}</text>
			</view>

			<!-- 助手消息 -->
			<view class="chat-bubble assistant-bubble" v-else>
				<!-- 如果有解析后的消息，则显示解析后的消息 -->
				<rich-text v-if="parsedMessage" class="markdown-content" :nodes="parsedMessage"></rich-text>
				<!-- 如果没有解析后的消息，则显示原始消息 -->
				<text v-else>{{message}}</text>

				<!-- 函数调用卡片 -->
				<view v-if="functionCalls && functionCalls.length > 0" class="function-cards">
					<function-card
						v-for="(call, index) in functionCalls"
						:key="index"
						:function-name="call.name"
						:parameters="call.parameters"
					></function-card>
				</view>
			</view>

			<!-- 消息时间 -->
			<view class="chat-time">{{time}}</view>
		</view>

		<!-- 用户头像 -->
		<view class="chat-avatar" v-if="isUser">
			<image src="/static/images/user-avatar.svg" mode="aspectFill"></image>
		</view>
	</view>
</template>

<script>
	import FunctionCard from './FunctionCard.vue';

	export default {
		name: 'ChatMessage',
		components: {
			FunctionCard
		},
		props: {
			isUser: {
				type: Boolean,
				default: false
			},
			message: {
				type: String,
				default: ''
			},
			parsedMessage: {
				type: String,
				default: ''
			},
			time: {
				type: String,
				default: ''
			},
			functionCalls: {
				type: Array,
				default: () => []
			}
		}
	}
</script>

<style>
	/* 聊天项 */
	.chat-item {
		display: flex;
		margin-bottom: 30rpx;
		position: relative;
	}

	.chat-left {
		justify-content: flex-start;
	}

	.chat-right {
		justify-content: flex-end;
	}

	/* 头像 */
	.chat-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		overflow: hidden;
		background-color: #f0f0f0;
		flex-shrink: 0;
	}

	.chat-left .chat-avatar {
		margin-right: 20rpx;
	}

	.chat-right .chat-avatar {
		margin-left: 20rpx;
	}

	.chat-avatar image {
		width: 100%;
		height: 100%;
	}

	/* 消息内容 */
	.chat-content {
		max-width: 70%;
		display: flex;
		flex-direction: column;
	}

	/* 气泡 */
	.chat-bubble {
		padding: 20rpx;
		border-radius: 18rpx;
		position: relative;
		word-break: break-all;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.user-bubble {
		background-color: #0081ff;
		color: #fff;
		border-top-right-radius: 4rpx;
	}

	.assistant-bubble {
		background-color: #fff;
		color: #333;
		border-top-left-radius: 4rpx;
	}

	/* 时间 */
	.chat-time {
		font-size: 22rpx;
		color: #999;
		margin-top: 10rpx;
		padding: 0 10rpx;
	}

	.chat-left .chat-time {
		text-align: left;
	}

	.chat-right .chat-time {
		text-align: right;
	}



	/* Markdown内容样式 */
	.markdown-content {
		font-size: 28rpx;
		line-height: 1.6;
		display: inline;
	}

	/* 函数调用卡片容器 */
	.function-cards {
		margin-top: 20rpx;
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}
</style>
