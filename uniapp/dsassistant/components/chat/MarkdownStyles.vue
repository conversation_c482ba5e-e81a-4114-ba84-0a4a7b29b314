<template>
	<view></view>
</template>

<script>
	export default {
		name: 'MarkdownStyles'
	}
</script>

<style>
	/* Markdown内容样式 */
	.markdown-content {
		font-size: 28rpx;
		line-height: 1.6;
	}

	/* 使用类选择器替代标签选择器 */
	.markdown-h1, .markdown-h2, .markdown-h3,
	.markdown-h4, .markdown-h5, .markdown-h6 {
		font-weight: 600;
		margin: 16rpx 0 8rpx 0;
		line-height: 1.4;
	}

	.markdown-h1 { font-size: 36rpx; }
	.markdown-h2 { font-size: 32rpx; }
	.markdown-h3 { font-size: 30rpx; }
	.markdown-h4, .markdown-h5, .markdown-h6 { font-size: 28rpx; }

	.markdown-p {
		margin-bottom: 12rpx;
	}

	.markdown-ul, .markdown-ol {
		margin: 12rpx 0;
		padding-left: 32rpx;
	}

	.markdown-li {
		margin-bottom: 6rpx;
	}

	.markdown-blockquote {
		border-left: 8rpx solid #e2e8f0;
		padding-left: 16rpx;
		color: #4a5568;
		margin: 12rpx 0;
	}

	.markdown-code {
		background-color: #f7fafc;
		padding: 4rpx 8rpx;
		border-radius: 4rpx;
		font-family: monospace;
		font-size: 24rpx;
	}

	.markdown-pre {
		background-color: #f7fafc;
		padding: 16rpx;
		border-radius: 8rpx;
		overflow-x: auto;
		margin: 12rpx 0;
	}

	.markdown-pre .markdown-code {
		background-color: transparent;
		padding: 0;
	}

	.markdown-a {
		color: #0081ff;
		text-decoration: none;
	}

	.markdown-img {
		max-width: 100%;
		height: auto;
		margin: 12rpx 0;
		border-radius: 8rpx;
	}

	.markdown-table {
		width: 100%;
		border-collapse: collapse;
		margin: 12rpx 0;
	}

	.markdown-th, .markdown-td {
		border: 2rpx solid #e2e8f0;
		padding: 8rpx 12rpx;
		text-align: left;
	}

	.markdown-th {
		background-color: #f7fafc;
	}
</style>
