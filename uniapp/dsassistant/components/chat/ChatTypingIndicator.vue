<template>
	<view class="chat-item chat-left" v-if="visible">
		<view class="chat-avatar">
			<image src="/static/images/assistant-avatar.svg" mode="aspectFill"></image>
		</view>
		<view class="chat-content">
			<view class="chat-bubble assistant-bubble typing-bubble">
				<view class="typing-indicator">
					<view class="typing-dot"></view>
					<view class="typing-dot"></view>
					<view class="typing-dot"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ChatTypingIndicator',
		props: {
			visible: {
				type: <PERSON><PERSON><PERSON>,
				default: false
			}
		}
	}
</script>

<style>
	/* 聊天项 */
	.chat-item {
		display: flex;
		margin-bottom: 30rpx;
		position: relative;
	}

	.chat-left {
		justify-content: flex-start;
	}

	/* 头像 */
	.chat-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		overflow: hidden;
		background-color: #f0f0f0;
		flex-shrink: 0;
		margin-right: 20rpx;
	}

	.chat-avatar image {
		width: 100%;
		height: 100%;
	}

	/* 消息内容 */
	.chat-content {
		max-width: 70%;
		display: flex;
		flex-direction: column;
	}

	/* 气泡 */
	.chat-bubble {
		padding: 20rpx;
		border-radius: 18rpx;
		position: relative;
		word-break: break-all;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.assistant-bubble {
		background-color: #fff;
		color: #333;
		border-top-left-radius: 4rpx;
	}

	/* 正在输入指示器 */
	.typing-indicator {
		display: flex;
		align-items: center;
		padding: 10rpx;
	}

	.typing-bubble {
		min-width: 100rpx;
		min-height: 60rpx;
	}

	.typing-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background-color: #ccc;
		margin: 0 6rpx;
		animation: typing 1s infinite;
	}

	.typing-dot:nth-child(1) {
		animation-delay: 0s;
	}

	.typing-dot:nth-child(2) {
		animation-delay: 0.2s;
	}

	.typing-dot:nth-child(3) {
		animation-delay: 0.4s;
	}

	@keyframes typing {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-6rpx);
		}
	}
</style>
