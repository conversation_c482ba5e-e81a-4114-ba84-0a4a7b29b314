<template>
	<view class="container">
		<view class="header">
			<text class="title">底部安全区域示例</text>
		</view>
		
		<view class="content">
			<view class="section">
				<view class="section-title">什么是底部安全区域？</view>
				<view class="section-content">
					<text>底部安全区域是为了适配全面屏手机（如 iPhone X 及以上机型）而设计的。这些设备底部有一个操作条或"刘海"，如果内容或交互元素放在这个区域，可能会被遮挡或难以触摸。</text>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">如何使用安全区域组件？</view>
				<view class="section-content">
					<text>1. 在页面底部添加 &lt;safe-area&gt;&lt;/safe-area&gt; 组件</text>
					<text>2. 对于固定在底部的元素，可以使用 CSS 变量：padding-bottom: env(safe-area-inset-bottom);</text>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">示例代码</view>
				<view class="code-block">
			
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">底部固定元素示例</view>
				<view class="section-content">
					<text>下方的底部栏展示了如何使用 env(safe-area-inset-bottom) 来适配底部安全区域。</text>
				</view>
			</view>
		</view>
		
		<!-- 底部固定栏 -->
		<view class="bottom-bar">
			<view class="bar-item">
				<text class="cuIcon-home"></text>
				<text>首页</text>
			</view>
			<view class="bar-item active">
				<text class="cuIcon-info"></text>
				<text>示例</text>
			</view>
			<view class="bar-item">
				<text class="cuIcon-settings"></text>
				<text>设置</text>
			</view>
		</view>
		
		<!-- 底部安全区域 -->
		<safe-area></safe-area>
	</view>
</template>

<script>
	import safeArea from "@/components/SafeArea.vue"
	export default {
		components: {
			safeArea
		},
		data() {
			return {
				
			}
		},
		methods: {
			
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f8f8f8;
		padding-bottom: 100rpx; /* 为底部栏留出空间 */
	}
	
	.header {
		background-color: #0081ff;
		padding: 30rpx;
		color: #ffffff;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
	}
	
	.content {
		padding: 30rpx;
	}
	
	.section {
		margin-bottom: 40rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		color: #333333;
	}
	
	.section-content {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
	}
	
	.section-content text {
		display: block;
		margin-bottom: 10rpx;
	}
	
	.code-block {
		background-color: #f5f5f5;
		padding: 20rpx;
		border-radius: 8rpx;
		font-family: monospace;
		font-size: 24rpx;
		color: #333333;
		line-height: 1.5;
	}
	
	.code-block text {
		display: block;
		white-space: pre;
	}
	
	/* 底部栏 */
	.bottom-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: space-around;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 10;
		padding-bottom: env(safe-area-inset-bottom); /* 适配 iOS 底部安全区域 */
	}
	
	.bar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex: 1;
		height: 100%;
		color: #999999;
		font-size: 24rpx;
	}
	
	.bar-item text:first-child {
		font-size: 40rpx;
		margin-bottom: 4rpx;
	}
	
	.bar-item.active {
		color: #0081ff;
	}
</style>
