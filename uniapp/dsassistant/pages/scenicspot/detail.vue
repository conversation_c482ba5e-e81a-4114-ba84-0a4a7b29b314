<template>
	<view class="container">
		<!-- 自定义导航栏 -->
<!-- 		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-left" @tap="goBack">
				<text class="cuIcon-back"></text>
			</view>
			<view class="navbar-title">景点详情</view>
			<view class="navbar-right">
				<text class="cuIcon-share" @tap="shareSpot"></text>
				<text class="cuIcon-favor" :class="{ 'text-red': isFavorite }" @tap="toggleFavorite"></text>
			</view>
		</view> -->

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<view class="loading-content">
				<text class="cuIcon-loading2 loading-icon"></text>
				<text>加载中...</text>
			</view>
		</view>

		<!-- 错误状态 -->
		<view class="error-container" v-else-if="loadError">
			<image src="/static/images/error.png" mode="aspectFit" class="error-image"></image>
			<text class="error-text">{{errorMsg}}</text>
			<button class="retry-btn" @tap="loadSpotDetail">重新加载</button>
		</view>

		<!-- 内容区域 -->
		<view class="content-container" v-else>
			<!-- 图片轮播 -->
			<view class="swiper-container">
				<swiper class="swiper" indicator-dots autoplay circular :interval="3000" :duration="500" v-if="spot.images && spot.images.length > 0">
					<swiper-item v-for="(item, index) in spot.images" :key="index" @tap="previewImage(index)">
						<image :src="item" mode="aspectFill" class="swiper-image"></image>
					</swiper-item>
				</swiper>
				<image v-else src="/static/images/no-image.jpg" mode="aspectFill" class="swiper-image"></image>

				<!-- 景点类型标签 -->
				<view class="spot-tag" v-if="spot.type">{{getSpotTypeLabel(spot.type)}}</view>
			</view>

			<!-- 景点信息 -->
			<view class="spot-info">
				<!-- 景点名称和评分 -->
				<view class="spot-header">
					<view class="spot-name">{{spot.name}}</view>
					<view class="spot-rating">
						<text class="cuIcon-favorfill text-yellow" v-for="n in Math.floor(spot.rating || 4.5)" :key="n"></text>
						<text class="cuIcon-favor text-yellow" v-if="(spot.rating || 4.5) % 1 > 0"></text>
						<text class="rating-text">{{spot.rating || 4.5}}</text>
					</view>
				</view>

				<!-- 标签列表 -->
				<scroll-view class="tags-scroll" scroll-x v-if="spot.tags && spot.tags.length">
					<view class="tag-item" v-for="(tag, index) in spot.tags" :key="index">{{tag}}</view>
				</scroll-view>

				<!-- 景点描述 -->
				<view class="spot-desc-container">
					<view class="spot-desc" :class="{ 'expanded': descExpanded }">{{spot.description || '暂无描述'}}</view>
					<view class="expand-btn" @tap="toggleDescExpand" v-if="spot.description && spot.description.length > 100">
						<text>{{ descExpanded ? '收起' : '展开' }}</text>
						<text class="cuIcon-unfold" v-if="!descExpanded"></text>
						<text class="cuIcon-fold" v-else></text>
					</view>
				</view>

				<!-- 基本信息 -->
				<view class="info-section">
					<view class="section-title">
						<text class="cuIcon-info text-blue"></text>
						<text>基本信息</text>
					</view>
					<view class="info-list">
						<view class="info-item">
							<text class="cuIcon-location text-blue"></text>
							<text>地址：{{spot.address || '暂无地址信息'}}</text>
						</view>
						<view class="info-item">
							<text class="cuIcon-time text-blue"></text>
							<text>开放时间：{{spot.opening_hours || '暂无开放时间信息'}}</text>
						</view>
						<view class="info-item">
							<text class="cuIcon-ticket text-blue"></text>
							<text>票价：{{spot.ticket_price || '暂无票价信息'}}</text>
						</view>
						<view class="info-item" v-if="spot.contact">
							<text class="cuIcon-phone text-blue"></text>
							<text>联系电话：{{spot.contact}}</text>
						</view>
					</view>
				</view>

				<!-- 游玩提示 -->
				<view class="info-section" v-if="spot.tips">
					<view class="section-title">
						<text class="cuIcon-notificationfill text-orange"></text>
						<text>游玩提示</text>
					</view>
					<view class="tips-content">{{spot.tips}}</view>
				</view>

				<!-- 位置导航 -->
				<view class="info-section">
					<view class="section-title">
						<text class="cuIcon-locationfill text-green"></text>
						<text>位置导航</text>
					</view>
					<view class="map-container">
						<map id="map" class="map" :latitude="Number(spot.latitude)" :longitude="Number(spot.longitude)" :markers="markers" :scale="16"></map>
					</view>
					<button class="nav-btn" @tap="navigateTo">
						<text class="cuIcon-location"></text>
						<text>导航到这里</text>
					</button>
				</view>

				<!-- 相关推荐 -->
				<view class="info-section" v-if="relatedSpots.length > 0">
					<view class="section-title">
						<text class="cuIcon-appreciatefill text-red"></text>
						<text>相关推荐</text>
					</view>
					<scroll-view class="related-spots-scroll" scroll-x>
						<view class="related-spot-item" v-for="(item, index) in relatedSpots" :key="index" @tap="goToSpotDetail(item.id)">
							<image :src="item.cover || '/static/images/no-image.jpg'" mode="aspectFill" class="related-spot-image"></image>
							<view class="related-spot-name">{{item.name}}</view>
							<view class="related-spot-distance" v-if="item.distance">
								<text class="cuIcon-location"></text>
								<text>{{formatDistance(item.distance)}}</text>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-bar">
			<button class="action-btn share-btn" @tap="shareSpot">
				<text class="cuIcon-share"></text>
				<text>分享</text>
			</button>
			<button class="action-btn favorite-btn" :class="{ 'active': isFavorite }" @tap="toggleFavorite">
				<text class="cuIcon-favor"></text>
				<text>{{ isFavorite ? '已收藏' : '收藏' }}</text>
			</button>
			<button class="action-btn nav-action-btn" @tap="navigateTo">
				<text class="cuIcon-location"></text>
				<text>导航</text>
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: 0,
				spot: {
					name: '',
					description: '',
					images: [],
					address: '',
					opening_hours: '',
					ticket_price: '',
					tips: '',
					latitude: 0,
					longitude: 0,
					type: '',
					rating: 4.5,
					tags: [],
					contact: ''
				},
				markers: [],
				loading: true,
				loadError: false,
				errorMsg: '加载失败，请重试',
				statusBarHeight: 20,
				isFavorite: false,
				descExpanded: false,
				relatedSpots: [],
				userLocation: null,
				// 景点类型选项
				spotTypes: [
					{ label: '全部', value: 'all' },
					{ label: '自然风光', value: 'nature' },
					{ label: '历史古迹', value: 'history' },
					{ label: '文化场馆', value: 'culture' },
					{ label: '休闲娱乐', value: 'entertainment' }
				]
			}
		},
		onLoad(options) {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;

			// 获取用户位置
			this.getUserLocation();

			if (options.id) {
				this.id = options.id;
				this.loadSpotDetail();
				this.checkFavoriteStatus();
			} else {
				this.loading = false;
				this.loadError = true;
				this.errorMsg = '参数错误';
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		onShareAppMessage() {
			return {
				title: this.spot.name,
				path: '/pages/scenicspot/detail?id=' + this.id,
				imageUrl: this.spot.images && this.spot.images.length > 0 ? this.spot.images[0] : ''
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 加载景点详情
			loadSpotDetail() {
				this.loading = true;
				this.loadError = false;

				uni.request({
					url: this.$baseUrl + '/api/getScenicSpotDetail',
					method: 'GET',
					data: {
						id: this.id
					},
					success: (res) => {
						this.loading = false;

						if (res.data.code === 1) {
							this.spot = res.data.data.info;

							// 如果没有标签，添加一些默认标签
							if (!this.spot.tags || this.spot.tags.length === 0) {
								this.spot.tags = ['景点', '旅游', this.getSpotTypeLabel(this.spot.type)];
							}

							this.createMarkers();
							this.loadRelatedSpots();
						} else {
							this.loadError = true;
							this.errorMsg = res.data.msg || '加载失败，请重试';
						}
					},
					fail: () => {
						this.loading = false;
						this.loadError = true;
						this.errorMsg = '网络错误，请稍后再试';
					}
				});
			},

			// 获取用户位置
			getUserLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.userLocation = {
							latitude: res.latitude,
							longitude: res.longitude
						};
					}
				});
			},

			// 创建地图标记
			createMarkers() {
				this.markers = [{
					id: 1,
					latitude: Number(this.spot.latitude),
					longitude: Number(this.spot.longitude),
					title: this.spot.name,
					iconPath: '/static/images/marker.png',
					width: 32,
					height: 32,
					callout: {
						content: this.spot.name,
						color: '#333333',
						fontSize: 14,
						borderRadius: 8,
						bgColor: '#FFFFFF',
						padding: 8,
						display: 'ALWAYS'
					}
				}];
			},

			// 导航到景点
			navigateTo() {
				uni.openLocation({
					latitude: parseFloat(this.spot.latitude),
					longitude: parseFloat(this.spot.longitude),
					name: this.spot.name,
					address: this.spot.address || '景区内',
					scale: 18
				});
			},

			// 预览图片
			previewImage(index) {
				if (this.spot.images && this.spot.images.length > 0) {
					uni.previewImage({
						current: index,
						urls: this.spot.images
					});
				}
			},

			// 分享景点
			shareSpot() {
				// 在小程序中，会自动调用 onShareAppMessage
				// 在APP中，可以调用系统分享
				// #ifdef APP-PLUS
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession",
					type: 0,
					title: this.spot.name,
					summary: this.spot.description || '景点详情',
					imageUrl: this.spot.images && this.spot.images.length > 0 ? this.spot.images[0] : '',
					href: this.$baseUrl + '/share/spot?id=' + this.id
				});
				// #endif

				// #ifdef H5
				// H5环境下可以使用Web Share API
				if (navigator.share) {
					navigator.share({
						title: this.spot.name,
						text: this.spot.description || '景点详情',
						url: window.location.href
					});
				} else {
					// 复制链接
					uni.setClipboardData({
						data: window.location.href,
						success: () => {
							uni.showToast({
								title: '链接已复制，请粘贴分享给好友',
								icon: 'none'
							});
						}
					});
				}
				// #endif
			},

			// 切换收藏状态
			toggleFavorite() {
				this.isFavorite = !this.isFavorite;

				// 获取本地存储的收藏列表
				uni.getStorage({
					key: 'favoriteSpots',
					success: (res) => {
						let favorites = res.data || [];

						if (this.isFavorite) {
							// 添加到收藏
							if (!favorites.includes(this.id)) {
								favorites.push(this.id);
							}
							uni.showToast({
								title: '收藏成功',
								icon: 'success'
							});
						} else {
							// 从收藏中移除
							favorites = favorites.filter(id => id !== this.id);
							uni.showToast({
								title: '已取消收藏',
								icon: 'none'
							});
						}

						// 保存更新后的收藏列表
						uni.setStorage({
							key: 'favoriteSpots',
							data: favorites
						});
					},
					fail: () => {
						// 如果没有收藏列表，创建一个新的
						let favorites = [];
						if (this.isFavorite) {
							favorites.push(this.id);
							uni.showToast({
								title: '收藏成功',
								icon: 'success'
							});
						}

						uni.setStorage({
							key: 'favoriteSpots',
							data: favorites
						});
					}
				});
			},

			// 检查收藏状态
			checkFavoriteStatus() {
				uni.getStorage({
					key: 'favoriteSpots',
					success: (res) => {
						let favorites = res.data || [];
						this.isFavorite = favorites.includes(this.id);
					}
				});
			},

			// 切换描述展开/收起状态
			toggleDescExpand() {
				this.descExpanded = !this.descExpanded;
			},

			// 加载相关推荐景点
			loadRelatedSpots() {
				uni.request({
					url: this.$baseUrl + '/api/getScenicSpots',
					method: 'GET',
					data: {
						type: this.spot.type,
						exclude_id: this.id,
						limit: 5
					},
					success: (res) => {
						if (res.data.code === 1) {
							this.relatedSpots = res.data.data.list || [];

							// 计算距离
							if (this.userLocation && this.relatedSpots.length > 0) {
								this.relatedSpots.forEach(spot => {
									spot.distance = this.calculateDistance(
										this.userLocation.latitude,
										this.userLocation.longitude,
										spot.latitude,
										spot.longitude
									);
								});

								// 按距离排序
								this.relatedSpots.sort((a, b) => a.distance - b.distance);
							}
						}
					}
				});
			},

			// 跳转到景点详情
			goToSpotDetail(id) {
				uni.navigateTo({
					url: '/pages/scenicspot/detail?id=' + id
				});
			},

			// 获取景点类型标签
			getSpotTypeLabel(type) {
				const typeObj = this.spotTypes.find(item => item.value === type);
				return typeObj ? typeObj.label : '景点';
			},

			// 格式化距离
			formatDistance(distance) {
				if (distance < 1000) {
					return Math.round(distance) + 'm';
				} else {
					return (distance / 1000).toFixed(1) + 'km';
				}
			},

			// 计算两点之间的距离（米）
			calculateDistance(lat1, lon1, lat2, lon2) {
				const R = 6371000; // 地球半径，单位米
				const dLat = this.deg2rad(lat2 - lat1);
				const dLon = this.deg2rad(lon2 - lon1);
				const a =
					Math.sin(dLat/2) * Math.sin(dLat/2) +
					Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
					Math.sin(dLon/2) * Math.sin(dLon/2);
				const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
				const distance = R * c; // 距离，单位米
				return distance;
			},

			// 角度转弧度
			deg2rad(deg) {
				return deg * (Math.PI/180);
			}
		}
	}
</script>

<style>
	/* 容器样式 */
	.container {
		background-color: #F5F5F5;
		min-height: 100vh;
		position: relative;
		padding-bottom: 100rpx; /* 为底部操作栏留出空间 */
	}

	/* 自定义导航栏 */
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 44px;
		background-color: rgba(255, 255, 255, 0.9);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		z-index: 100;
		backdrop-filter: blur(10px);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.navbar-left {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.navbar-left text {
		font-size: 40rpx;
		color: #333333;
	}

	.navbar-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
	}

	.navbar-right {
		display: flex;
		align-items: center;
	}

	.navbar-right text {
		font-size: 40rpx;
		color: #333333;
		margin-left: 30rpx;
	}

	.text-red {
		color: #FF5252 !important;
	}

	/* 加载状态 */
	.loading-container {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100vh;
	}

	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.loading-icon {
		font-size: 60rpx;
		color: #0081ff;
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	.loading-content text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #666666;
	}

	/* 错误状态 */
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100vh;
		padding: 0 50rpx;
	}

	.error-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}

	.error-text {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.retry-btn {
		background-color: #0081ff;
		color: #FFFFFF;
		font-size: 28rpx;
		padding: 20rpx 60rpx;
		border-radius: 40rpx;
	}

	/* 内容区域 */
	.content-container {
		padding-top: 64px; /* 状态栏高度 + 导航栏高度 */
	}

	/* 轮播图 */
	.swiper-container {
		position: relative;
		width: 100%;
		height: 500rpx;
	}

	.swiper {
		width: 100%;
		height: 100%;
	}

	.swiper-image {
		width: 100%;
		height: 100%;
	}

	.spot-tag {
		position: absolute;
		top: 30rpx;
		right: 30rpx;
		background-color: rgba(0, 0, 0, 0.6);
		color: #FFFFFF;
		font-size: 24rpx;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		z-index: 10;
	}

	/* 景点信息 */
	.spot-info {
		padding: 30rpx;
		margin-top: -50rpx;
		position: relative;
		z-index: 20;
		border-radius: 30rpx 30rpx 0 0;
		background-color: #FFFFFF;
	}

	.spot-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.spot-name {
		font-size: 40rpx;
		font-weight: bold;
		color: #333333;
		flex: 1;
	}

	.spot-rating {
		display: flex;
		align-items: center;
	}

	.spot-rating text {
		font-size: 24rpx;
		margin-right: 4rpx;
	}

	.text-yellow {
		color: #FFAB00;
	}

	.rating-text {
		font-size: 24rpx;
		color: #666666;
		margin-left: 10rpx;
	}

	/* 标签列表 */
	.tags-scroll {
		white-space: nowrap;
		margin-bottom: 20rpx;
	}

	.tag-item {
		display: inline-block;
		font-size: 24rpx;
		color: #0081ff;
		background-color: rgba(0, 129, 255, 0.1);
		padding: 8rpx 20rpx;
		border-radius: 20rpx;
		margin-right: 15rpx;
	}

	/* 景点描述 */
	.spot-desc-container {
		margin-bottom: 30rpx;
	}

	.spot-desc {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
		max-height: 200rpx;
		overflow: hidden;
		transition: max-height 0.3s;
	}

	.spot-desc.expanded {
		max-height: none;
	}

	.expand-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #0081ff;
		margin-top: 10rpx;
	}

	.expand-btn text {
		margin-right: 6rpx;
	}

	/* 信息区块 */
	.info-section {
		margin-bottom: 30rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.section-title {
		display: flex;
		align-items: center;
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		color: #333333;
	}

	.section-title text:first-child {
		margin-right: 10rpx;
	}

	.text-blue {
		color: #0081ff;
	}

	.text-orange {
		color: #FF9800;
	}

	.text-green {
		color: #4CAF50;
	}

	.info-list {
		margin-bottom: 10rpx;
	}

	.info-item {
		display: flex;
		align-items: flex-start;
		font-size: 28rpx;
		margin-bottom: 20rpx;
		color: #666666;
	}

	.info-item text:first-child {
		margin-right: 15rpx;
		margin-top: 4rpx;
	}

	.tips-content {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
	}

	/* 地图 */
	.map-container {
		width: 100%;
		height: 400rpx;
		margin-bottom: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.map {
		width: 100%;
		height: 100%;
	}

	.nav-btn {
		width: 100%;
		height: 80rpx;
		background-color: #0081ff;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
		border: none;
	}

	.nav-btn::after {
		border: none;
	}

	.nav-btn text:first-child {
		margin-right: 10rpx;
	}

	/* 相关推荐 */
	.related-spots-scroll {
		white-space: nowrap;
		margin: 0 -30rpx;
		padding: 0 30rpx;
	}

	.related-spot-item {
		display: inline-block;
		width: 300rpx;
		margin-right: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		background-color: #FFFFFF;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.related-spot-image {
		width: 100%;
		height: 200rpx;
	}

	.related-spot-name {
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
		padding: 15rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.related-spot-distance {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #0081ff;
		padding: 0 15rpx 15rpx;
	}

	.related-spot-distance text:first-child {
		margin-right: 6rpx;
	}

	/* 底部操作栏 */
	.bottom-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 100rpx;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: space-around;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 50;
	}

	.action-btn {
		flex: 1;
		height: 80rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		background-color: transparent;
		border: none;
		color: #666666;
		padding: 0;
		line-height: 1.2;
	}

	.action-btn::after {
		border: none;
	}

	.action-btn text:first-child {
		font-size: 36rpx;
		margin-bottom: 4rpx;
	}

	.action-btn.active {
		color: #FF5252;
	}

	.nav-action-btn {
		background-color: #0081ff;
		color: #FFFFFF;
		border-radius: 40rpx;
		margin: 0 20rpx;
		flex: 2;
	}
</style>
