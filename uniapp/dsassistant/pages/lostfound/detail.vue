<template>
	<view class="container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<view class="loading-content">
				<text class="cuIcon-loading2 loading-icon"></text>
				<text>加载中...</text>
			</view>
		</view>

		<!-- 错误状态 -->
		<view class="error-container" v-else-if="loadError">
			<image src="/static/images/error.png" mode="aspectFit" class="error-image"></image>
			<text class="error-text">{{errorMsg}}</text>
			<button class="retry-btn" @tap="loadItemDetail">重新加载</button>
		</view>

		<!-- 内容区域 -->
		<view class="content-container" v-else>
			<!-- 图片轮播 -->
			<view class="swiper-container">
				<swiper class="swiper" indicator-dots autoplay circular :interval="3000" :duration="500" v-if="item.images && item.images.length > 0">
					<swiper-item v-for="(img, index) in item.images" :key="index" @tap="previewImage(index)">
						<image :src="img" mode="aspectFill" class="swiper-image"></image>
					</swiper-item>
				</swiper>
				<image v-else src="/static/images/no-image.jpg" mode="aspectFill" class="swiper-image"></image>

				<!-- 物品类型标签 -->
				<view class="item-tag" :class="item.type === 'lost' ? 'lost' : 'found'">
					{{item.type === 'lost' ? '寻物启事' : '招领启事'}}
				</view>

				<!-- 状态标签 -->
				<view class="status-tag" :class="'status-' + item.status">
					{{getStatusText(item.status)}}
				</view>
			</view>

			<!-- 物品信息 -->
			<view class="item-info">
				<!-- 物品标题 -->
				<view class="item-header">
					<view class="item-title">{{item.title}}</view>
					<view class="item-date">
						<text class="cuIcon-time"></text>
						<text>{{formatDate(item.createtime)}}</text>
					</view>
				</view>

				<!-- 物品描述 -->
				<view class="item-desc-container">
					<view class="item-desc" :class="{ 'expanded': descExpanded }">{{item.description || '暂无描述'}}</view>
					<view class="expand-btn" @tap="toggleDescExpand" v-if="item.description && item.description.length > 100">
						<text>{{ descExpanded ? '收起' : '展开' }}</text>
						<text class="cuIcon-unfold" v-if="!descExpanded"></text>
						<text class="cuIcon-fold" v-else></text>
					</view>
				</view>

				<!-- 基本信息 -->
				<view class="info-section">
					<view class="section-title">
						<text class="cuIcon-info text-blue"></text>
						<text>基本信息</text>
					</view>
					<view class="info-list">
						<view class="info-item">
							<text class="cuIcon-tag text-blue"></text>
							<text>类别：{{item.category || '未分类'}}</text>
						</view>
						<view class="info-item">
							<text class="cuIcon-location text-blue"></text>
							<text>地点：{{item.location || '未知地点'}}</text>
						</view>
						<view class="info-item">
							<text class="cuIcon-calendar text-blue"></text>
							<text>发布时间：{{formatDate(item.createtime)}}</text>
						</view>
						<view class="info-item">
							<text class="cuIcon-attention text-blue"></text>
							<text>状态：{{getStatusText(item.status)}}</text>
						</view>
					</view>
				</view>

				<!-- 联系方式 -->
				<view class="info-section">
					<view class="section-title">
						<text class="cuIcon-people text-green"></text>
						<text>联系方式</text>
					</view>
					<view class="contact-list">
						<view class="contact-item">
							<text class="cuIcon-people text-green"></text>
							<text>联系人：{{item.contact_name || '未提供'}}</text>
						</view>
						<view class="contact-item" @tap="makePhoneCall" v-if="item.contact_phone">
							<text class="cuIcon-phone text-green"></text>
							<text class="phone">电话：{{item.contact_phone}}</text>
							<view class="call-now">拨打</view>
						</view>
						<view class="contact-item" v-else>
							<text class="cuIcon-phone text-green"></text>
							<text>电话：未提供</text>
						</view>
					</view>
				</view>

				<!-- 相关推荐 -->
				<view class="info-section" v-if="relatedItems.length > 0">
					<view class="section-title">
						<text class="cuIcon-similar text-orange"></text>
						<text>相关推荐</text>
					</view>
					<scroll-view class="related-items-scroll" scroll-x>
						<view class="related-item" v-for="(item, index) in relatedItems" :key="index" @tap="goToItemDetail(item.id)">
							<image :src="item.cover || '/static/images/no-image.jpg'" mode="aspectFill" class="related-item-image"></image>
							<view class="related-item-info">
								<view class="related-item-title">{{item.title}}</view>
								<view class="related-item-type" :class="item.type === 'lost' ? 'lost' : 'found'">
									{{item.type === 'lost' ? '寻物' : '招领'}}
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-bar">
			<button class="action-btn share-btn" open-type="share">
				<text class="cuIcon-share"></text>
				<text>分享</text>
			</button>
			<button class="action-btn favorite-btn" :class="{ 'active': isFavorite }" @tap="toggleFavorite">
				<text class="cuIcon-favor"></text>
				<text>{{ isFavorite ? '已收藏' : '收藏' }}</text>
			</button>
			<button class="action-btn call-action-btn" @tap="makePhoneCall" v-if="item.contact_phone">
				<text class="cuIcon-phone"></text>
				<text>拨打电话</text>
			</button>
		</view>

		<!-- 底部安全区域 -->
		<safe-area></safe-area>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: 0,
				item: {
					title: '',
					description: '',
					type: '',
					category: '',
					location: '',
					contact_name: '',
					contact_phone: '',
					images: [],
					status: '',
					createtime: 0
				},
				loading: true,
				loadError: false,
				errorMsg: '加载失败，请重试',
				isFavorite: false,
				descExpanded: false,
				relatedItems: []
			}
		},
		onLoad(options) {
			if (options.id) {
				this.id = options.id;
				this.loadItemDetail();
				this.checkFavoriteStatus();
			} else {
				this.loading = false;
				this.loadError = true;
				this.errorMsg = '参数错误';
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		onShareAppMessage() {
			return {
				title: (this.item.type === 'lost' ? '寻物启事：' : '招领启事：') + this.item.title,
				path: '/pages/lostfound/detail?id=' + this.id,
				imageUrl: this.item.images && this.item.images.length > 0 ? this.item.images[0] : ''
			};
		},
		methods: {
			// 加载物品详情
			loadItemDetail() {
				this.loading = true;
				this.loadError = false;

				uni.request({
					url: this.$baseUrl + '/api/getLostFoundDetail',
					method: 'GET',
					data: {
						id: this.id
					},
					success: (res) => {
						this.loading = false;

						if (res.data.code === 1) {
							this.item = res.data.data.info;
							// 加载相关推荐
							this.loadRelatedItems();
						} else {
							this.loadError = true;
							this.errorMsg = res.data.msg || '加载失败，请重试';
						}
					},
					fail: () => {
						this.loading = false;
						this.loadError = true;
						this.errorMsg = '网络错误，请稍后再试';
					}
				});
			},

			// 加载相关推荐
			loadRelatedItems() {
				uni.request({
					url: this.$baseUrl + '/api/getLostFound',
					method: 'GET',
					data: {
						type: this.item.type,
						category: this.item.category,
						exclude_id: this.id,
						limit: 5
					},
					success: (res) => {
						if (res.data.code === 1) {
							this.relatedItems = res.data.data.list || [];
						}
					}
				});
			},

			// 预览图片
			previewImage(index) {
				if (this.item.images && this.item.images.length > 0) {
					uni.previewImage({
						current: index,
						urls: this.item.images
					});
				}
			},

			// 拨打电话
			makePhoneCall() {
				if (!this.item.contact_phone) {
					uni.showToast({
						title: '未提供联系电话',
						icon: 'none'
					});
					return;
				}

				uni.makePhoneCall({
					phoneNumber: this.item.contact_phone,
					fail: () => {
						uni.showToast({
							title: '拨打电话失败',
							icon: 'none'
						});
					}
				});
			},

			// 切换收藏状态
			toggleFavorite() {
				this.isFavorite = !this.isFavorite;

				// 获取本地存储的收藏列表
				uni.getStorage({
					key: 'favoriteItems',
					success: (res) => {
						let favorites = res.data || [];

						if (this.isFavorite) {
							// 添加到收藏
							if (!favorites.includes(this.id)) {
								favorites.push(this.id);
							}
							uni.showToast({
								title: '收藏成功',
								icon: 'success'
							});
						} else {
							// 从收藏中移除
							favorites = favorites.filter(id => id !== this.id);
							uni.showToast({
								title: '已取消收藏',
								icon: 'none'
							});
						}

						// 保存更新后的收藏列表
						uni.setStorage({
							key: 'favoriteItems',
							data: favorites
						});
					},
					fail: () => {
						// 如果没有收藏列表，创建一个新的
						let favorites = [];
						if (this.isFavorite) {
							favorites.push(this.id);
							uni.showToast({
								title: '收藏成功',
								icon: 'success'
							});
						}

						uni.setStorage({
							key: 'favoriteItems',
							data: favorites
						});
					}
				});
			},

			// 检查收藏状态
			checkFavoriteStatus() {
				uni.getStorage({
					key: 'favoriteItems',
					success: (res) => {
						let favorites = res.data || [];
						this.isFavorite = favorites.includes(this.id);
					}
				});
			},

			// 切换描述展开/收起状态
			toggleDescExpand() {
				this.descExpanded = !this.descExpanded;
			},

			// 跳转到物品详情
			goToItemDetail(id) {
				uni.navigateTo({
					url: '/pages/lostfound/detail?id=' + id
				});
			},

			// 获取状态文本
			getStatusText(status) {
				switch (status) {
					case 'pending': return '处理中';
					case 'processed': return '已处理';
					case 'closed': return '已关闭';
					default: return '未知';
				}
			},

			// 格式化日期
			formatDate(timestamp) {
				if (!timestamp) return '未知时间';

				const date = new Date(timestamp * 1000);
				return date.getFullYear() + '-' +
					   this.padZero(date.getMonth() + 1) + '-' +
					   this.padZero(date.getDate()) + ' ' +
					   this.padZero(date.getHours()) + ':' +
					   this.padZero(date.getMinutes());
			},

			// 补零
			padZero(num) {
				return num < 10 ? '0' + num : num;
			}
		}
	}
</script>

<style>
	/* 容器样式 */
	.container {
		background-color: #F5F5F5;
		min-height: 100vh;
		position: relative;
		padding-bottom: 100rpx; /* 为底部操作栏留出空间 */
	}

	/* 加载状态 */
	.loading-container {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100vh;
	}

	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.loading-icon {
		font-size: 60rpx;
		color: #0081ff;
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	.loading-content text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #666666;
	}

	/* 错误状态 */
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100vh;
		padding: 0 50rpx;
	}

	.error-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}

	.error-text {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.retry-btn {
		background-color: #0081ff;
		color: #FFFFFF;
		font-size: 28rpx;
		padding: 20rpx 60rpx;
		border-radius: 40rpx;
	}

	/* 内容区域 */
	.content-container {
		padding-bottom: 30rpx;
	}

	/* 轮播图 */
	.swiper-container {
		position: relative;
		width: 100%;
		height: 500rpx;
	}

	.swiper {
		width: 100%;
		height: 100%;
	}

	.swiper-image {
		width: 100%;
		height: 100%;
	}

	.item-tag {
		position: absolute;
		top: 30rpx;
		right: 30rpx;
		background-color: rgba(0, 0, 0, 0.6);
		color: #FFFFFF;
		font-size: 24rpx;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		z-index: 10;
	}

	.item-tag.lost {
		background-color: rgba(255, 82, 82, 0.8);
	}

	.item-tag.found {
		background-color: rgba(76, 175, 80, 0.8);
	}

	.status-tag {
		position: absolute;
		top: 30rpx;
		left: 30rpx;
		background-color: rgba(0, 0, 0, 0.6);
		color: #FFFFFF;
		font-size: 24rpx;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		z-index: 10;
	}

	.status-pending {
		background-color: rgba(255, 152, 0, 0.8);
	}

	.status-processed {
		background-color: rgba(0, 129, 255, 0.8);
	}

	.status-closed {
		background-color: rgba(158, 158, 158, 0.8);
	}

	/* 物品信息 */
	.item-info {
		padding: 30rpx;
		margin-top: -50rpx;
		position: relative;
		z-index: 20;
		border-radius: 30rpx 30rpx 0 0;
		background-color: #FFFFFF;
	}

	.item-header {
		display: flex;
		flex-direction: column;
		margin-bottom: 20rpx;
	}

	.item-title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}

	.item-date {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #999999;
	}

	.item-date text:first-child {
		margin-right: 6rpx;
	}

	/* 物品描述 */
	.item-desc-container {
		margin-bottom: 30rpx;
	}

	.item-desc {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
		max-height: 200rpx;
		overflow: hidden;
		transition: max-height 0.3s;
	}

	.item-desc.expanded {
		max-height: none;
	}

	.expand-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #0081ff;
		margin-top: 10rpx;
	}

	.expand-btn text {
		margin-right: 6rpx;
	}

	/* 信息区块 */
	.info-section {
		margin-bottom: 30rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.section-title {
		display: flex;
		align-items: center;
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		color: #333333;
	}

	.section-title text:first-child {
		margin-right: 10rpx;
	}

	.text-blue {
		color: #0081ff;
	}

	.text-green {
		color: #4CAF50;
	}

	.text-orange {
		color: #FF9800;
	}

	.info-list, .contact-list {
		margin-bottom: 10rpx;
	}

	.info-item, .contact-item {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		margin-bottom: 20rpx;
		color: #666666;
	}

	.info-item text:first-child, .contact-item text:first-child {
		margin-right: 15rpx;
	}

	.contact-item {
		position: relative;
	}

	.phone {
		color: #0081ff;
	}

	.call-now {
		position: absolute;
		right: 0;
		background-color: #4CAF50;
		color: #FFFFFF;
		font-size: 24rpx;
		padding: 6rpx 20rpx;
		border-radius: 30rpx;
	}

	/* 相关推荐 */
	.related-items-scroll {
		white-space: nowrap;
		margin: 0 -30rpx;
		padding: 0 30rpx;
	}

	.related-item {
		display: inline-block;
		width: 300rpx;
		margin-right: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		background-color: #FFFFFF;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.related-item-image {
		width: 100%;
		height: 200rpx;
	}

	.related-item-info {
		padding: 15rpx;
		position: relative;
	}

	.related-item-title {
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-right: 60rpx;
	}

	.related-item-type {
		position: absolute;
		right: 15rpx;
		top: 15rpx;
		font-size: 20rpx;
		color: #FFFFFF;
		padding: 4rpx 10rpx;
		border-radius: 10rpx;
	}

	.related-item-type.lost {
		background-color: #FF5252;
	}

	.related-item-type.found {
		background-color: #4CAF50;
	}

	/* 底部操作栏 */
	.bottom-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 100rpx;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: space-around;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 50;
		padding-bottom: env(safe-area-inset-bottom); /* 适配 iOS 底部安全区域 */
	}

	.action-btn {
		flex: 1;
		height: 80rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		background-color: transparent;
		border: none;
		color: #666666;
		padding: 0;
		line-height: 1.2;
	}

	.action-btn::after {
		border: none;
	}

	.action-btn text:first-child {
		font-size: 36rpx;
		margin-bottom: 4rpx;
	}

	.action-btn.active {
		color: #FF5252;
	}

	.call-action-btn {
		background-color: #0081ff;
		color: #FFFFFF;
		border-radius: 40rpx;
		margin: 0 20rpx;
		flex: 2;
	}
</style>
