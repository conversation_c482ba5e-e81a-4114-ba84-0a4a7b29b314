<template>
	<view class="container">
		<!-- 顶部标签栏 -->
		<view class="header">
			<view class="tabs">
				<view class="tab-item" :class="{active: activeTab === 'all'}" @tap="changeTab('all')">全部</view>
				<view class="tab-item" :class="{active: activeTab === 'lost'}" @tap="changeTab('lost')">寻物启事</view>
				<view class="tab-item" :class="{active: activeTab === 'found'}" @tap="changeTab('found')">招领启事</view>
			</view>

			<!-- 搜索框 -->
			<view class="search-box">
				<view class="search-input">
					<text class="cuIcon-search"></text>
					<input type="text" v-model="keyword" placeholder="搜索物品..." @confirm="search" />
					<text class="cuIcon-close" v-if="keyword" @tap="clearKeyword"></text>
				</view>
				<button class="search-btn" @tap="search">搜索</button>
			</view>

			<!-- 分类筛选 -->
			<scroll-view class="category-scroll" scroll-x v-if="categories.length > 0">
				<view class="category-item"
					  :class="{active: selectedCategory === category.id}"
					  v-for="(category, index) in categories"
					  :key="index"
					  @tap="selectCategory(category.id)">
					{{category.category}}
				</view>
			</scroll-view>
		</view>

		<!-- 下拉刷新提示 -->
		<view class="refresh-tip" v-if="refreshing">
			<text class="cuIcon-loading2 loading-icon"></text>
			<text>正在刷新...</text>
		</view>

		<!-- 物品列表 -->
		<scroll-view class="item-list"
					 scroll-y
					 @scrolltolower="loadMore"
					 @refresherrefresh="onRefresh"
					 refresher-enabled
					 :refresher-triggered="refreshing">
			<view class="item-container" v-if="items.length > 0">
				<view class="item-card" v-for="(item, index) in items" :key="index" @tap="navigateToDetail(item.id)">
					<view class="item-image-container">
						<image class="item-image" :src="item.cover || '/static/images/no-image.jpg'" mode="aspectFill"></image>
						<view class="item-type" :class="item.type === 'lost' ? 'lost' : 'found'">
							{{item.type === 'lost' ? '寻物' : '招领'}}
						</view>
						<view class="item-status" :class="'status-' + item.status">
							{{getStatusText(item.status)}}
						</view>
					</view>
					<view class="item-info">
						<view class="item-title">{{item.title}}</view>
						<view class="item-desc">{{item.description || '暂无描述'}}</view>
						<view class="item-meta">
							<view class="meta-item">
								<text class="cuIcon-tag"></text>
								<text>{{item.category || '未分类'}}</text>
							</view>
							<view class="meta-item">
								<text class="cuIcon-location"></text>
								<text>{{item.location || '未知地点'}}</text>
							</view>
							<view class="meta-item">
								<text class="cuIcon-time"></text>
								<text>{{formatDate(item.createtime)}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-tip" v-else-if="!loading">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无数据</text>
				<button class="retry-btn" @tap="refresh">重新加载</button>
			</view>

			<!-- 加载状态 -->
			<view class="loading" v-if="loading && !refreshing">
				<text class="cuIcon-loading2 loading-icon"></text>
				<text>加载中...</text>
			</view>

			<!-- 没有更多数据 -->
			<view class="no-more" v-if="!hasMore && items.length > 0">
				<text>没有更多数据了</text>
			</view>
		</scroll-view>

		<!-- 悬浮按钮 -->
		<view class="float-btn" @tap="navigateToSubmit">
			<text class="cuIcon-add"></text>
		</view>

		<!-- 底部安全区域 -->
		<safe-area></safe-area>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				activeTab: 'all',
				keyword: '',
				items: [],
				page: 1,
				limit: 10,
				loading: false,
				hasMore: true,
				refreshing: false,
				categories: [],
				selectedCategory: ''
			}
		},
		onLoad() {
			this.loadCategories();
			this.loadItems();
		},
		onPullDownRefresh() {
			this.refresh();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		methods: {
			// 加载分类列表
			loadCategories() {
				uni.request({
					url: this.$baseUrl + '/api/getLostFoundCategories',
					method: 'GET',
					success: (res) => {
						if (res.data.code === 1) {
							// 添加"全部"选项
							this.categories = [
								{ id: '', category: '全部' },
								...res.data.data
							];
						}
					}
				});
			},

			// 切换标签
			changeTab(tab) {
				if (this.activeTab === tab) return;
				this.activeTab = tab;
				this.refresh();
			},

			// 选择分类
			selectCategory(categoryId) {
				if (this.selectedCategory === categoryId) return;
				this.selectedCategory = categoryId;
				this.refresh();
			},

			// 清除关键词
			clearKeyword() {
				this.keyword = '';
				this.refresh();
			},

			// 搜索
			search() {
				this.refresh();
			},

			// 下拉刷新
			onRefresh() {
				this.refreshing = true;
				this.refresh();
				setTimeout(() => {
					this.refreshing = false;
				}, 1000);
			},

			// 刷新数据
			refresh() {
				this.page = 1;
				this.items = [];
				this.hasMore = true;
				this.loadItems();
			},

			// 加载更多
			loadMore() {
				if (this.loading || !this.hasMore) return;
				this.page++;
				this.loadItems();
			},

			// 加载物品列表
			loadItems() {
				if (this.loading && !this.refreshing) return;
				this.loading = true;

				uni.request({
					url: this.$baseUrl + '/api/getLostFound',
					method: 'GET',
					data: {
						page: this.page,
						limit: this.limit,
						type: this.activeTab === 'all' ? '' : this.activeTab,
						keyword: this.keyword,
						category_id: this.selectedCategory
					},
					success: (res) => {
						this.loading = false;

						if (res.data.code === 1) {
							const list = res.data.data.list;

							if (this.page === 1) {
								this.items = list;
							} else {
								this.items = [...this.items, ...list];
							}

							this.hasMore = list.length === this.limit;
						} else {
							uni.showToast({
								title: '加载失败：' + res.data.msg,
								icon: 'none'
							});
						}
					},
					fail: () => {
						this.loading = false;
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
					}
				});
			},

			// 获取状态文本
			getStatusText(status) {
				switch (status) {
					case 'pending': return '处理中';
					case 'processed': return '已处理';
					case 'closed': return '已关闭';
					default: return '未知';
				}
			},

			// 导航到详情页
			navigateToDetail(id) {
				uni.navigateTo({
					url: '/pages/lostfound/detail?id=' + id
				});
			},

			// 导航到提交页
			navigateToSubmit() {
				uni.navigateTo({
					url: '/pages/lostfound/submit'
				});
			},

			// 格式化日期
			formatDate(timestamp) {
				if (!timestamp) return '未知时间';

				const date = new Date(timestamp * 1000);
				return date.getFullYear() + '-' +
					   this.padZero(date.getMonth() + 1) + '-' +
					   this.padZero(date.getDate()) + ' ' +
					   this.padZero(date.getHours()) + ':' +
					   this.padZero(date.getMinutes());
			},

			// 补零
			padZero(num) {
				return num < 10 ? '0' + num : num;
			}
		}
	}
</script>

<style>
	/* 容器样式 */
	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #F5F5F5;
	}

	/* 头部区域 */
	.header {
		background-color: #FFFFFF;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 10;
	}

	/* 标签栏 */
	.tabs {
		display: flex;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #EEEEEE;
	}

	.tab-item {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
		padding: 10rpx 0;
		transition: all 0.3s;
	}

	.tab-item.active {
		color: #0081ff;
		font-weight: bold;
	}

	.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background-color: #0081ff;
		border-radius: 2rpx;
	}

	/* 搜索框 */
	.search-box {
		display: flex;
		padding: 20rpx;
		background-color: #FFFFFF;
	}

	.search-input {
		flex: 1;
		height: 70rpx;
		background-color: #F5F5F5;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		position: relative;
	}

	.search-input text.cuIcon-search {
		margin-right: 10rpx;
		color: #999999;
	}

	.search-input text.cuIcon-close {
		position: absolute;
		right: 20rpx;
		color: #999999;
		font-size: 24rpx;
	}

	.search-input input {
		flex: 1;
		height: 100%;
		font-size: 28rpx;
	}

	.search-btn {
		width: 120rpx;
		height: 70rpx;
		background-color: #0081ff;
		color: #FFFFFF;
		font-size: 28rpx;
		margin-left: 20rpx;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0;
	}

	/* 分类筛选 */
	.category-scroll {
		white-space: nowrap;
		padding: 20rpx;
		background-color: #FFFFFF;
		border-bottom: 1rpx solid #EEEEEE;
	}

	.category-item {
		display: inline-block;
		padding: 10rpx 30rpx;
		margin-right: 20rpx;
		font-size: 24rpx;
		color: #666666;
		background-color: #F5F5F5;
		border-radius: 30rpx;
		transition: all 0.3s;
	}

	.category-item.active {
		color: #FFFFFF;
		background-color: #0081ff;
	}

	/* 下拉刷新提示 */
	.refresh-tip {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
		font-size: 24rpx;
		color: #999999;
		background-color: #FFFFFF;
	}

	.refresh-tip text {
		margin-right: 10rpx;
	}

	/* 物品列表 */
	.item-list {
		flex: 1;
		padding: 20rpx;
		box-sizing: border-box;
	}

	.item-container {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.item-card {
		width: 48%;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 15rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
		transition: all 0.3s;
	}

	.item-card:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.item-image-container {
		position: relative;
		width: 100%;
		height: 200rpx;
	}

	.item-image {
		width: 100%;
		height: 100%;
	}

	.item-type {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		padding: 5rpx 10rpx;
		border-radius: 5rpx;
		font-size: 20rpx;
		color: #FFFFFF;
		z-index: 1;
	}

	.item-type.lost {
		background-color: rgba(255, 82, 82, 0.8);
	}

	.item-type.found {
		background-color: rgba(76, 175, 80, 0.8);
	}

	.item-status {
		position: absolute;
		top: 10rpx;
		left: 10rpx;
		padding: 5rpx 10rpx;
		border-radius: 5rpx;
		font-size: 20rpx;
		color: #FFFFFF;
		z-index: 1;
	}

	.status-pending {
		background-color: rgba(255, 152, 0, 0.8);
	}

	.status-processed {
		background-color: rgba(0, 129, 255, 0.8);
	}

	.status-closed {
		background-color: rgba(158, 158, 158, 0.8);
	}

	.item-info {
		padding: 15rpx;
	}

	.item-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		color: #333333;
	}

	.item-desc {
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		height: 70rpx;
		line-height: 1.5;
	}

	.item-meta {
		font-size: 20rpx;
		color: #999999;
	}

	.meta-item {
		display: flex;
		align-items: center;
		margin-bottom: 5rpx;
	}

	.meta-item text:first-child {
		margin-right: 5rpx;
	}

	/* 空状态 */
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}

	.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 20rpx;
	}

	.empty-tip text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 30rpx;
	}

	.retry-btn {
		background-color: #0081ff;
		color: #FFFFFF;
		font-size: 24rpx;
		padding: 10rpx 40rpx;
		border-radius: 30rpx;
	}

	/* 加载状态和没有更多 */
	.loading, .no-more {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
		font-size: 24rpx;
		color: #999999;
	}

	.loading-icon {
		animation: rotate 1s linear infinite;
		margin-right: 10rpx;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 悬浮按钮 */
	.float-btn {
		position: fixed;
		right: 30rpx;
		bottom: calc(30rpx + env(safe-area-inset-bottom)); /* 适配 iOS 底部安全区域 */
		width: 100rpx;
		height: 100rpx;
		background-color: #0081ff;
		color: #FFFFFF;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 129, 255, 0.3);
		z-index: 10;
		transition: all 0.3s;
	}

	.float-btn:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 10rpx rgba(0, 129, 255, 0.2);
	}

	.float-btn text {
		font-size: 50rpx;
	}
</style>
