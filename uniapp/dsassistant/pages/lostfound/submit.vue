<template>
	<view class="container">
		<view class="form-header">
			<view class="type-tabs">
				<view class="type-tab" :class="{active: formData.type === 'lost'}" @click="formData.type = 'lost'">
					<text class="cuIcon-search"></text>
					<text>寻物启事</text>
				</view>
				<view class="type-tab" :class="{active: formData.type === 'found'}" @click="formData.type = 'found'">
					<text class="cuIcon-discover"></text>
					<text>招领启事</text>
				</view>
			</view>
		</view>
		
		<view class="form-content">
			<view class="form-item">
				<view class="form-label">标题</view>
				<input class="form-input" type="text" v-model="formData.title" placeholder="请输入标题" />
			</view>
			
			<view class="form-item">
				<view class="form-label">描述</view>
				<textarea class="form-textarea" v-model="formData.description" placeholder="请详细描述物品特征、丢失/拾获时间、地点等信息" />
			</view>
			
			<view class="form-item">
				<view class="form-label">物品类别</view>
				<picker class="form-picker" :range="categories" @change="onCategoryChange">
					<view class="picker-value">{{formData.category || '请选择物品类别'}}</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="form-label">地点</view>
				<input class="form-input" type="text" v-model="formData.location" placeholder="请输入丢失/拾获地点" />
			</view>
			
			<view class="form-item">
				<view class="form-label">联系人</view>
				<input class="form-input" type="text" v-model="formData.contact_name" placeholder="请输入联系人姓名" />
			</view>
			
			<view class="form-item">
				<view class="form-label">联系电话</view>
				<input class="form-input" type="number" v-model="formData.contact_phone" placeholder="请输入联系电话" />
			</view>
			
			<view class="form-item">
				<view class="form-label">上传图片</view>
				<view class="upload-box">
					<view class="image-list">
						<view class="image-item" v-for="(item, index) in imageList" :key="index">
							<image :src="item" mode="aspectFill"></image>
							<view class="delete-btn" @click="deleteImage(index)">
								<text class="cuIcon-close"></text>
							</view>
						</view>
						<view class="upload-btn" @click="chooseImage" v-if="imageList.length < 9">
							<text class="cuIcon-cameraadd"></text>
						</view>
					</view>
					<view class="upload-tip">最多上传9张图片</view>
				</view>
			</view>
		</view>
		
		<view class="form-footer">
			<button class="submit-btn" @click="submitForm">提交</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					type: 'lost',
					title: '',
					description: '',
					category: '',
					location: '',
					contact_name: '',
					contact_phone: '',
					images: []
				},
				imageList: [],
				categories: ['证件', '钱包', '手机', '背包', '钥匙', '其他']
			}
		},
		methods: {
			// 选择物品类别
			onCategoryChange(e) {
				const index = e.detail.value;
				this.formData.category = this.categories[index];
			},
			
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 9 - this.imageList.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.imageList = [...this.imageList, ...res.tempFilePaths];
					}
				});
			},
			
			// 删除图片
			deleteImage(index) {
				this.imageList.splice(index, 1);
			},
			
			// 提交表单
			submitForm() {
				// 表单验证
				if (!this.formData.title) {
					this.showToast('请输入标题');
					return;
				}
				
				if (!this.formData.description) {
					this.showToast('请输入描述');
					return;
				}
				
				if (!this.formData.contact_name) {
					this.showToast('请输入联系人');
					return;
				}
				
				if (!this.formData.contact_phone) {
					this.showToast('请输入联系电话');
					return;
				}
				
				// 显示加载提示
				uni.showLoading({
					title: '提交中...'
				});
				
				// 先上传图片
				this.uploadImages().then(imageUrls => {
					// 更新表单数据
					this.formData.images = imageUrls;
					
					// 提交表单数据
					uni.request({
						url: this.$baseUrl + '/api/submitLostFound',
						method: 'POST',
						data: this.formData,
						success: (res) => {
							uni.hideLoading();
							
							if (res.data.code === 1) {
								uni.showToast({
									title: '提交成功',
									icon: 'success'
								});
								
								// 返回上一页
								setTimeout(() => {
									uni.navigateBack();
								}, 1500);
							} else {
								this.showToast('提交失败：' + res.data.msg);
							}
						},
						fail: () => {
							uni.hideLoading();
							this.showToast('网络错误，请稍后再试');
						}
					});
				}).catch(err => {
					uni.hideLoading();
					this.showToast('图片上传失败：' + err);
				});
			},
			
			// 上传图片
			uploadImages() {
				return new Promise((resolve, reject) => {
					if (this.imageList.length === 0) {
						resolve([]);
						return;
					}
					
					const uploadTasks = this.imageList.map(path => {
						return new Promise((resolve, reject) => {
							uni.uploadFile({
								url: this.$baseUrl + '/api/upload',
								filePath: path,
								name: 'file',
								success: (res) => {
									const data = JSON.parse(res.data);
									if (data.code === 1) {
										resolve(data.data.url);
									} else {
										reject(data.msg);
									}
								},
								fail: (err) => {
									reject(err.errMsg);
								}
							});
						});
					});
					
					Promise.all(uploadTasks).then(resolve).catch(reject);
				});
			},
			
			// 显示提示
			showToast(title) {
				uni.showToast({
					title: title,
					icon: 'none'
				});
			}
		}
	}
</script>

<style>
	.container {
		background-color: #F5F5F5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}
	
	.form-header {
		padding: 30rpx;
	}
	
	.type-tabs {
		display: flex;
		background-color: #FFFFFF;
		border-radius: 10rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.type-tab {
		flex: 1;
		height: 100rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
	}
	
	.type-tab.active {
		background-color: #007AFF;
		color: #FFFFFF;
	}
	
	.type-tab text:first-child {
		font-size: 40rpx;
		margin-bottom: 5rpx;
	}
	
	.form-content {
		padding: 0 30rpx;
	}
	
	.form-item {
		background-color: #FFFFFF;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.form-label {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	
	.form-input, .form-textarea, .form-picker {
		width: 100%;
		font-size: 28rpx;
		color: #333333;
	}
	
	.form-input {
		height: 80rpx;
	}
	
	.form-textarea {
		height: 200rpx;
		padding: 10rpx 0;
	}
	
	.picker-value {
		height: 80rpx;
		line-height: 80rpx;
		color: #333333;
	}
	
	.upload-box {
		margin-top: 10rpx;
	}
	
	.image-list {
		display: flex;
		flex-wrap: wrap;
	}
	
	.image-item, .upload-btn {
		width: 160rpx;
		height: 160rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
		overflow: hidden;
	}
	
	.image-item {
		position: relative;
	}
	
	.image-item image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.5);
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
	}
	
	.upload-btn {
		background-color: #F5F5F5;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 60rpx;
		color: #999999;
		border: 1rpx dashed #DDDDDD;
	}
	
	.upload-tip {
		font-size: 24rpx;
		color: #999999;
		margin-top: 10rpx;
	}
	
	.form-footer {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 20rpx 30rpx;
		background-color: #FFFFFF;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		box-sizing: border-box;
		z-index: 10;
	}
	
	.submit-btn {
		width: 100%;
		height: 80rpx;
		background-color: #007AFF;
		color: #FFFFFF;
		border-radius: 40rpx;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
