<template>
	<view class="guide-page" :class="{'map-fullscreen': isMapFullscreen}">
		<!-- 搜索栏 -->
		<search-bar
			:keyword="searchKeyword"
			@input="onSearchInput"
			@search="searchSpots"
			@clear="clearSearch"
			@filter="toggleFilterPanel"
		></search-bar>

		<!-- 筛选面板 -->
		<filter-panel
			:show="showFilterPanel"
			:spot-types="spotTypes"
			:selected-type.sync="selectedType"
			:sort-by.sync="sortBy"
			@reset="resetFilter"
			@apply="applyFilter"
			@close="toggleFilterPanel"
		></filter-panel>

		<!-- 地图容器 -->
		<view class="map-wrapper" @touchstart="onTouchStart" @touchend="onTouchEnd">
			<map-container
				:latitude="Number(latitude)"
				:longitude="Number(longitude)"
				:scale="scale"
				:markers="markers"
				:is-fullscreen="isMapFullscreen"
				:map-height="mapHeight"
				@marker-tap="onMarkerTap"
				@callout-tap="onCalloutTap"
				@region-change="onMapRegionChange"
				@zoom-in="zoomIn"
				@zoom-out="zoomOut"
				@reset-view="resetMapView"
				@locate-user="locateUser"
				@enter-fullscreen="enterFullscreen"
				@exit-fullscreen="exitFullscreen"
			></map-container>
		</view>

		<!-- 景点列表容器 -->
		<view class="spot-list-container" v-if="!isMapFullscreen" :style="{ height: listContainerHeight }">
			<!-- 分类标签 -->
			<category-tabs
				:categories="spotTypes"
				:active-category="activeCategory"
				@category-change="filterByCategory"
			></category-tabs>

			<!-- 景点列表 -->
			<spot-list
				:spots="filteredSpots"
				:spot-types="spotTypes"
				:has-more="hasMore"
				:loading="loading"
				:refreshing="refreshing"
				@load-more="loadMoreSpots"
				@refresh="refreshSpots"
				@spot-tap="showSpotDetail"
				@detail-tap="navigateToSpot"
				@nav-tap="navigateTo"
				@reset-filter="resetFilter"
			></spot-list>
		</view>

		<!-- 景点详情弹窗 -->
		<spot-detail-popup
			:show="showDetailPopup"
			:spot="currentSpot"
			@close="closeDetailPopup"
			@detail-tap="navigateToSpot"
			@nav-tap="navigateTo"
		></spot-detail-popup>

		<!-- 全局加载指示器 -->
		<loading-overlay :show="loading"></loading-overlay>
	</view>
</template>

<script>
	import SearchBar from '@/components/guide/SearchBar.vue';
	import FilterPanel from '@/components/guide/FilterPanel.vue';
	import MapContainer from '@/components/guide/MapContainer.vue';
	import CategoryTabs from '@/components/guide/CategoryTabs.vue';
	import SpotList from '@/components/guide/SpotList.vue';
	import SpotDetailPopup from '@/components/guide/SpotDetailPopup.vue';
	import LoadingOverlay from '@/components/guide/LoadingOverlay.vue';

	export default {
		components: {
			SearchBar,
			FilterPanel,
			MapContainer,
			CategoryTabs,
			SpotList,
			SpotDetailPopup,
			LoadingOverlay
		},
		data() {
			return {
				// 地图相关
				latitude: 39.90923,
				longitude: 116.397428,
				scale: 15,
				markers: [],
				isMapFullscreen: false,
				mapHeight: '400rpx',

				// 景点数据
				spots: [],
				filteredSpots: [],
				currentSpot: {},
				userLocation: null,

				// 分页加载
				page: 1,
				pageSize: 10,
				hasMore: true,
				loading: false,
				refreshing: false,

				// 搜索和筛选
				searchKeyword: '',
				showFilterPanel: false,
				selectedType: 'all',
				sortBy: 'distance',
				activeCategory: 'all',

				// 景点类型选项
				spotTypes: [],

				// 弹窗控制
				showDetailPopup: false,

				// 触摸事件相关
				touchStartY: 0,
				touchEndY: 0,
				touchThreshold: 50, // 触摸阈值，超过这个值才认为是有效的滑动

				// 列表容器高度
				listContainerHeight: 'calc(100vh - 110rpx - 400rpx)' // 默认高度
			}
		},
		computed: {
			// 根据筛选条件过滤景点
			computedFilteredSpots() {
				if (!this.spots.length) return [];

				let result = [...this.spots];

				// 关键词搜索
				if (this.searchKeyword) {
					const keyword = this.searchKeyword.toLowerCase();
					result = result.filter(spot =>
						spot.name.toLowerCase().includes(keyword) ||
						(spot.description && spot.description.toLowerCase().includes(keyword))
					);
				}

				// 类型筛选
				if (this.selectedType !== 'all') {
					result = result.filter(spot => spot.type === this.selectedType);
				}

				// 排序
				if (this.sortBy === 'distance' && this.userLocation) {
					result.sort((a, b) => {
						const distanceA = this.calculateDistance(
							this.userLocation.latitude,
							this.userLocation.longitude,
							a.latitude,
							a.longitude
						);
						const distanceB = this.calculateDistance(
							this.userLocation.latitude,
							this.userLocation.longitude,
							b.latitude,
							b.longitude
						);
						return distanceA - distanceB;
					});
				} else if (this.sortBy === 'popularity') {
					result.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
				}

				return result;
			}
		},
		onLoad() {
			this.getUserLocation();
			this.loadSpotTypes();
			this.loadSpots();
			this.updateMapHeight();
		},
		onShow() {
			// 页面显示时更新地图高度
			this.updateMapHeight();
		},
		onResize() {
			// 窗口大小变化时更新地图高度
			this.updateMapHeight();
		},
		methods: {
			// 搜索输入事件
			onSearchInput(value) {
				this.searchKeyword = value;
			},

			// 加载景点类型
			loadSpotTypes() {
				uni.request({
					url: this.$baseUrl + '/api/getScenicSpotTypes',
					method: 'GET',
					success: (res) => {
						if (res.data.code === 1) {
							const types = res.data.data.list || [];
							this.spotTypes = types.map(type => ({
								label: type.name,
								value: type.code,
								color: type.color || '#1890ff',
								icon: type.icon || ''
							}));
						}
					},
					fail: () => {
						// 如果加载失败，使用默认类型
						this.spotTypes = [];
					}
				});
			},

			// 加载景点信息
			loadSpots(reset = true) {
				if (this.loading && !this.refreshing) return;

				if (reset) {
					this.page = 1;
					this.hasMore = true;
				}

				this.loading = true;

				uni.request({
					url: this.$baseUrl + '/api/getScenicSpots',
					method: 'GET',
					data: {
						page: this.page,
						pageSize: this.pageSize,
						type: this.activeCategory !== 'all' ? this.activeCategory : '',
						keyword: this.searchKeyword
					},
					success: (res) => {
						this.loading = false;
						this.refreshing = false;

						if (res.data.code === 1) {
							const list = res.data.data.list || [];

							// 计算距离和处理类型信息
							if (this.userLocation) {
								list.forEach(spot => {
									spot.distance = this.calculateDistance(
										this.userLocation.latitude,
										this.userLocation.longitude,
										spot.latitude,
										spot.longitude
									);
								});
							}

							// 处理景点类型信息
							list.forEach(spot => {
								if (spot.type_info) {
									spot.type = spot.type_info.code || spot.type || '';
									spot.typeName = spot.type_info.name || '';
									spot.typeColor = spot.type_info.color || '#1890ff';
									spot.typeIcon = spot.type_info.icon || '';
								}
							});

							if (reset) {
								this.spots = list;
							} else {
								this.spots = [...this.spots, ...list];
							}

							// 更新过滤后的景点列表
							this.filteredSpots = this.computedFilteredSpots;

							// 创建地图标记
							this.createMarkers();

							// 判断是否还有更多数据
							this.hasMore = list.length >= this.pageSize;

							// 如果有景点且是首次加载，将地图中心设置为第一个景点的位置
							if (this.spots.length > 0 && reset && !this.userLocation) {
								this.latitude = Number(this.spots[0].latitude);
								this.longitude = Number(this.spots[0].longitude);
							}
						} else {
							uni.showToast({
								title: '加载失败：' + res.data.msg,
								icon: 'none'
							});
						}
					},
					fail: () => {
						this.loading = false;
						this.refreshing = false;

						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
					}
				});
			},

			// 加载更多景点
			loadMoreSpots() {
				if (!this.hasMore || this.loading) return;

				this.page++;
				this.loadSpots(false);
			},

			// 下拉刷新
			refreshSpots() {
				this.refreshing = true;
				this.loadSpots();
			},

			// 获取用户位置
			getUserLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.userLocation = {
							latitude: Number(res.latitude),
							longitude: Number(res.longitude)
						};

						this.latitude = Number(res.latitude);
						this.longitude = Number(res.longitude);

						// 重新计算距离
						if (this.spots.length > 0) {
							this.spots.forEach(spot => {
								spot.distance = this.calculateDistance(
									res.latitude,
									res.longitude,
									spot.latitude,
									spot.longitude
								);
							});

							// 更新过滤后的景点列表
							this.filteredSpots = this.computedFilteredSpots;
						}
					},
					fail: () => {
						uni.showToast({
							title: '获取位置失败，请检查定位权限',
							icon: 'none'
						});
					}
				});
			},

			// 创建地图标记
			createMarkers() {
				this.markers = this.spots.map((spot) => {
					return {
						id: spot.id,
						latitude: spot.latitude,
						longitude: spot.longitude,
						title: spot.name,
						iconPath: '/static/images/marker.png',
						width: 32,
						height: 32,
						callout: {
							content: spot.name,
							color: '#333333',
							fontSize: 14,
							borderRadius: 8,
							bgColor: '#FFFFFF',
							padding: 8,
							display: 'BYCLICK',
							textAlign: 'center'
						},
						label: {
							content: spot.name,
							color: '#333333',
							fontSize: 12,
							anchorX: 0,
							anchorY: -65,
							borderWidth: 0,
							borderColor: '#FFFFFF',
							borderRadius: 4,
							bgColor: '#FFFFFF',
							padding: 4,
							textAlign: 'center'
						}
					};
				});

				// 添加用户位置标记
				if (this.userLocation) {
					this.markers.push({
						id: 0,
						latitude: this.userLocation.latitude,
						longitude: this.userLocation.longitude,
						iconPath: '/static/images/user-location.png',
						width: 32,
						height: 32,
						zIndex: 99
					});
				}
			},

			// 标记点击事件
			onMarkerTap(e) {
				const markerId = e.markerId;
				const spot = this.spots.find(item => item.id === markerId);
				if (spot) {
					this.showSpotDetail(spot);
				}
			},

			// 气泡点击事件
			onCalloutTap(e) {
				const markerId = e.markerId;
				const spot = this.spots.find(item => item.id === markerId);
				if (spot) {
					this.showSpotDetail(spot);
				}
			},

			// 地图区域变化事件
			onMapRegionChange() {
				// 可以在这里处理地图区域变化事件
				// 例如加载当前可见区域内的景点
			},

			// 放大地图
			zoomIn() {
				if (this.scale < 20) {
					this.scale++;
				}
			},

			// 缩小地图
			zoomOut() {
				if (this.scale > 5) {
					this.scale--;
				}
			},

			// 重置地图视图
			resetMapView() {
				if (this.userLocation) {
					this.latitude = Number(this.userLocation.latitude);
					this.longitude = Number(this.userLocation.longitude);
				} else if (this.spots.length > 0) {
					this.latitude = Number(this.spots[0].latitude);
					this.longitude = Number(this.spots[0].longitude);
				}
				this.scale = 15;
			},

			// 定位用户
			locateUser() {
				if (this.userLocation) {
					this.latitude = Number(this.userLocation.latitude);
					this.longitude = Number(this.userLocation.longitude);
					this.scale = 16;
				} else {
					this.getUserLocation();
				}
			},

			// 进入全屏模式
			enterFullscreen() {
				this.isMapFullscreen = true;
				this.updateMapHeight();
			},

			// 退出全屏模式
			exitFullscreen() {
				this.isMapFullscreen = false;
				this.updateMapHeight();
			},

			// 更新地图高度
			updateMapHeight() {
				if (this.isMapFullscreen) {
					this.mapHeight = '100vh';
				} else {
					// 如果没有指定高度，使用默认高度
					if (!this.mapHeight || this.mapHeight === '100vh') {
						this.mapHeight = '400rpx';
					}

					// 直接通过计算更新列表容器的CSS变量
					this.$nextTick(() => {
						// 假设搜索栏高度为110rpx
						const searchBarHeight = 110;
						const mapHeightValue = parseInt(this.mapHeight);

						// 更新CSS变量，用于动态设置列表容器高度
						this.listContainerHeight = `calc(100vh - ${searchBarHeight}rpx - ${mapHeightValue}rpx)`;
					});
				}
			},

			// 搜索景点
			searchSpots() {
				this.loadSpots();
			},

			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
				this.loadSpots();
			},

			// 切换筛选面板
			toggleFilterPanel() {
				this.showFilterPanel = !this.showFilterPanel;
			},

			// 重置筛选
			resetFilter() {
				this.selectedType = 'all';
				this.sortBy = 'distance';
				this.searchKeyword = '';
				this.activeCategory = 'all';
				this.loadSpots();
				this.showFilterPanel = false;
			},

			// 应用筛选
			applyFilter() {
				this.activeCategory = this.selectedType;
				this.loadSpots();
				this.showFilterPanel = false;
			},

			// 按分类筛选
			filterByCategory(category) {
				this.activeCategory = category;
				this.loadSpots();
			},

			// 显示景点详情
			showSpotDetail(spot) {
				this.currentSpot = spot;
				this.showDetailPopup = true;
			},

			// 关闭景点详情弹窗
			closeDetailPopup() {
				this.showDetailPopup = false;
			},

			// 导航到景点详情页
			navigateToSpot(id) {
				uni.navigateTo({
					url: '/pages/scenicspot/detail?id=' + id
				});
			},

			// 导航到景点
			navigateTo(spot) {
				uni.openLocation({
					latitude: parseFloat(spot.latitude),
					longitude: parseFloat(spot.longitude),
					name: spot.name,
					address: spot.address || '景区内',
					scale: 18
				});
			},

			// 触摸开始事件
			onTouchStart(e) {
				if (this.isMapFullscreen) return;
				this.touchStartY = e.changedTouches[0].clientY;
			},

			// 触摸结束事件
			onTouchEnd(e) {
				if (this.isMapFullscreen) return;
				this.touchEndY = e.changedTouches[0].clientY;

				// 计算滑动距离
				const deltaY = this.touchStartY - this.touchEndY;

				// 向上滑动且超过阈值，收起地图
				if (deltaY > this.touchThreshold) {
					this.mapHeight = '200rpx'; // 收起地图，只显示一小部分
					this.listContainerHeight = 'calc(100vh - 110rpx - 200rpx)';
				}
				// 向下滑动且超过阈值，展开地图
				else if (deltaY < -this.touchThreshold) {
					this.mapHeight = '400rpx'; // 恢复地图高度
					this.listContainerHeight = 'calc(100vh - 110rpx - 400rpx)';
				}
			},

			// 计算两点之间的距离（米）
			calculateDistance(lat1, lon1, lat2, lon2) {
				const R = 6371000; // 地球半径，单位米
				const dLat = this.deg2rad(lat2 - lat1);
				const dLon = this.deg2rad(lon2 - lon1);
				const a =
					Math.sin(dLat/2) * Math.sin(dLat/2) +
					Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
					Math.sin(dLon/2) * Math.sin(dLon/2);
				const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
				const distance = R * c; // 距离，单位米
				return distance;
			},

			// 角度转弧度
			deg2rad(deg) {
				return deg * (Math.PI/180);
			}
		}
	}
</script>

<style>
	/* 页面容器 */
	.guide-page {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f8f8;
		position: relative;
	}

	/* 景点列表容器 */
	.spot-list-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		transition: height 0.3s ease; /* 添加过渡效果 */
		min-height: 0; /* 确保flex子项可以收缩 */
	}

	/* 确保SpotList组件能够填充剩余空间 */
	.spot-list-container > .spot-list {
		flex: 1;
		min-height: 0; /* 确保可以收缩 */
		overflow-y: auto; /* 确保可以垂直滚动 */
	}

	/* 当地图全屏时隐藏列表 */
	.map-fullscreen .spot-list-container {
		display: none;
	}

	/* 地图容器包装 */
	.map-wrapper {
		width: 100%;
		position: relative;
		transition: height 0.3s ease;
	}
</style>
