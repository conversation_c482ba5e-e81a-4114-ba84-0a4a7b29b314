<template>
	<view class="warning-page">
		<!-- 顶部筛选栏 -->
		<view class="filter-bar">
			<view
				class="filter-item"
				v-for="(item, index) in filterOptions"
				:key="index"
				:class="{ active: currentFilter === item.value }"
				@tap="filterWarnings(item.value)"
			>
				<text>{{ item.label }}</text>
			</view>
		</view>

		<!-- 预警列表 -->
		<scroll-view
			class="warning-list"
			scroll-y
			@scrolltolower="loadMore"
			@refresherrefresh="onRefresh"
			refresher-enabled
			:refresher-triggered="refreshing"
		>
			<view class="warning-item"
				v-for="(item, index) in filteredWarnings"
				:key="index"
				:class="'level-' + item.level"
				@tap="toggleExpand(index)"
			>
				<!-- 预警标签 -->
				<view class="warning-badge" :class="'level-' + item.level">
					{{ getLevelText(item.level) }}
				</view>

				<!-- 预警头部 -->
				<view class="warning-header">
					<view class="warning-icon" :class="'level-' + item.level">
						<text :class="getTypeIcon(item.type)"></text>
					</view>
					<view class="warning-info">
						<view class="warning-title">{{item.title}}</view>
						<view class="warning-meta">
							<text class="warning-type">{{getTypeText(item.type)}}</text>
							<text class="warning-time">{{formatTimeAgo(item.createtime)}}</text>
						</view>
					</view>
					<view class="warning-arrow" :class="{ 'expanded': item.expanded }">
						<text class="cuIcon-unfold"></text>
					</view>
				</view>

				<!-- 预警内容 - 展开时显示 -->
				<view class="warning-content" v-if="item.expanded">
					<view class="content-text">{{item.content}}</view>

					<view class="warning-details">
						<view class="detail-item">
							<text class="detail-label">发布时间</text>
							<text class="detail-value">{{formatDate(item.createtime)}}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">有效期</text>
							<text class="detail-value">{{formatDate(item.start_time)}} 至 {{formatDate(item.end_time)}}</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="warning-actions">
						<button class="action-btn share-btn" @tap.stop="shareWarning(item)">
							<text class="cuIcon-share"></text>
							<text>分享</text>
						</button>
						<button class="action-btn remind-btn" @tap.stop="setReminder(item)">
							<text class="cuIcon-notification"></text>
							<text>提醒</text>
						</button>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="loading-more" v-if="hasMore && filteredWarnings.length > 0">
				<text class="cuIcon-loading2 loading-icon"></text>
				<text>加载更多...</text>
			</view>

			<!-- 无数据提示 -->
			<view class="empty-tip" v-if="filteredWarnings.length === 0 && !loading">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>当前没有预警信息</text>
				<button class="refresh-btn" @tap="loadWarnings">刷新</button>
			</view>
		</scroll-view>

		<!-- 全局加载指示器 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<text class="cuIcon-loading2 loading-icon"></text>
				<text>加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				warnings: [],
				loading: false,
				refreshing: false,
				currentFilter: 'all',
				page: 1,
				pageSize: 10,
				hasMore: true,
				filterOptions: [
					{ label: '全部', value: 'all' },
					{ label: '人流', value: 'crowd' },
					{ label: '天气', value: 'weather' },
					{ label: '交通', value: 'traffic' }
				]
			}
		},
		computed: {
			// 根据筛选条件过滤预警信息
			filteredWarnings() {
				if (this.currentFilter === 'all') {
					return this.warnings;
				} else {
					return this.warnings.filter(item => item.type === this.currentFilter);
				}
			}
		},
		onLoad() {
			this.loadWarnings();
		},
		methods: {
			// 加载预警信息
			loadWarnings(reset = true) {
				if (this.loading && !this.refreshing) return;

				if (reset) {
					this.page = 1;
					this.hasMore = true;
				}

				this.loading = true;

				uni.request({
					url: this.$baseUrl + '/api/getWarnings',
					method: 'GET',
					data: {
						page: this.page,
						pageSize: this.pageSize,
						type: this.currentFilter !== 'all' ? this.currentFilter : ''
					},
					success: (res) => {
						this.loading = false;
						this.refreshing = false;

						if (res.data.code === 1) {
							const list = res.data.data.list || [];

							// 添加展开状态属性
							list.forEach(item => {
								item.expanded = false;
							});

							if (reset) {
								this.warnings = list;
							} else {
								this.warnings = [...this.warnings, ...list];
							}

							// 判断是否还有更多数据
							this.hasMore = list.length >= this.pageSize;
						} else {
							uni.showToast({
								title: '加载失败：' + res.data.msg,
								icon: 'none'
							});
						}
					},
					fail: () => {
						this.loading = false;
						this.refreshing = false;

						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
					}
				});
			},

			// 加载更多
			loadMore() {
				if (!this.hasMore || this.loading) return;

				this.page++;
				this.loadWarnings(false);
			},

			// 下拉刷新
			onRefresh() {
				this.refreshing = true;
				this.loadWarnings();
			},

			// 筛选预警
			filterWarnings(type) {
				if (this.currentFilter === type) return;

				this.currentFilter = type;
				this.loadWarnings();
			},

			// 切换展开状态
			toggleExpand(index) {
				// 先将所有项折叠
				this.warnings.forEach((item, i) => {
					if (i !== index) {
						this.$set(item, 'expanded', false);
					}
				});

				// 切换当前项的展开状态
				this.$set(this.warnings[index], 'expanded', !this.warnings[index].expanded);
			},

			// 分享预警
			shareWarning(item) {
				uni.showShareMenu({
					withShareTicket: true,
					success: () => {
						uni.showToast({
							title: '请点击右上角分享',
							icon: 'none'
						});
					}
				});
			},

			// 设置提醒
			setReminder(item) {
				uni.showModal({
					title: '设置提醒',
					content: '是否设置此预警的到期提醒？',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '提醒已设置',
								icon: 'success'
							});
						}
					}
				});
			},

			// 获取类型图标
			getTypeIcon(type) {
				switch (type) {
					case 'crowd': return 'cuIcon-group';
					case 'weather': return 'cuIcon-cloudy';
					case 'traffic': return 'cuIcon-car';
					default: return 'cuIcon-warn';
				}
			},

			// 获取类型文本
			getTypeText(type) {
				switch (type) {
					case 'crowd': return '人流预警';
					case 'weather': return '天气预警';
					case 'traffic': return '交通预警';
					default: return '其他预警';
				}
			},

			// 获取级别文本
			getLevelText(level) {
				switch (level) {
					case 'info': return '提示';
					case 'warning': return '警告';
					case 'danger': return '紧急';
					default: return '提示';
				}
			},

			// 格式化日期
			formatDate(timestamp) {
				if (!timestamp) return '未知时间';

				const date = new Date(timestamp * 1000);
				return date.getFullYear() + '-' +
					   this.padZero(date.getMonth() + 1) + '-' +
					   this.padZero(date.getDate()) + ' ' +
					   this.padZero(date.getHours()) + ':' +
					   this.padZero(date.getMinutes());
			},

			// 格式化相对时间
			formatTimeAgo(timestamp) {
				if (!timestamp) return '未知时间';

				const now = new Date();
				const date = new Date(timestamp * 1000);
				const diff = Math.floor((now - date) / 1000);

				if (diff < 60) {
					return '刚刚';
				} else if (diff < 3600) {
					return Math.floor(diff / 60) + '分钟前';
				} else if (diff < 86400) {
					return Math.floor(diff / 3600) + '小时前';
				} else if (diff < 2592000) {
					return Math.floor(diff / 86400) + '天前';
				} else {
					return this.formatDate(timestamp);
				}
			},

			// 补零
			padZero(num) {
				return num < 10 ? '0' + num : num;
			}
		}
	}
</script>

<style>
	/* 页面容器 */
	.warning-page {
		background-color: #f8f8f8;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	/* 筛选栏 */
	.filter-bar {
		display: flex;
		background-color: #ffffff;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		position: sticky;
		top: 0;
		z-index: 10;
	}

	.filter-item {
		padding: 12rpx 24rpx;
		margin-right: 20rpx;
		border-radius: 30rpx;
		font-size: 26rpx;
		color: #666666;
		background-color: #f5f5f5;
		transition: all 0.3s;
	}

	.filter-item.active {
		background-color: #0081ff;
		color: #ffffff;
		font-weight: 500;
	}

	/* 预警列表 */
	.warning-list {
		flex: 1;
		padding: 20rpx;
		box-sizing: border-box;
	}

	/* 预警项 */
	.warning-item {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
		position: relative;
	}

	/* 预警标签 */
	.warning-badge {
		position: absolute;
		top: 0;
		right: 0;
		padding: 6rpx 16rpx;
		font-size: 22rpx;
		color: #ffffff;
		border-bottom-left-radius: 16rpx;
		z-index: 1;
	}

	.warning-badge.level-info {
		background-color: #0081ff;
	}

	.warning-badge.level-warning {
		background-color: #fbbd08;
	}

	.warning-badge.level-danger {
		background-color: #e54d42;
	}

	/* 预警头部 */
	.warning-header {
		display: flex;
		align-items: center;
		padding: 24rpx;
		position: relative;
	}

	/* 预警图标 */
	.warning-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.warning-icon.level-info {
		background-color: rgba(0, 129, 255, 0.1);
		color: #0081ff;
	}

	.warning-icon.level-warning {
		background-color: rgba(251, 189, 8, 0.1);
		color: #fbbd08;
	}

	.warning-icon.level-danger {
		background-color: rgba(229, 77, 66, 0.1);
		color: #e54d42;
	}

	/* 预警信息 */
	.warning-info {
		flex: 1;
		overflow: hidden;
	}

	.warning-title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.warning-meta {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 24rpx;
	}

	.warning-type {
		color: #666666;
		background-color: #f5f5f5;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
	}

	.warning-time {
		color: #999999;
	}

	/* 展开箭头 */
	.warning-arrow {
		margin-left: 20rpx;
		color: #cccccc;
		transition: all 0.3s;
	}

	.warning-arrow.expanded {
		transform: rotate(180deg);
	}

	/* 预警内容 */
	.warning-content {
		padding: 0 24rpx 24rpx;
		border-top: 1rpx solid #f5f5f5;
		animation: fadeIn 0.3s ease;
	}

	.content-text {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
		margin-top: 20rpx;
		margin-bottom: 30rpx;
	}

	/* 预警详情 */
	.warning-details {
		background-color: #f8f8f8;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
	}

	.detail-item {
		display: flex;
		margin-bottom: 16rpx;
	}

	.detail-item:last-child {
		margin-bottom: 0;
	}

	.detail-label {
		font-size: 26rpx;
		color: #999999;
		width: 120rpx;
		flex-shrink: 0;
	}

	.detail-value {
		font-size: 26rpx;
		color: #666666;
		flex: 1;
	}

	/* 操作按钮 */
	.warning-actions {
		display: flex;
		justify-content: flex-end;
	}

	.action-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 30rpx;
		height: 70rpx;
		border-radius: 35rpx;
		font-size: 26rpx;
		margin-left: 20rpx;
		background-color: #f5f5f5;
		color: #666666;
		border: none;
	}

	.action-btn::after {
		border: none;
	}

	.action-btn text {
		margin-right: 6rpx;
	}

	.share-btn {
		background-color: #f5f5f5;
		color: #666666;
	}

	.remind-btn {
		background-color: #0081ff;
		color: #ffffff;
	}

	/* 加载更多 */
	.loading-more {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30rpx 0;
		font-size: 24rpx;
		color: #999999;
	}

	/* 空数据提示 */
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}

	.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.5;
	}

	.empty-tip text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 30rpx;
	}

	.refresh-btn {
		background-color: #0081ff;
		color: #ffffff;
		font-size: 26rpx;
		padding: 10rpx 40rpx;
		border-radius: 30rpx;
		border: none;
	}

	.refresh-btn::after {
		border: none;
	}

	/* 全局加载指示器 */
	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.loading-content {
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 12rpx;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.loading-content text {
		color: #ffffff;
		font-size: 26rpx;
		margin-top: 20rpx;
	}

	.loading-icon {
		font-size: 50rpx;
		color: #ffffff;
		animation: rotate 1s linear infinite;
	}

	/* 动画 */
	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
</style>
