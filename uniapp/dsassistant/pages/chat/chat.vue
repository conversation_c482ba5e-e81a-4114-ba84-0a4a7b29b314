<template>
	<view class="chat-page">
		<!-- 聊天内容区域 -->
		<scroll-view
			class="chat-container"
			scroll-y="true"
			:scroll-top="scrollTop"
			:scroll-with-animation="true"
			@scrolltoupper="loadMoreMessages"
		>
			<view class="chat-list">
				<!-- 欢迎提示 -->
				<view class="chat-tips" v-if="chatList.length > 0">
					<text>今天</text>
				</view>

				<!-- 聊天消息列表 -->
				<chat-message
					v-for="(item, index) in chatList"
					:key="index"
					:is-user="item.isUser"
					:message="item.message"
					:parsed-message="item.parsedMessage"
					:time="item.time"
					:is-streaming="item.isStreaming"
					:function-calls="item.functionCalls"
				/>

				<!-- 正在输入指示器 -->
				<chat-typing-indicator :visible="isTyping" />

				<!-- 底部留白，确保最后一条消息不被输入框遮挡 -->
				<view class="chat-bottom-space"></view>
			</view>
		</scroll-view>

		<!-- 输入区域 -->
		<chat-input @send="handleSendMessage" />

		<!-- 引入Markdown样式 -->
		<markdown-styles />
	</view>
</template>

<script>
	// 导入组件
	import ChatMessage from '@/components/chat/ChatMessage.vue';
	import ChatInput from '@/components/chat/ChatInput.vue';
	import ChatTypingIndicator from '@/components/chat/ChatTypingIndicator.vue';
	import MarkdownStyles from '@/components/chat/MarkdownStyles.vue';

	// 导入服务
	import ChatService from '@/utils/ChatService.js';
	import {marked} from 'marked';

	export default {
		// 注册组件
		components: {
			ChatMessage,
			ChatInput,
			ChatTypingIndicator,
			MarkdownStyles
		},
		data() {
			return {
				chatList: [],
				scrollTop: 0,
				isTyping: false,
				sessionId: '',
				userId: '',
				chatService: null,
				inputMessage: '' // 保留以兼容旧代码
			}
		},
		onLoad() {
			// 生成会话ID
			this.sessionId = 'miniapp_' + Math.random().toString(36).substring(2, 15);

			// 获取或生成设备ID
			let deviceId = uni.getStorageSync('deviceId');
			if (!deviceId) {
				// 生成一个随机的设备ID
				deviceId = 'device_' + Math.random().toString(36).substring(2, 15) + Date.now().toString(36);
				uni.setStorageSync('deviceId', deviceId);
			}

			// 获取或生成用户ID
			this.userId = uni.getStorageSync('userId') || 'user_' + Math.random().toString(36).substring(2, 15);
			uni.setStorageSync('userId', this.userId);

			// 初始化聊天服务
			this.chatService = new ChatService({
				baseUrl: this.$baseUrl,
				userId: this.userId,
				deviceId: deviceId,
				sessionId: this.sessionId,
				platform: 'miniapp'
			});

			// 添加欢迎消息
			const welcomeMessage = '您好！我是景区智能助理，请问有什么可以帮助您的？';

			// 创建欢迎消息对象
			const welcomeMessageObj = {
				isUser: false,
				message: welcomeMessage,
				parsedMessage: this.parseMarkdown(welcomeMessage),
				time: this.formatTime(new Date()),
				isStreaming: false,
				functionCalls: [] // 初始欢迎消息没有函数调用
			};

			// 添加到聊天列表
			this.chatList.push(welcomeMessageObj);

			// 滚动到底部
			this.$nextTick(() => {
				this.scrollToBottom();
			});
		},
		methods: {
			// 处理发送消息
			handleSendMessage(message) {
				if (!message.trim()) return;

				// 添加用户消息
				this.chatList.push({
					isUser: true,
					message: message,
					time: this.formatTime(new Date())
				});

				// 滚动到底部
				this.$nextTick(() => {
					this.scrollToBottom();
				});

				// 显示正在输入指示器
				this.isTyping = true;

				// 使用聊天服务发送消息
				this.chatService.sendMessage(
					message,
					// 成功回调
					(response) => {
						// 隐藏正在输入指示器
						this.isTyping = false;

						// 创建一个新的消息对象
						const newMessage = {
							isUser: false,
							message: response.text,
							parsedMessage: this.parseMarkdown(response.text),
							time: this.formatTime(new Date()),
							isStreaming: false,
							functionCalls: response.functionCalls || []
						};

						// 添加到聊天列表
						this.chatList.push(newMessage);

						// 滚动到底部
						this.$nextTick(() => {
							this.scrollToBottom();
						});
					},
					// 失败回调
					(errorMsg) => {
						// 隐藏正在输入指示器
						this.isTyping = false;

						// 添加错误消息
						this.chatList.push({
							isUser: false,
							message: errorMsg,
							parsedMessage: this.parseMarkdown(errorMsg),
							time: this.formatTime(new Date()),
							isStreaming: false, // 确保不使用流式显示
							functionCalls: [] // 确保没有函数调用
						});

						// 滚动到底部
						this.$nextTick(() => {
							this.scrollToBottom();
						});
					}
				);
			},

			// 旧的发送消息方法（保留以兼容现有代码）
			sendMessage() {
				if (!this.inputMessage.trim()) return;

				// 调用新的处理方法
				this.handleSendMessage(this.inputMessage);

				// 清空输入框
				this.inputMessage = '';
			},

			// 滚动到底部
			scrollToBottom() {
				// 使用一个较大的值确保滚动到底部
				this.scrollTop = 100000;
			},

			// 加载更多消息（暂未实现）
			loadMoreMessages() {
				// 这里可以实现加载历史消息的功能
				console.log('加载更多消息');
			},

			// 格式化日期
			formatDate(date) {
				// 判断是否是今天
				const today = new Date();
				if (date.getDate() === today.getDate() &&
					date.getMonth() === today.getMonth() &&
					date.getFullYear() === today.getFullYear()) {
					return '';
				}

				const month = this.padZero(date.getMonth() + 1);
				const day = this.padZero(date.getDate());

				// 如果是今年，只显示月日
				if (date.getFullYear() === today.getFullYear()) {
					return month + '月' + day + '日';
				}

				// 否则显示完整日期
				const year = date.getFullYear();
				return year + '年' + month + '月' + day + '日';
			},

			// 格式化时间
			formatTime(date) {
				const hour = this.padZero(date.getHours());
				const minute = this.padZero(date.getMinutes());
				return hour + ':' + minute;
			},

			// 补零
			padZero(num) {
				return num < 10 ? '0' + num : num;
			},



			// 解析Markdown
			parseMarkdown(text) {
				if (!text) return '';

				try {
					// 使用marked.js解析Markdown
					let html = marked(text);

					// 处理HTML以适应小程序rich-text组件
					// 为HTML元素添加类名，以便应用样式
					html = html
						.replace(/<h1/g, '<h1 class="markdown-h1"')
						.replace(/<h2/g, '<h2 class="markdown-h2"')
						.replace(/<h3/g, '<h3 class="markdown-h3"')
						.replace(/<h4/g, '<h4 class="markdown-h4"')
						.replace(/<h5/g, '<h5 class="markdown-h5"')
						.replace(/<h6/g, '<h6 class="markdown-h6"')
						.replace(/<p/g, '<p class="markdown-p"')
						.replace(/<ul/g, '<ul class="markdown-ul"')
						.replace(/<ol/g, '<ol class="markdown-ol"')
						.replace(/<li/g, '<li class="markdown-li"')
						.replace(/<blockquote/g, '<blockquote class="markdown-blockquote"')
						.replace(/<pre/g, '<pre class="markdown-pre"')
						.replace(/<code/g, '<code class="markdown-code"')
						.replace(/<a/g, '<a class="markdown-a"')
						.replace(/<img/g, '<img class="markdown-img"')
						.replace(/<table/g, '<table class="markdown-table"')
						.replace(/<th/g, '<th class="markdown-th"')
						.replace(/<td/g, '<td class="markdown-td"');

					return html;
				} catch (error) {
					console.error('Markdown解析错误:', error);
					return text;
				}
			}
		}
	}
</script>

<style>
	/* 页面容器 */
	.chat-page {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f7fa;
	}

	/* 聊天容器 */
	.chat-container {
		flex: 1;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
	}

	/* 聊天列表 */
	.chat-list {
		padding-bottom: 20rpx;
	}

	/* 聊天提示 */
	.chat-tips {
		text-align: center;
		margin: 20rpx 0;
	}

	.chat-tips text {
		font-size: 24rpx;
		color: #999;
		background-color: rgba(0, 0, 0, 0.05);
		padding: 6rpx 20rpx;
		border-radius: 20rpx;
	}

	/* 底部留白 */
	.chat-bottom-space {
		height: 30rpx;
	}
</style>
