<template>
	<view class="page">
		<!-- 顶部轮播图 - 更符合移动端视觉体验，高度适中 -->
		<swiper class="home-swiper" circular autoplay interval="5000" duration="500" indicator-dots indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#FFFFFF">
			<swiper-item>
				<image src="/static/images/header-bg.jpg" mode="aspectFill"></image>
				<view class="swiper-overlay">
					<view class="swiper-content">
						<view class="swiper-title">景区智能助理</view>
						<view class="swiper-desc">基于DeepSeek AI技术，为游客提供全方位智能服务</view>
					</view>
				</view>
			</swiper-item>
			<swiper-item>
				<image src="/static/images/scenic-1.jpg" mode="aspectFill"></image>
				<view class="swiper-overlay">
					<view class="swiper-content">
						<view class="swiper-title">探索自然美景</view>
						<view class="swiper-desc">让我们的智能助理带您领略景区的壮丽风光</view>
					</view>
				</view>
			</swiper-item>
			<swiper-item>
				<image src="/static/images/scenic-2.jpg" mode="aspectFill"></image>
				<view class="swiper-overlay">
					<view class="swiper-content">
						<view class="swiper-title">智能导览体验</view>
						<view class="swiper-desc">随时随地获取景点信息，享受智能导览服务</view>
					</view>
				</view>
			</swiper-item>
		</swiper>

		<!-- 核心功能区 - 大图标，清晰文字，更符合移动端触摸操作 -->
		<view class="feature-section">
			<view class="section-title">
				<text>核心功能</text>
			</view>
			<view class="feature-grid">
				<view class="feature-item" @click="navigateTo('/pages/chat/chat')">
					<view class="feature-icon bg-blue">
						<text class="cuIcon-comment"></text>
					</view>
					<view class="feature-info">
						<view class="feature-name">智能问答</view>
						<view class="feature-desc">解答各类问题</view>
					</view>
				</view>
				<view class="feature-item" @click="navigateTo('/pages/warning/warning')">
					<view class="feature-icon bg-orange">
						<text class="cuIcon-warn"></text>
					</view>
					<view class="feature-info">
						<view class="feature-name">实时预警</view>
						<view class="feature-desc">及时获取预警</view>
					</view>
				</view>
				<view class="feature-item" @click="navigateTo('/pages/guide/guide')">
					<view class="feature-icon bg-green">
						<text class="cuIcon-location"></text>
					</view>
					<view class="feature-info">
						<view class="feature-name">电子导览</view>
						<view class="feature-desc">景区地图导航</view>
					</view>
				</view>
				<view class="feature-item" @click="navigateTo('/pages/lostfound/lostfound')">
					<view class="feature-icon bg-purple">
						<text class="cuIcon-search"></text>
					</view>
					<view class="feature-info">
						<view class="feature-name">失物招领</view>
						<view class="feature-desc">失物登记查询</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 预警信息区 - 更符合移动端信息展示，紧凑但清晰 -->
		<view class="warning-section" v-if="warnings.length > 0">
			<view class="section-header">
				<view class="section-title">
					<text class="cuIcon-notificationfill text-red"></text>
					<text>最新预警</text>
				</view>
				<view class="section-more" @click="navigateTo('/pages/warning/warning')">
					<text>更多</text>
					<text class="cuIcon-right"></text>
				</view>
			</view>
			<view class="warning-list">
				<view
					class="warning-item"
					v-for="(item, index) in warnings"
					:key="index"
					:class="'level-' + item.level"
				>
					<view class="warning-icon">
						<text :class="getTypeIcon(item.type)"></text>
					</view>
					<view class="warning-content">
						<view class="warning-title">{{item.title}}</view>
						<view class="warning-time">{{formatDate(item.createtime)}}</view>
					</view>
					<text class="cuIcon-right warning-arrow"></text>
				</view>
			</view>
		</view>

		<!-- 热门景点区 - 卡片式设计，更符合移动端浏览习惯 -->
		<view class="spot-section">
			<view class="section-header">
				<view class="section-title">
					<text>热门景点</text>
				</view>
				<view class="section-more" @click="navigateTo('/pages/guide/guide')">
					<text>更多</text>
					<text class="cuIcon-right"></text>
				</view>
			</view>
			<scroll-view class="spot-scroll" scroll-x>
				<view class="spot-list">
					<view
						class="spot-item"
						v-for="(item, index) in spots"
						:key="index"
						@click="navigateToSpot(item.id)"
					>
						<image
							class="spot-image"
							:src="item.cover || '/static/images/no-image.jpg'"
							mode="aspectFill"
						></image>
						<view class="spot-tag">热门</view>
						<view class="spot-info">
							<view class="spot-name">{{item.name}}</view>
							<view class="spot-desc">{{item.description || '暂无描述'}}</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 底部版权信息 - 简洁不突兀 -->
		<view class="footer">
			<text>© 2024 景区智能助理</text>
		</view>

		<!-- 底部安全区域 -->
		<safe-area></safe-area>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				warnings: [],
				spots: []
			}
		},
		onLoad() {
			this.loadWarnings();
			this.loadSpots();
		},
		methods: {
			// 加载预警信息
			loadWarnings() {
				uni.request({
					url: this.$baseUrl + '/api/getWarnings',
					method: 'GET',
					success: (res) => {
						if (res.data.code === 1) {
							this.warnings = res.data.data.list.slice(0, 3); // 只显示前3条
						}
					}
				});
			},

			// 加载景点信息
			loadSpots() {
				uni.request({
					url: this.$baseUrl + '/api/getScenicSpots',
					method: 'GET',
					success: (res) => {
						if (res.data.code === 1) {
							this.spots = res.data.data.list.slice(0, 3); // 只显示前3条
						}
					}
				});
			},

			// 页面导航
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},

			// 导航到景点详情
			navigateToSpot(id) {
				uni.navigateTo({
					url: '/pages/scenicspot/detail?id=' + id
				});
			},

			// 获取预警类型图标
			getTypeIcon(type) {
				switch (type) {
					case 'crowd': return 'cuIcon-group';
					case 'weather': return 'cuIcon-cloudy';
					case 'traffic': return 'cuIcon-car';
					default: return 'cuIcon-warn';
				}
			},

			// 格式化日期
			formatDate(timestamp) {
				if (!timestamp) return '未知时间';

				const date = new Date(timestamp * 1000);
				return date.getFullYear() + '-' +
					   this.padZero(date.getMonth() + 1) + '-' +
					   this.padZero(date.getDate()) + ' ' +
					   this.padZero(date.getHours()) + ':' +
					   this.padZero(date.getMinutes());
			},

			// 补零
			padZero(num) {
				return num < 10 ? '0' + num : num;
			}
		}
	}
</script>

<style>
	/* 页面容器 */
	.page {
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	/* 轮播图样式 */
	.home-swiper {
		height: 360rpx;
		position: relative;
	}

	.home-swiper image {
		width: 100%;
		height: 100%;
	}

	.swiper-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.6));
		display: flex;
		align-items: flex-end;
		padding: 30rpx;
	}

	.swiper-content {
		width: 100%;
	}

	.swiper-title {
		color: #ffffff;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.swiper-desc {
		color: rgba(255,255,255,0.9);
		font-size: 24rpx;
	}

	/* 功能区样式 */
	.feature-section {
		padding: 30rpx 20rpx;
		background-color: #ffffff;
		border-radius: 20rpx 20rpx 0 0;
		margin-top: -20rpx;
		position: relative;
		z-index: 1;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		padding-left: 10rpx;
		position: relative;
	}

	.section-title::before {
		content: "";
		position: absolute;
		left: 0;
		top: 8rpx;
		height: 32rpx;
		width: 6rpx;
		background-color: #0081ff;
		border-radius: 3rpx;
	}

	.feature-grid {
		display: flex;
		flex-wrap: wrap;
	}

	.feature-item {
		width: 25%;
		padding: 20rpx 10rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.feature-icon {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 15rpx;
		box-shadow: 0 10rpx 20rpx rgba(0,0,0,0.05);
	}

	.feature-icon text {
		font-size: 50rpx;
		color: #ffffff;
	}

	.bg-blue {
		background-color: #0081ff;
	}

	.bg-orange {
		background-color: #f37b1d;
	}

	.bg-green {
		background-color: #39b54a;
	}

	.bg-purple {
		background-color: #6739b6;
	}

	.feature-info {
		text-align: center;
	}

	.feature-name {
		font-size: 26rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 5rpx;
	}

	.feature-desc {
		font-size: 22rpx;
		color: #999999;
	}

	/* 预警区样式 */
	.warning-section {
		margin: 20rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.05);
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.section-title text {
		margin-right: 10rpx;
	}

	.section-more {
		font-size: 24rpx;
		color: #0081ff;
		display: flex;
		align-items: center;
	}

	.section-more text {
		margin-left: 5rpx;
	}

	.warning-list {
		border-radius: 8rpx;
		overflow: hidden;
	}

	.warning-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
		position: relative;
	}

	.warning-item:last-child {
		border-bottom: none;
	}

	.warning-item.level-info {
		border-left: 6rpx solid #0081ff;
	}

	.warning-item.level-warning {
		border-left: 6rpx solid #fbbd08;
	}

	.warning-item.level-danger {
		border-left: 6rpx solid #e54d42;
	}

	.warning-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #f8f8f8;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}

	.warning-icon text {
		font-size: 40rpx;
		color: #666666;
	}

	.level-info .warning-icon text {
		color: #0081ff;
	}

	.level-warning .warning-icon text {
		color: #fbbd08;
	}

	.level-danger .warning-icon text {
		color: #e54d42;
	}

	.warning-content {
		flex: 1;
	}

	.warning-title {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 8rpx;
	}

	.warning-time {
		font-size: 22rpx;
		color: #999999;
	}

	.warning-arrow {
		color: #cccccc;
		font-size: 24rpx;
	}

	/* 景点区样式 */
	.spot-section {
		margin: 20rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.05);
	}

	.spot-scroll {
		width: 100%;
		white-space: nowrap;
	}

	.spot-list {
		padding: 10rpx 0;
		display: flex;
	}

	.spot-item {
		width: 280rpx;
		margin-right: 20rpx;
		border-radius: 12rpx;
		overflow: hidden;
		background-color: #ffffff;
		box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.05);
		display: inline-block;
		position: relative;
	}

	.spot-image {
		width: 100%;
		height: 180rpx;
	}

	.spot-tag {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		background-color: #0081ff;
		color: #ffffff;
		font-size: 20rpx;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
	}

	.spot-info {
		padding: 15rpx;
	}

	.spot-name {
		font-size: 26rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 8rpx;
		white-space: normal;
	}

	.spot-desc {
		font-size: 22rpx;
		color: #999999;
		white-space: normal;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		height: 60rpx;
	}

	/* 底部样式 */
	.footer {
		text-align: center;
		padding: 30rpx 0;
		color: #999999;
		font-size: 24rpx;
	}
</style>
