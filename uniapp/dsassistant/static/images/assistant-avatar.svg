<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Assistant Avatar</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="assistantGradient">
            <stop stop-color="#10B981" offset="0%"></stop>
            <stop stop-color="#059669" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle fill="url(#assistantGradient)" cx="100" cy="100" r="100"></circle>
        <g transform="translate(50, 50)">
            <!-- Robot head -->
            <rect x="20" y="10" width="60" height="70" rx="10" fill="#FFFFFF"/>
            
            <!-- Robot eyes -->
            <circle cx="40" cy="40" r="8" fill="#059669"/>
            <circle cx="60" cy="40" r="8" fill="#059669"/>
            
            <!-- Robot antenna -->
            <rect x="45" y="0" width="10" height="15" rx="5" fill="#FFFFFF"/>
            <circle cx="50" cy="0" r="5" fill="#FFFFFF"/>
            
            <!-- Robot mouth -->
            <rect x="35" y="60" width="30" height="5" rx="2.5" fill="#059669"/>
            
            <!-- Robot body -->
            <path d="M30,80 L70,80 L80,100 L20,100 Z" fill="#FFFFFF"/>
        </g>
    </g>
</svg>
