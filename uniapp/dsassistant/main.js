import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 导入全局组件
import SafeArea from './components/SafeArea.vue'

// 注册全局组件
Vue.component('safe-area', SafeArea)

// 全局配置
Vue.config.productionTip = false
Vue.prototype.$baseUrl = 'https://fa.localhost/addons/dsassistant' // 替换为实际的API地址

// 全局混入
Vue.mixin({
  methods: {
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '未知时间';

      const date = new Date(timestamp * 1000);
      return date.getFullYear() + '-' +
             this.padZero(date.getMonth() + 1) + '-' +
             this.padZero(date.getDate()) + ' ' +
             this.padZero(date.getHours()) + ':' +
             this.padZero(date.getMinutes());
    },

    // 补零
    padZero(num) {
      return num < 10 ? '0' + num : num;
    }
  }
})

App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import SafeArea from './components/SafeArea.vue'

export function createApp() {
  const app = createSSRApp(App)

  // 注册全局组件
  app.component('safe-area', SafeArea)

  // 全局配置
  app.config.globalProperties.$baseUrl = 'http://fa.localhost/addons/dsassistant' // 替换为实际的API地址

  return {
    app
  }
}
// #endif