/**
 * 聊天服务
 * 处理聊天相关的逻辑
 */
import {marked} from 'marked';

class ChatService {
	/**
	 * 构造函数
	 * @param {Object} options 配置项
	 */
	constructor(options = {}) {
		this.baseUrl = options.baseUrl || '';
		this.userId = options.userId || '';
		this.deviceId = options.deviceId || '';
		this.sessionId = options.sessionId || '';
		this.platform = options.platform || 'miniapp';
	}

	/**
	 * 发送聊天消息
	 * @param {String} message 消息内容
	 * @param {Function} onSuccess 成功回调
	 * @param {Function} onFail 失败回调
	 */
	sendMessage(message, onSuccess, onFail) {
		uni.request({
			url: this.baseUrl + '/api/chat',
			method: 'POST',
			data: {
				question: message,
				user_id: this.userId,
				device_id: this.deviceId,
				session_id: this.sessionId,
				platform: this.platform
			},
			success: (res) => {
				if (res.data.code === 1) {
					// 检查是否有函数调用
					if (res.data.data.functionCalls && Array.isArray(res.data.data.functionCalls)) {
						// 返回包含函数调用的响应
						onSuccess && onSuccess({
							text: res.data.data.answer,
							functionCalls: res.data.data.functionCalls
						});
					} else {
						// 返回普通文本响应
						onSuccess && onSuccess({
							text: res.data.data.answer,
							functionCalls: []
						});
					}
				} else {
					onFail && onFail('抱歉，出现了一些问题：' + res.data.msg);
				}
			},
			fail: () => {
				onFail && onFail('抱歉，网络连接出现问题，请稍后再试。');
			}
		});
	}

	/**
	 * 解析Markdown
	 * @param {String} text Markdown文本
	 * @return {String} 解析后的HTML
	 */
	parseMarkdown(text) {
		if (!text) return '';

		try {
			// 使用marked.js解析Markdown
			let html = marked(text);

			// 处理HTML以适应小程序rich-text组件
			// 为HTML元素添加类名，以便应用样式
			html = html
				.replace(/<h1/g, '<h1 class="markdown-h1"')
				.replace(/<h2/g, '<h2 class="markdown-h2"')
				.replace(/<h3/g, '<h3 class="markdown-h3"')
				.replace(/<h4/g, '<h4 class="markdown-h4"')
				.replace(/<h5/g, '<h5 class="markdown-h5"')
				.replace(/<h6/g, '<h6 class="markdown-h6"')
				.replace(/<p/g, '<p class="markdown-p"')
				.replace(/<ul/g, '<ul class="markdown-ul"')
				.replace(/<ol/g, '<ol class="markdown-ol"')
				.replace(/<li/g, '<li class="markdown-li"')
				.replace(/<blockquote/g, '<blockquote class="markdown-blockquote"')
				.replace(/<pre/g, '<pre class="markdown-pre"')
				.replace(/<code/g, '<code class="markdown-code"')
				.replace(/<a/g, '<a class="markdown-a"')
				.replace(/<img/g, '<img class="markdown-img"')
				.replace(/<table/g, '<table class="markdown-table"')
				.replace(/<th/g, '<th class="markdown-th"')
				.replace(/<td/g, '<td class="markdown-td"');

			return html;
		} catch (error) {
			console.error('Markdown解析错误:', error);
			return text;
		}
	}

	/**
	 * 格式化时间
	 * @param {Date} date 日期对象
	 * @return {String} 格式化后的时间字符串
	 */
	formatTime(date) {
		const hour = this.padZero(date.getHours());
		const minute = this.padZero(date.getMinutes());
		return hour + ':' + minute;
	}

	/**
	 * 补零
	 * @param {Number} num 数字
	 * @return {String} 补零后的字符串
	 */
	padZero(num) {
		return num < 10 ? '0' + num : num;
	}
}

export default ChatService;
