"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "FunctionCard",
  props: {
    functionName: {
      type: String,
      required: true
    },
    parameters: {
      type: Object,
      required: true
    }
  },
  computed: {
    iconSrc() {
      const icons = {
        "showOnMap": "/static/images/map-icon.svg",
        "showScenicSpotDetails": "/static/images/info-icon.svg"
      };
      return icons[this.functionName] || "/static/images/default-icon.svg";
    },
    title() {
      if (this.functionName === "showOnMap") {
        return `${this.parameters.spotName}的位置`;
      } else if (this.functionName === "showScenicSpotDetails") {
        return `${this.parameters.spotName}详情`;
      }
      return "查看详情";
    },
    description() {
      if (this.functionName === "showOnMap") {
        return "点击查看地图位置";
      } else if (this.functionName === "showScenicSpotDetails") {
        return "点击查看景点详细信息";
      }
      return "点击查看";
    },
    actionText() {
      const actions = {
        "showOnMap": "查看地图",
        "showScenicSpotDetails": "查看详情"
      };
      return actions[this.functionName] || "查看";
    }
  },
  methods: {
    handleCardTap() {
      if (this.functionName === "showOnMap") {
        common_vendor.index.navigateTo({
          url: `/pages/map/map?spotId=${this.parameters.spotId || ""}&spotName=${this.parameters.spotName || ""}&latitude=${this.parameters.latitude || ""}&longitude=${this.parameters.longitude || ""}`
        });
      } else if (this.functionName === "showScenicSpotDetails") {
        common_vendor.index.navigateTo({
          url: `/pages/scenic/detail?spotId=${this.parameters.spotId || ""}&spotName=${this.parameters.spotName || ""}`
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $options.iconSrc,
    b: common_vendor.t($options.title),
    c: common_vendor.t($options.description),
    d: common_vendor.t($options.actionText),
    e: common_vendor.o((...args) => $options.handleCardTap && $options.handleCardTap(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/chat/FunctionCard.js.map
