"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const FunctionCard = () => "./FunctionCard.js";
const _sfc_main = {
  name: "ChatMessage",
  components: {
    FunctionCard
  },
  props: {
    isUser: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: ""
    },
    parsedMessage: {
      type: String,
      default: ""
    },
    time: {
      type: String,
      default: ""
    },
    functionCalls: {
      type: Array,
      default: () => []
    }
  }
};
if (!Array) {
  const _component_function_card = common_vendor.resolveComponent("function-card");
  _component_function_card();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$props.isUser
  }, !$props.isUser ? {
    b: common_assets._imports_0$3
  } : {}, {
    c: $props.isUser
  }, $props.isUser ? {
    d: common_vendor.t($props.message)
  } : common_vendor.e({
    e: $props.parsedMessage
  }, $props.parsedMessage ? {
    f: $props.parsedMessage
  } : {
    g: common_vendor.t($props.message)
  }, {
    h: $props.functionCalls && $props.functionCalls.length > 0
  }, $props.functionCalls && $props.functionCalls.length > 0 ? {
    i: common_vendor.f($props.functionCalls, (call, index, i0) => {
      return {
        a: index,
        b: "43f13332-0-" + i0,
        c: common_vendor.p({
          ["function-name"]: call.name,
          parameters: call.parameters
        })
      };
    })
  } : {}), {
    j: common_vendor.t($props.time),
    k: $props.isUser
  }, $props.isUser ? {
    l: common_assets._imports_1$2
  } : {}, {
    m: common_vendor.n($props.isUser ? "chat-right" : "chat-left")
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/chat/ChatMessage.js.map
