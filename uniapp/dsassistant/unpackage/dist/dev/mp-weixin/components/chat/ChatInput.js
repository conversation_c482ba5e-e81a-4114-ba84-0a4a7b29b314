"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "ChatInput",
  props: {
    placeholder: {
      type: String,
      default: "请输入您的问题..."
    }
  },
  data() {
    return {
      inputValue: ""
    };
  },
  methods: {
    sendMessage() {
      if (!this.inputValue.trim())
        return;
      this.$emit("send", this.inputValue);
      this.inputValue = "";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.placeholder,
    b: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    c: $data.inputValue,
    d: common_vendor.o(($event) => $data.inputValue = $event.detail.value),
    e: common_vendor.n($data.inputValue.trim() ? "active" : ""),
    f: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/chat/ChatInput.js.map
