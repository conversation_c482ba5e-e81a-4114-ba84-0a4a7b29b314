"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "CategoryTabs",
  props: {
    categories: {
      type: Array,
      default: () => []
    },
    activeCategory: {
      type: String,
      default: "all"
    }
  },
  methods: {
    onCategoryTap(category) {
      this.$emit("category-change", category);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.categories, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: index,
        c: $props.activeCategory === item.value ? 1 : "",
        d: common_vendor.o(($event) => $options.onCategoryTap(item.value), index)
      };
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/guide/CategoryTabs.js.map
