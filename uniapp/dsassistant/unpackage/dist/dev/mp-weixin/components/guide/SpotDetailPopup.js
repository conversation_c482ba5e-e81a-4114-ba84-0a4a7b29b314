"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "SpotDetailPopup",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    spot: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    onClose() {
      this.$emit("close");
    },
    onDetailTap() {
      this.$emit("detail-tap", this.spot.id);
    },
    onNavTap() {
      this.$emit("nav-tap", this.spot);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.show
  }, $props.show ? common_vendor.e({
    b: common_vendor.o((...args) => $options.onClose && $options.onClose(...args)),
    c: common_vendor.t($props.spot.name),
    d: common_vendor.o((...args) => $options.onClose && $options.onClose(...args)),
    e: $props.spot.cover || "/static/images/no-image.jpg",
    f: common_vendor.t($props.spot.description || "暂无描述"),
    g: common_vendor.t($props.spot.opening_hours || "暂无开放时间信息"),
    h: common_vendor.t($props.spot.ticket_price || "暂无票价信息"),
    i: $props.spot.address
  }, $props.spot.address ? {
    j: common_vendor.t($props.spot.address)
  } : {}, {
    k: common_vendor.o((...args) => $options.onDetailTap && $options.onDetailTap(...args)),
    l: common_vendor.o((...args) => $options.onNavTap && $options.onNavTap(...args))
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/guide/SpotDetailPopup.js.map
