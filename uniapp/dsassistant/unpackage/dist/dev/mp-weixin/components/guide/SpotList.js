"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const SpotItem = () => "./SpotItem.js";
const _sfc_main = {
  name: "SpotList",
  components: {
    SpotItem
  },
  props: {
    spots: {
      type: Array,
      default: () => []
    },
    spotTypes: {
      type: Array,
      default: () => []
    },
    hasMore: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    refreshing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      scrollTop: 0,
      showBackToTop: false,
      scrollThreshold: 300,
      // 滚动多少距离显示返回顶部按钮
      scrollTimer: null
    };
  },
  methods: {
    onLoadMore() {
      this.$emit("load-more");
    },
    onRefresh() {
      this.$emit("refresh");
    },
    onSpotTap(spot) {
      this.$emit("spot-tap", spot);
    },
    onDetailTap(id) {
      this.$emit("detail-tap", id);
    },
    onNavTap(spot) {
      this.$emit("nav-tap", spot);
    },
    onResetFilter() {
      this.$emit("reset-filter");
    },
    // 滚动事件处理
    onScroll(e) {
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      this.scrollTimer = setTimeout(() => {
        const scrollTop = e.detail.scrollTop;
        this.showBackToTop = scrollTop > this.scrollThreshold;
      }, 100);
    },
    // 滚动到顶部
    scrollToTop() {
      this.scrollTop = 0;
      setTimeout(() => {
        this.scrollTop = -1;
      }, 300);
    }
  }
};
if (!Array) {
  const _component_spot_item = common_vendor.resolveComponent("spot-item");
  _component_spot_item();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($props.spots, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.o($options.onSpotTap, index),
        c: common_vendor.o($options.onDetailTap, index),
        d: common_vendor.o($options.onNavTap, index),
        e: "7eed8802-0-" + i0,
        f: common_vendor.p({
          spot: item,
          ["spot-types"]: $props.spotTypes
        })
      };
    }),
    b: $props.hasMore && $props.spots.length > 0
  }, $props.hasMore && $props.spots.length > 0 ? {} : {}, {
    c: $props.spots.length === 0 && !$props.loading
  }, $props.spots.length === 0 && !$props.loading ? {
    d: common_assets._imports_0$1,
    e: common_vendor.o((...args) => $options.onResetFilter && $options.onResetFilter(...args))
  } : {}, {
    f: common_vendor.o((...args) => $options.onLoadMore && $options.onLoadMore(...args)),
    g: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    h: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args)),
    i: $props.refreshing,
    j: $data.scrollTop,
    k: $data.showBackToTop
  }, $data.showBackToTop ? {
    l: common_vendor.o((...args) => $options.scrollToTop && $options.scrollToTop(...args))
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/guide/SpotList.js.map
