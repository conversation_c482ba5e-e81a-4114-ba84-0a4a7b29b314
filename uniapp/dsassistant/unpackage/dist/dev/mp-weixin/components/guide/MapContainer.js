"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "MapContainer",
  props: {
    latitude: {
      type: [Number, String],
      required: true,
      validator: function(value) {
        return !isNaN(Number(value));
      }
    },
    longitude: {
      type: [Number, String],
      required: true,
      validator: function(value) {
        return !isNaN(Number(value));
      }
    },
    scale: {
      type: Number,
      default: 15
    },
    markers: {
      type: Array,
      default: () => []
    },
    isFullscreen: {
      type: Boolean,
      default: false
    },
    mapHeight: {
      type: String,
      default: "400rpx"
    }
  },
  data() {
    return {
      mapContext: null
    };
  },
  mounted() {
    this.mapContext = common_vendor.index.createMapContext("map");
  },
  methods: {
    onMarkerTap(e) {
      this.$emit("marker-tap", e);
    },
    onCalloutTap(e) {
      this.$emit("callout-tap", e);
    },
    onMapRegionChange(e) {
      this.$emit("region-change", e);
    },
    onZoomIn() {
      this.$emit("zoom-in");
    },
    onZoomOut() {
      this.$emit("zoom-out");
    },
    onResetMapView() {
      this.$emit("reset-view");
    },
    onLocateUser() {
      this.$emit("locate-user");
    },
    onEnterFullscreen() {
      this.$emit("enter-fullscreen");
    },
    onExitFullscreen() {
      this.$emit("exit-fullscreen");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: Number($props.latitude),
    b: Number($props.longitude),
    c: $props.markers,
    d: $props.scale,
    e: common_vendor.o((...args) => $options.onMarkerTap && $options.onMarkerTap(...args)),
    f: common_vendor.o((...args) => $options.onCalloutTap && $options.onCalloutTap(...args)),
    g: common_vendor.o((...args) => $options.onMapRegionChange && $options.onMapRegionChange(...args)),
    h: common_vendor.o((...args) => $options.onZoomIn && $options.onZoomIn(...args)),
    i: common_vendor.o((...args) => $options.onZoomOut && $options.onZoomOut(...args)),
    j: common_vendor.o((...args) => $options.onResetMapView && $options.onResetMapView(...args)),
    k: common_vendor.o((...args) => $options.onLocateUser && $options.onLocateUser(...args)),
    l: $props.isFullscreen
  }, $props.isFullscreen ? {
    m: common_vendor.o((...args) => $options.onExitFullscreen && $options.onExitFullscreen(...args))
  } : {}, {
    n: !$props.isFullscreen
  }, !$props.isFullscreen ? {
    o: common_vendor.o((...args) => $options.onEnterFullscreen && $options.onEnterFullscreen(...args))
  } : {}, {
    p: $props.mapHeight
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/guide/MapContainer.js.map
