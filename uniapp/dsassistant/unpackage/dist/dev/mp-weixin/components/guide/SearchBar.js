"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "SearchBar",
  props: {
    keyword: {
      type: String,
      default: ""
    }
  },
  methods: {
    onInput(e) {
      this.$emit("input", e.detail.value);
    },
    onSearch() {
      this.$emit("search");
    },
    onClear() {
      this.$emit("clear");
    },
    onFilter() {
      this.$emit("filter");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.keyword,
    b: common_vendor.o((...args) => $options.onInput && $options.onInput(...args)),
    c: common_vendor.o((...args) => $options.onSearch && $options.onSearch(...args)),
    d: $props.keyword
  }, $props.keyword ? {
    e: common_vendor.o((...args) => $options.onClear && $options.onClear(...args))
  } : {}, {
    f: common_vendor.o((...args) => $options.onFilter && $options.onFilter(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/guide/SearchBar.js.map
