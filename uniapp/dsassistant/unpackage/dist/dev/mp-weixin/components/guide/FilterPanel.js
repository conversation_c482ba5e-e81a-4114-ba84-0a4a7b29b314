"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "FilterPanel",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    spotTypes: {
      type: Array,
      default: () => []
    },
    selectedType: {
      type: String,
      default: "all"
    },
    sortBy: {
      type: String,
      default: "distance"
    }
  },
  data() {
    return {
      localSelectedType: this.selectedType,
      localSortBy: this.sortBy
    };
  },
  watch: {
    selectedType(val) {
      this.localSelectedType = val;
    },
    sortBy(val) {
      this.localSortBy = val;
    }
  },
  methods: {
    onSelectType(type) {
      this.localSelectedType = type;
      this.$emit("update:selectedType", type);
    },
    onSelectSort(sort) {
      this.localSortBy = sort;
      this.$emit("update:sortBy", sort);
    },
    onReset() {
      this.$emit("reset");
    },
    onApply() {
      this.$emit("apply");
    },
    onClose() {
      this.$emit("close");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.show
  }, $props.show ? {
    b: common_vendor.o((...args) => $options.onClose && $options.onClose(...args)),
    c: common_vendor.f($props.spotTypes, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: index,
        c: $props.selectedType === item.value ? 1 : "",
        d: common_vendor.o(($event) => $options.onSelectType(item.value), index)
      };
    }),
    d: $props.sortBy === "distance" ? 1 : "",
    e: common_vendor.o(($event) => $options.onSelectSort("distance")),
    f: $props.sortBy === "popularity" ? 1 : "",
    g: common_vendor.o(($event) => $options.onSelectSort("popularity")),
    h: common_vendor.o((...args) => $options.onReset && $options.onReset(...args)),
    i: common_vendor.o((...args) => $options.onApply && $options.onApply(...args))
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/guide/FilterPanel.js.map
