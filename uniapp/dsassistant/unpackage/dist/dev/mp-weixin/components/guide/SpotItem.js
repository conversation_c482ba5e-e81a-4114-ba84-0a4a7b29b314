"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "SpotItem",
  props: {
    spot: {
      type: Object,
      required: true
    },
    spotTypes: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    typeLabel() {
      const typeObj = this.spotTypes.find((item) => item.value === this.spot.type);
      return typeObj ? typeObj.label : "";
    }
  },
  methods: {
    onSpotTap() {
      this.$emit("spot-tap", this.spot);
    },
    onDetailTap() {
      this.$emit("detail-tap", this.spot.id);
    },
    onNavTap() {
      this.$emit("nav-tap", this.spot);
    },
    formatDistance(distance) {
      if (distance < 1e3) {
        return Math.round(distance) + "m";
      } else {
        return (distance / 1e3).toFixed(1) + "km";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.spot.cover || "/static/images/no-image.jpg",
    b: $props.spot.type
  }, $props.spot.type ? {
    c: common_vendor.t($options.typeLabel)
  } : {}, {
    d: common_vendor.t($props.spot.name),
    e: $props.spot.distance
  }, $props.spot.distance ? {
    f: common_vendor.t($options.formatDistance($props.spot.distance))
  } : {}, {
    g: common_vendor.t($props.spot.description || "暂无描述"),
    h: common_vendor.t($props.spot.opening_hours || "暂无开放时间"),
    i: common_vendor.t($props.spot.ticket_price || "暂无票价信息"),
    j: common_vendor.o((...args) => $options.onDetailTap && $options.onDetailTap(...args)),
    k: common_vendor.o((...args) => $options.onNavTap && $options.onNavTap(...args)),
    l: common_vendor.o((...args) => $options.onSpotTap && $options.onSpotTap(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/guide/SpotItem.js.map
