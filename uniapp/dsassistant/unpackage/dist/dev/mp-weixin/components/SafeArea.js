"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "SafeArea",
  data() {
    return {
      safeAreaHeight: 0
    };
  },
  created() {
    const systemInfo = common_vendor.index.getSystemInfoSync();
    if (systemInfo.safeAreaInsets) {
      this.safeAreaHeight = systemInfo.safeAreaInsets.bottom;
    } else if (systemInfo.safeArea) {
      const safeAreaBottom = systemInfo.safeArea.bottom;
      const screenHeight = systemInfo.screenHeight;
      this.safeAreaHeight = screenHeight - safeAreaBottom;
    } else {
      this.safeAreaHeight = 0;
    }
    if (systemInfo.platform === "ios" && this.safeAreaHeight < 20) {
      this.safeAreaHeight = 20;
    }
    common_vendor.index.__f__("log", "at components/SafeArea.vue:37", "SafeArea height:", this.safeAreaHeight);
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.safeAreaHeight + "px"
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/SafeArea.js.map
