"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/chat/chat.js";
  "./pages/warning/warning.js";
  "./pages/guide/guide.js";
  "./pages/scenicspot/detail.js";
  "./pages/lostfound/lostfound.js";
  "./pages/lostfound/detail.js";
  "./pages/lostfound/submit.js";
  "./pages/example/safe-area-example.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:4", "App Launch");
    this.checkLoginStatus();
    this.getSystemInfo();
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:13", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:16", "App Hide");
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const token = common_vendor.index.getStorageSync("token");
      if (token) {
        common_vendor.index.request({
          url: this.$baseUrl + "/api/checkToken",
          method: "POST",
          data: { token },
          success: (res) => {
            if (res.data.code !== 1) {
              common_vendor.index.removeStorageSync("token");
              common_vendor.index.removeStorageSync("userInfo");
            }
          }
        });
      }
    },
    // 获取系统信息
    getSystemInfo() {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          this.globalData.systemInfo = res;
          this.globalData.safeAreaBottom = res.safeAreaInsets ? res.safeAreaInsets.bottom : 0;
        }
      });
    }
  },
  globalData: {
    systemInfo: null,
    safeAreaBottom: 0,
    userInfo: null
  }
};
const SafeArea = () => "./components/SafeArea.js";
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.component("safe-area", SafeArea);
  app.config.globalProperties.$baseUrl = "http://fa.localhost/addons/dsassistant";
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
