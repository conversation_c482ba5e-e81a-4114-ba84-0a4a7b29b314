<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>User Avatar</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="userGradient">
            <stop stop-color="#6366F1" offset="0%"></stop>
            <stop stop-color="#4F46E5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle fill="url(#userGradient)" cx="100" cy="100" r="100"></circle>
        <g transform="translate(40, 40)">
            <circle fill="#FFFFFF" cx="60" cy="45" r="35"></circle>
            <path d="M120,140 C120,106.863 93.137,80 60,80 C26.863,80 0,106.863 0,140" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>
