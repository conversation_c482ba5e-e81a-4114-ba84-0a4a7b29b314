"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      activeTab: "all",
      keyword: "",
      items: [],
      page: 1,
      limit: 10,
      loading: false,
      hasMore: true,
      refreshing: false,
      categories: [],
      selectedCategory: ""
    };
  },
  onLoad() {
    this.loadCategories();
    this.loadItems();
  },
  onPullDownRefresh() {
    this.refresh();
    setTimeout(() => {
      common_vendor.index.stopPullDownRefresh();
    }, 1e3);
  },
  methods: {
    // 加载分类列表
    loadCategories() {
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getLostFoundCategories",
        method: "GET",
        success: (res) => {
          if (res.data.code === 1) {
            this.categories = [
              { id: "", category: "全部" },
              ...res.data.data
            ];
          }
        }
      });
    },
    // 切换标签
    changeTab(tab) {
      if (this.activeTab === tab)
        return;
      this.activeTab = tab;
      this.refresh();
    },
    // 选择分类
    selectCategory(categoryId) {
      if (this.selectedCategory === categoryId)
        return;
      this.selectedCategory = categoryId;
      this.refresh();
    },
    // 清除关键词
    clearKeyword() {
      this.keyword = "";
      this.refresh();
    },
    // 搜索
    search() {
      this.refresh();
    },
    // 下拉刷新
    onRefresh() {
      this.refreshing = true;
      this.refresh();
      setTimeout(() => {
        this.refreshing = false;
      }, 1e3);
    },
    // 刷新数据
    refresh() {
      this.page = 1;
      this.items = [];
      this.hasMore = true;
      this.loadItems();
    },
    // 加载更多
    loadMore() {
      if (this.loading || !this.hasMore)
        return;
      this.page++;
      this.loadItems();
    },
    // 加载物品列表
    loadItems() {
      if (this.loading && !this.refreshing)
        return;
      this.loading = true;
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getLostFound",
        method: "GET",
        data: {
          page: this.page,
          limit: this.limit,
          type: this.activeTab === "all" ? "" : this.activeTab,
          keyword: this.keyword,
          category_id: this.selectedCategory
        },
        success: (res) => {
          this.loading = false;
          if (res.data.code === 1) {
            const list = res.data.data.list;
            if (this.page === 1) {
              this.items = list;
            } else {
              this.items = [...this.items, ...list];
            }
            this.hasMore = list.length === this.limit;
          } else {
            common_vendor.index.showToast({
              title: "加载失败：" + res.data.msg,
              icon: "none"
            });
          }
        },
        fail: () => {
          this.loading = false;
          common_vendor.index.showToast({
            title: "网络错误，请稍后再试",
            icon: "none"
          });
        }
      });
    },
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "pending":
          return "处理中";
        case "processed":
          return "已处理";
        case "closed":
          return "已关闭";
        default:
          return "未知";
      }
    },
    // 导航到详情页
    navigateToDetail(id) {
      common_vendor.index.navigateTo({
        url: "/pages/lostfound/detail?id=" + id
      });
    },
    // 导航到提交页
    navigateToSubmit() {
      common_vendor.index.navigateTo({
        url: "/pages/lostfound/submit"
      });
    },
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp)
        return "未知时间";
      const date = new Date(timestamp * 1e3);
      return date.getFullYear() + "-" + this.padZero(date.getMonth() + 1) + "-" + this.padZero(date.getDate()) + " " + this.padZero(date.getHours()) + ":" + this.padZero(date.getMinutes());
    },
    // 补零
    padZero(num) {
      return num < 10 ? "0" + num : num;
    }
  }
};
if (!Array) {
  const _component_safe_area = common_vendor.resolveComponent("safe-area");
  _component_safe_area();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.activeTab === "all" ? 1 : "",
    b: common_vendor.o(($event) => $options.changeTab("all")),
    c: $data.activeTab === "lost" ? 1 : "",
    d: common_vendor.o(($event) => $options.changeTab("lost")),
    e: $data.activeTab === "found" ? 1 : "",
    f: common_vendor.o(($event) => $options.changeTab("found")),
    g: common_vendor.o((...args) => $options.search && $options.search(...args)),
    h: $data.keyword,
    i: common_vendor.o(($event) => $data.keyword = $event.detail.value),
    j: $data.keyword
  }, $data.keyword ? {
    k: common_vendor.o((...args) => $options.clearKeyword && $options.clearKeyword(...args))
  } : {}, {
    l: common_vendor.o((...args) => $options.search && $options.search(...args)),
    m: $data.categories.length > 0
  }, $data.categories.length > 0 ? {
    n: common_vendor.f($data.categories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.category),
        b: $data.selectedCategory === category.id ? 1 : "",
        c: index,
        d: common_vendor.o(($event) => $options.selectCategory(category.id), index)
      };
    })
  } : {}, {
    o: $data.refreshing
  }, $data.refreshing ? {} : {}, {
    p: $data.items.length > 0
  }, $data.items.length > 0 ? {
    q: common_vendor.f($data.items, (item, index, i0) => {
      return {
        a: item.cover || "/static/images/no-image.jpg",
        b: common_vendor.t(item.type === "lost" ? "寻物" : "招领"),
        c: common_vendor.n(item.type === "lost" ? "lost" : "found"),
        d: common_vendor.t($options.getStatusText(item.status)),
        e: common_vendor.n("status-" + item.status),
        f: common_vendor.t(item.title),
        g: common_vendor.t(item.description || "暂无描述"),
        h: common_vendor.t(item.category || "未分类"),
        i: common_vendor.t(item.location || "未知地点"),
        j: common_vendor.t($options.formatDate(item.createtime)),
        k: index,
        l: common_vendor.o(($event) => $options.navigateToDetail(item.id), index)
      };
    })
  } : !$data.loading ? {
    s: common_assets._imports_0$1,
    t: common_vendor.o((...args) => $options.refresh && $options.refresh(...args))
  } : {}, {
    r: !$data.loading,
    v: $data.loading && !$data.refreshing
  }, $data.loading && !$data.refreshing ? {} : {}, {
    w: !$data.hasMore && $data.items.length > 0
  }, !$data.hasMore && $data.items.length > 0 ? {} : {}, {
    x: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    y: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    z: $data.refreshing,
    A: common_vendor.o((...args) => $options.navigateToSubmit && $options.navigateToSubmit(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/lostfound/lostfound.js.map
