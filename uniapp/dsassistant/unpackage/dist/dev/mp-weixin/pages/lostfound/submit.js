"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      formData: {
        type: "lost",
        title: "",
        description: "",
        category: "",
        location: "",
        contact_name: "",
        contact_phone: "",
        images: []
      },
      imageList: [],
      categories: ["证件", "钱包", "手机", "背包", "钥匙", "其他"]
    };
  },
  methods: {
    // 选择物品类别
    onCategoryChange(e) {
      const index = e.detail.value;
      this.formData.category = this.categories[index];
    },
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 9 - this.imageList.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.imageList = [...this.imageList, ...res.tempFilePaths];
        }
      });
    },
    // 删除图片
    deleteImage(index) {
      this.imageList.splice(index, 1);
    },
    // 提交表单
    submitForm() {
      if (!this.formData.title) {
        this.showToast("请输入标题");
        return;
      }
      if (!this.formData.description) {
        this.showToast("请输入描述");
        return;
      }
      if (!this.formData.contact_name) {
        this.showToast("请输入联系人");
        return;
      }
      if (!this.formData.contact_phone) {
        this.showToast("请输入联系电话");
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      this.uploadImages().then((imageUrls) => {
        this.formData.images = imageUrls;
        common_vendor.index.request({
          url: this.$baseUrl + "/api/submitLostFound",
          method: "POST",
          data: this.formData,
          success: (res) => {
            common_vendor.index.hideLoading();
            if (res.data.code === 1) {
              common_vendor.index.showToast({
                title: "提交成功",
                icon: "success"
              });
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 1500);
            } else {
              this.showToast("提交失败：" + res.data.msg);
            }
          },
          fail: () => {
            common_vendor.index.hideLoading();
            this.showToast("网络错误，请稍后再试");
          }
        });
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.showToast("图片上传失败：" + err);
      });
    },
    // 上传图片
    uploadImages() {
      return new Promise((resolve, reject) => {
        if (this.imageList.length === 0) {
          resolve([]);
          return;
        }
        const uploadTasks = this.imageList.map((path) => {
          return new Promise((resolve2, reject2) => {
            common_vendor.index.uploadFile({
              url: this.$baseUrl + "/api/upload",
              filePath: path,
              name: "file",
              success: (res) => {
                const data = JSON.parse(res.data);
                if (data.code === 1) {
                  resolve2(data.data.url);
                } else {
                  reject2(data.msg);
                }
              },
              fail: (err) => {
                reject2(err.errMsg);
              }
            });
          });
        });
        Promise.all(uploadTasks).then(resolve).catch(reject);
      });
    },
    // 显示提示
    showToast(title) {
      common_vendor.index.showToast({
        title,
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.formData.type === "lost" ? 1 : "",
    b: common_vendor.o(($event) => $data.formData.type = "lost"),
    c: $data.formData.type === "found" ? 1 : "",
    d: common_vendor.o(($event) => $data.formData.type = "found"),
    e: $data.formData.title,
    f: common_vendor.o(($event) => $data.formData.title = $event.detail.value),
    g: $data.formData.description,
    h: common_vendor.o(($event) => $data.formData.description = $event.detail.value),
    i: common_vendor.t($data.formData.category || "请选择物品类别"),
    j: $data.categories,
    k: common_vendor.o((...args) => $options.onCategoryChange && $options.onCategoryChange(...args)),
    l: $data.formData.location,
    m: common_vendor.o(($event) => $data.formData.location = $event.detail.value),
    n: $data.formData.contact_name,
    o: common_vendor.o(($event) => $data.formData.contact_name = $event.detail.value),
    p: $data.formData.contact_phone,
    q: common_vendor.o(($event) => $data.formData.contact_phone = $event.detail.value),
    r: common_vendor.f($data.imageList, (item, index, i0) => {
      return {
        a: item,
        b: common_vendor.o(($event) => $options.deleteImage(index), index),
        c: index
      };
    }),
    s: $data.imageList.length < 9
  }, $data.imageList.length < 9 ? {
    t: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    v: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/lostfound/submit.js.map
