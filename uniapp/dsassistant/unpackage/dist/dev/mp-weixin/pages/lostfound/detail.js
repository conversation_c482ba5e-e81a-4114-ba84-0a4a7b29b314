"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      id: 0,
      item: {
        title: "",
        description: "",
        type: "",
        category: "",
        location: "",
        contact_name: "",
        contact_phone: "",
        images: [],
        status: "",
        createtime: 0
      },
      loading: true,
      loadError: false,
      errorMsg: "加载失败，请重试",
      isFavorite: false,
      descExpanded: false,
      relatedItems: []
    };
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.loadItemDetail();
      this.checkFavoriteStatus();
    } else {
      this.loading = false;
      this.loadError = true;
      this.errorMsg = "参数错误";
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  onShareAppMessage() {
    return {
      title: (this.item.type === "lost" ? "寻物启事：" : "招领启事：") + this.item.title,
      path: "/pages/lostfound/detail?id=" + this.id,
      imageUrl: this.item.images && this.item.images.length > 0 ? this.item.images[0] : ""
    };
  },
  methods: {
    // 加载物品详情
    loadItemDetail() {
      this.loading = true;
      this.loadError = false;
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getLostFoundDetail",
        method: "GET",
        data: {
          id: this.id
        },
        success: (res) => {
          this.loading = false;
          if (res.data.code === 1) {
            this.item = res.data.data.info;
            this.loadRelatedItems();
          } else {
            this.loadError = true;
            this.errorMsg = res.data.msg || "加载失败，请重试";
          }
        },
        fail: () => {
          this.loading = false;
          this.loadError = true;
          this.errorMsg = "网络错误，请稍后再试";
        }
      });
    },
    // 加载相关推荐
    loadRelatedItems() {
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getLostFound",
        method: "GET",
        data: {
          type: this.item.type,
          category: this.item.category,
          exclude_id: this.id,
          limit: 5
        },
        success: (res) => {
          if (res.data.code === 1) {
            this.relatedItems = res.data.data.list || [];
          }
        }
      });
    },
    // 预览图片
    previewImage(index) {
      if (this.item.images && this.item.images.length > 0) {
        common_vendor.index.previewImage({
          current: index,
          urls: this.item.images
        });
      }
    },
    // 拨打电话
    makePhoneCall() {
      if (!this.item.contact_phone) {
        common_vendor.index.showToast({
          title: "未提供联系电话",
          icon: "none"
        });
        return;
      }
      common_vendor.index.makePhoneCall({
        phoneNumber: this.item.contact_phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    },
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      common_vendor.index.getStorage({
        key: "favoriteItems",
        success: (res) => {
          let favorites = res.data || [];
          if (this.isFavorite) {
            if (!favorites.includes(this.id)) {
              favorites.push(this.id);
            }
            common_vendor.index.showToast({
              title: "收藏成功",
              icon: "success"
            });
          } else {
            favorites = favorites.filter((id) => id !== this.id);
            common_vendor.index.showToast({
              title: "已取消收藏",
              icon: "none"
            });
          }
          common_vendor.index.setStorage({
            key: "favoriteItems",
            data: favorites
          });
        },
        fail: () => {
          let favorites = [];
          if (this.isFavorite) {
            favorites.push(this.id);
            common_vendor.index.showToast({
              title: "收藏成功",
              icon: "success"
            });
          }
          common_vendor.index.setStorage({
            key: "favoriteItems",
            data: favorites
          });
        }
      });
    },
    // 检查收藏状态
    checkFavoriteStatus() {
      common_vendor.index.getStorage({
        key: "favoriteItems",
        success: (res) => {
          let favorites = res.data || [];
          this.isFavorite = favorites.includes(this.id);
        }
      });
    },
    // 切换描述展开/收起状态
    toggleDescExpand() {
      this.descExpanded = !this.descExpanded;
    },
    // 跳转到物品详情
    goToItemDetail(id) {
      common_vendor.index.navigateTo({
        url: "/pages/lostfound/detail?id=" + id
      });
    },
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "pending":
          return "处理中";
        case "processed":
          return "已处理";
        case "closed":
          return "已关闭";
        default:
          return "未知";
      }
    },
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp)
        return "未知时间";
      const date = new Date(timestamp * 1e3);
      return date.getFullYear() + "-" + this.padZero(date.getMonth() + 1) + "-" + this.padZero(date.getDate()) + " " + this.padZero(date.getHours()) + ":" + this.padZero(date.getMinutes());
    },
    // 补零
    padZero(num) {
      return num < 10 ? "0" + num : num;
    }
  }
};
if (!Array) {
  const _component_safe_area = common_vendor.resolveComponent("safe-area");
  _component_safe_area();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.loadError ? {
    c: common_assets._imports_0$2,
    d: common_vendor.t($data.errorMsg),
    e: common_vendor.o((...args) => $options.loadItemDetail && $options.loadItemDetail(...args))
  } : common_vendor.e({
    f: $data.item.images && $data.item.images.length > 0
  }, $data.item.images && $data.item.images.length > 0 ? {
    g: common_vendor.f($data.item.images, (img, index, i0) => {
      return {
        a: img,
        b: index,
        c: common_vendor.o(($event) => $options.previewImage(index), index)
      };
    })
  } : {
    h: common_assets._imports_1$1
  }, {
    i: common_vendor.t($data.item.type === "lost" ? "寻物启事" : "招领启事"),
    j: common_vendor.n($data.item.type === "lost" ? "lost" : "found"),
    k: common_vendor.t($options.getStatusText($data.item.status)),
    l: common_vendor.n("status-" + $data.item.status),
    m: common_vendor.t($data.item.title),
    n: common_vendor.t($options.formatDate($data.item.createtime)),
    o: common_vendor.t($data.item.description || "暂无描述"),
    p: $data.descExpanded ? 1 : "",
    q: $data.item.description && $data.item.description.length > 100
  }, $data.item.description && $data.item.description.length > 100 ? common_vendor.e({
    r: common_vendor.t($data.descExpanded ? "收起" : "展开"),
    s: !$data.descExpanded
  }, !$data.descExpanded ? {} : {}, {
    t: common_vendor.o((...args) => $options.toggleDescExpand && $options.toggleDescExpand(...args))
  }) : {}, {
    v: common_vendor.t($data.item.category || "未分类"),
    w: common_vendor.t($data.item.location || "未知地点"),
    x: common_vendor.t($options.formatDate($data.item.createtime)),
    y: common_vendor.t($options.getStatusText($data.item.status)),
    z: common_vendor.t($data.item.contact_name || "未提供"),
    A: $data.item.contact_phone
  }, $data.item.contact_phone ? {
    B: common_vendor.t($data.item.contact_phone),
    C: common_vendor.o((...args) => $options.makePhoneCall && $options.makePhoneCall(...args))
  } : {}, {
    D: $data.relatedItems.length > 0
  }, $data.relatedItems.length > 0 ? {
    E: common_vendor.f($data.relatedItems, (item, index, i0) => {
      return {
        a: item.cover || "/static/images/no-image.jpg",
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.type === "lost" ? "寻物" : "招领"),
        d: common_vendor.n(item.type === "lost" ? "lost" : "found"),
        e: index,
        f: common_vendor.o(($event) => $options.goToItemDetail(item.id), index)
      };
    })
  } : {}), {
    b: $data.loadError,
    F: common_vendor.t($data.isFavorite ? "已收藏" : "收藏"),
    G: $data.isFavorite ? 1 : "",
    H: common_vendor.o((...args) => $options.toggleFavorite && $options.toggleFavorite(...args)),
    I: $data.item.contact_phone
  }, $data.item.contact_phone ? {
    J: common_vendor.o((...args) => $options.makePhoneCall && $options.makePhoneCall(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/lostfound/detail.js.map
