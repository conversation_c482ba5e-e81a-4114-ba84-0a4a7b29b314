"use strict";
const common_vendor = require("../../common/vendor.js");
const safeArea = () => "../../components/SafeArea.js";
const _sfc_main = {
  components: {
    safeArea
  },
  data() {
    return {};
  },
  methods: {}
};
if (!Array) {
  const _component_safe_area = common_vendor.resolveComponent("safe-area");
  _component_safe_area();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {};
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/example/safe-area-example.js.map
