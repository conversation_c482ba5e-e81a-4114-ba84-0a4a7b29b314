"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      warnings: [],
      loading: false,
      refreshing: false,
      currentFilter: "all",
      page: 1,
      pageSize: 10,
      hasMore: true,
      filterOptions: [
        { label: "全部", value: "all" },
        { label: "人流", value: "crowd" },
        { label: "天气", value: "weather" },
        { label: "交通", value: "traffic" }
      ]
    };
  },
  computed: {
    // 根据筛选条件过滤预警信息
    filteredWarnings() {
      if (this.currentFilter === "all") {
        return this.warnings;
      } else {
        return this.warnings.filter((item) => item.type === this.currentFilter);
      }
    }
  },
  onLoad() {
    this.loadWarnings();
  },
  methods: {
    // 加载预警信息
    loadWarnings(reset = true) {
      if (this.loading && !this.refreshing)
        return;
      if (reset) {
        this.page = 1;
        this.hasMore = true;
      }
      this.loading = true;
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getWarnings",
        method: "GET",
        data: {
          page: this.page,
          pageSize: this.pageSize,
          type: this.currentFilter !== "all" ? this.currentFilter : ""
        },
        success: (res) => {
          this.loading = false;
          this.refreshing = false;
          if (res.data.code === 1) {
            const list = res.data.data.list || [];
            list.forEach((item) => {
              item.expanded = false;
            });
            if (reset) {
              this.warnings = list;
            } else {
              this.warnings = [...this.warnings, ...list];
            }
            this.hasMore = list.length >= this.pageSize;
          } else {
            common_vendor.index.showToast({
              title: "加载失败：" + res.data.msg,
              icon: "none"
            });
          }
        },
        fail: () => {
          this.loading = false;
          this.refreshing = false;
          common_vendor.index.showToast({
            title: "网络错误，请稍后再试",
            icon: "none"
          });
        }
      });
    },
    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading)
        return;
      this.page++;
      this.loadWarnings(false);
    },
    // 下拉刷新
    onRefresh() {
      this.refreshing = true;
      this.loadWarnings();
    },
    // 筛选预警
    filterWarnings(type) {
      if (this.currentFilter === type)
        return;
      this.currentFilter = type;
      this.loadWarnings();
    },
    // 切换展开状态
    toggleExpand(index) {
      this.warnings.forEach((item, i) => {
        if (i !== index) {
          this.$set(item, "expanded", false);
        }
      });
      this.$set(this.warnings[index], "expanded", !this.warnings[index].expanded);
    },
    // 分享预警
    shareWarning(item) {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        success: () => {
          common_vendor.index.showToast({
            title: "请点击右上角分享",
            icon: "none"
          });
        }
      });
    },
    // 设置提醒
    setReminder(item) {
      common_vendor.index.showModal({
        title: "设置提醒",
        content: "是否设置此预警的到期提醒？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "提醒已设置",
              icon: "success"
            });
          }
        }
      });
    },
    // 获取类型图标
    getTypeIcon(type) {
      switch (type) {
        case "crowd":
          return "cuIcon-group";
        case "weather":
          return "cuIcon-cloudy";
        case "traffic":
          return "cuIcon-car";
        default:
          return "cuIcon-warn";
      }
    },
    // 获取类型文本
    getTypeText(type) {
      switch (type) {
        case "crowd":
          return "人流预警";
        case "weather":
          return "天气预警";
        case "traffic":
          return "交通预警";
        default:
          return "其他预警";
      }
    },
    // 获取级别文本
    getLevelText(level) {
      switch (level) {
        case "info":
          return "提示";
        case "warning":
          return "警告";
        case "danger":
          return "紧急";
        default:
          return "提示";
      }
    },
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp)
        return "未知时间";
      const date = new Date(timestamp * 1e3);
      return date.getFullYear() + "-" + this.padZero(date.getMonth() + 1) + "-" + this.padZero(date.getDate()) + " " + this.padZero(date.getHours()) + ":" + this.padZero(date.getMinutes());
    },
    // 格式化相对时间
    formatTimeAgo(timestamp) {
      if (!timestamp)
        return "未知时间";
      const now = /* @__PURE__ */ new Date();
      const date = new Date(timestamp * 1e3);
      const diff = Math.floor((now - date) / 1e3);
      if (diff < 60) {
        return "刚刚";
      } else if (diff < 3600) {
        return Math.floor(diff / 60) + "分钟前";
      } else if (diff < 86400) {
        return Math.floor(diff / 3600) + "小时前";
      } else if (diff < 2592e3) {
        return Math.floor(diff / 86400) + "天前";
      } else {
        return this.formatDate(timestamp);
      }
    },
    // 补零
    padZero(num) {
      return num < 10 ? "0" + num : num;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.filterOptions, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: index,
        c: $data.currentFilter === item.value ? 1 : "",
        d: common_vendor.o(($event) => $options.filterWarnings(item.value), index)
      };
    }),
    b: common_vendor.f($options.filteredWarnings, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.getLevelText(item.level)),
        b: common_vendor.n("level-" + item.level),
        c: common_vendor.n($options.getTypeIcon(item.type)),
        d: common_vendor.n("level-" + item.level),
        e: common_vendor.t(item.title),
        f: common_vendor.t($options.getTypeText(item.type)),
        g: common_vendor.t($options.formatTimeAgo(item.createtime)),
        h: item.expanded ? 1 : "",
        i: item.expanded
      }, item.expanded ? {
        j: common_vendor.t(item.content),
        k: common_vendor.t($options.formatDate(item.createtime)),
        l: common_vendor.t($options.formatDate(item.start_time)),
        m: common_vendor.t($options.formatDate(item.end_time)),
        n: common_vendor.o(($event) => $options.shareWarning(item), index),
        o: common_vendor.o(($event) => $options.setReminder(item), index)
      } : {}, {
        p: index,
        q: common_vendor.n("level-" + item.level),
        r: common_vendor.o(($event) => $options.toggleExpand(index), index)
      });
    }),
    c: $data.hasMore && $options.filteredWarnings.length > 0
  }, $data.hasMore && $options.filteredWarnings.length > 0 ? {} : {}, {
    d: $options.filteredWarnings.length === 0 && !$data.loading
  }, $options.filteredWarnings.length === 0 && !$data.loading ? {
    e: common_assets._imports_0$1,
    f: common_vendor.o((...args) => $options.loadWarnings && $options.loadWarnings(...args))
  } : {}, {
    g: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    h: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    i: $data.refreshing,
    j: $data.loading
  }, $data.loading ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/warning/warning.js.map
