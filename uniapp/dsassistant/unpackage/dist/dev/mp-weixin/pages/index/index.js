"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      warnings: [],
      spots: []
    };
  },
  onLoad() {
    this.loadWarnings();
    this.loadSpots();
  },
  methods: {
    // 加载预警信息
    loadWarnings() {
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getWarnings",
        method: "GET",
        success: (res) => {
          if (res.data.code === 1) {
            this.warnings = res.data.data.list.slice(0, 3);
          }
        }
      });
    },
    // 加载景点信息
    loadSpots() {
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getScenicSpots",
        method: "GET",
        success: (res) => {
          if (res.data.code === 1) {
            this.spots = res.data.data.list.slice(0, 3);
          }
        }
      });
    },
    // 页面导航
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 导航到景点详情
    navigateToSpot(id) {
      common_vendor.index.navigateTo({
        url: "/pages/scenicspot/detail?id=" + id
      });
    },
    // 获取预警类型图标
    getTypeIcon(type) {
      switch (type) {
        case "crowd":
          return "cuIcon-group";
        case "weather":
          return "cuIcon-cloudy";
        case "traffic":
          return "cuIcon-car";
        default:
          return "cuIcon-warn";
      }
    },
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp)
        return "未知时间";
      const date = new Date(timestamp * 1e3);
      return date.getFullYear() + "-" + this.padZero(date.getMonth() + 1) + "-" + this.padZero(date.getDate()) + " " + this.padZero(date.getHours()) + ":" + this.padZero(date.getMinutes());
    },
    // 补零
    padZero(num) {
      return num < 10 ? "0" + num : num;
    }
  }
};
if (!Array) {
  const _component_safe_area = common_vendor.resolveComponent("safe-area");
  _component_safe_area();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0,
    b: common_assets._imports_1,
    c: common_assets._imports_2,
    d: common_vendor.o(($event) => $options.navigateTo("/pages/chat/chat")),
    e: common_vendor.o(($event) => $options.navigateTo("/pages/warning/warning")),
    f: common_vendor.o(($event) => $options.navigateTo("/pages/guide/guide")),
    g: common_vendor.o(($event) => $options.navigateTo("/pages/lostfound/lostfound")),
    h: $data.warnings.length > 0
  }, $data.warnings.length > 0 ? {
    i: common_vendor.o(($event) => $options.navigateTo("/pages/warning/warning")),
    j: common_vendor.f($data.warnings, (item, index, i0) => {
      return {
        a: common_vendor.n($options.getTypeIcon(item.type)),
        b: common_vendor.t(item.title),
        c: common_vendor.t($options.formatDate(item.createtime)),
        d: index,
        e: common_vendor.n("level-" + item.level)
      };
    })
  } : {}, {
    k: common_vendor.o(($event) => $options.navigateTo("/pages/guide/guide")),
    l: common_vendor.f($data.spots, (item, index, i0) => {
      return {
        a: item.cover || "/static/images/no-image.jpg",
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.description || "暂无描述"),
        d: index,
        e: common_vendor.o(($event) => $options.navigateToSpot(item.id), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
