"use strict";
const common_vendor = require("../../common/vendor.js");
const SearchBar = () => "../../components/guide/SearchBar.js";
const FilterPanel = () => "../../components/guide/FilterPanel.js";
const MapContainer = () => "../../components/guide/MapContainer.js";
const CategoryTabs = () => "../../components/guide/CategoryTabs.js";
const SpotList = () => "../../components/guide/SpotList.js";
const SpotDetailPopup = () => "../../components/guide/SpotDetailPopup.js";
const LoadingOverlay = () => "../../components/guide/LoadingOverlay.js";
const _sfc_main = {
  components: {
    SearchBar,
    FilterPanel,
    MapContainer,
    CategoryTabs,
    SpotList,
    SpotDetailPopup,
    LoadingOverlay
  },
  data() {
    return {
      // 地图相关
      latitude: 39.90923,
      longitude: 116.397428,
      scale: 15,
      markers: [],
      isMapFullscreen: false,
      mapHeight: "400rpx",
      // 景点数据
      spots: [],
      filteredSpots: [],
      currentSpot: {},
      userLocation: null,
      // 分页加载
      page: 1,
      pageSize: 10,
      hasMore: true,
      loading: false,
      refreshing: false,
      // 搜索和筛选
      searchKeyword: "",
      showFilterPanel: false,
      selectedType: "all",
      sortBy: "distance",
      activeCategory: "all",
      // 景点类型选项
      spotTypes: [],
      // 弹窗控制
      showDetailPopup: false,
      // 触摸事件相关
      touchStartY: 0,
      touchEndY: 0,
      touchThreshold: 50,
      // 触摸阈值，超过这个值才认为是有效的滑动
      // 列表容器高度
      listContainerHeight: "calc(100vh - 110rpx - 400rpx)"
      // 默认高度
    };
  },
  computed: {
    // 根据筛选条件过滤景点
    computedFilteredSpots() {
      if (!this.spots.length)
        return [];
      let result = [...this.spots];
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(
          (spot) => spot.name.toLowerCase().includes(keyword) || spot.description && spot.description.toLowerCase().includes(keyword)
        );
      }
      if (this.selectedType !== "all") {
        result = result.filter((spot) => spot.type === this.selectedType);
      }
      if (this.sortBy === "distance" && this.userLocation) {
        result.sort((a, b) => {
          const distanceA = this.calculateDistance(
            this.userLocation.latitude,
            this.userLocation.longitude,
            a.latitude,
            a.longitude
          );
          const distanceB = this.calculateDistance(
            this.userLocation.latitude,
            this.userLocation.longitude,
            b.latitude,
            b.longitude
          );
          return distanceA - distanceB;
        });
      } else if (this.sortBy === "popularity") {
        result.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
      }
      return result;
    }
  },
  onLoad() {
    this.getUserLocation();
    this.loadSpotTypes();
    this.loadSpots();
    this.updateMapHeight();
  },
  onShow() {
    this.updateMapHeight();
  },
  onResize() {
    this.updateMapHeight();
  },
  methods: {
    // 搜索输入事件
    onSearchInput(value) {
      this.searchKeyword = value;
    },
    // 加载景点类型
    loadSpotTypes() {
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getScenicSpotTypes",
        method: "GET",
        success: (res) => {
          if (res.data.code === 1) {
            const types = res.data.data.list || [];
            this.spotTypes = types.map((type) => ({
              label: type.name,
              value: type.code,
              color: type.color || "#1890ff",
              icon: type.icon || ""
            }));
          }
        },
        fail: () => {
          this.spotTypes = [];
        }
      });
    },
    // 加载景点信息
    loadSpots(reset = true) {
      if (this.loading && !this.refreshing)
        return;
      if (reset) {
        this.page = 1;
        this.hasMore = true;
      }
      this.loading = true;
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getScenicSpots",
        method: "GET",
        data: {
          page: this.page,
          pageSize: this.pageSize,
          type: this.activeCategory !== "all" ? this.activeCategory : "",
          keyword: this.searchKeyword
        },
        success: (res) => {
          this.loading = false;
          this.refreshing = false;
          if (res.data.code === 1) {
            const list = res.data.data.list || [];
            if (this.userLocation) {
              list.forEach((spot) => {
                spot.distance = this.calculateDistance(
                  this.userLocation.latitude,
                  this.userLocation.longitude,
                  spot.latitude,
                  spot.longitude
                );
              });
            }
            list.forEach((spot) => {
              if (spot.type_info) {
                spot.type = spot.type_info.code || spot.type || "";
                spot.typeName = spot.type_info.name || "";
                spot.typeColor = spot.type_info.color || "#1890ff";
                spot.typeIcon = spot.type_info.icon || "";
              }
            });
            if (reset) {
              this.spots = list;
            } else {
              this.spots = [...this.spots, ...list];
            }
            this.filteredSpots = this.computedFilteredSpots;
            this.createMarkers();
            this.hasMore = list.length >= this.pageSize;
            if (this.spots.length > 0 && reset && !this.userLocation) {
              this.latitude = Number(this.spots[0].latitude);
              this.longitude = Number(this.spots[0].longitude);
            }
          } else {
            common_vendor.index.showToast({
              title: "加载失败：" + res.data.msg,
              icon: "none"
            });
          }
        },
        fail: () => {
          this.loading = false;
          this.refreshing = false;
          common_vendor.index.showToast({
            title: "网络错误，请稍后再试",
            icon: "none"
          });
        }
      });
    },
    // 加载更多景点
    loadMoreSpots() {
      if (!this.hasMore || this.loading)
        return;
      this.page++;
      this.loadSpots(false);
    },
    // 下拉刷新
    refreshSpots() {
      this.refreshing = true;
      this.loadSpots();
    },
    // 获取用户位置
    getUserLocation() {
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          this.userLocation = {
            latitude: Number(res.latitude),
            longitude: Number(res.longitude)
          };
          this.latitude = Number(res.latitude);
          this.longitude = Number(res.longitude);
          if (this.spots.length > 0) {
            this.spots.forEach((spot) => {
              spot.distance = this.calculateDistance(
                res.latitude,
                res.longitude,
                spot.latitude,
                spot.longitude
              );
            });
            this.filteredSpots = this.computedFilteredSpots;
          }
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "获取位置失败，请检查定位权限",
            icon: "none"
          });
        }
      });
    },
    // 创建地图标记
    createMarkers() {
      this.markers = this.spots.map((spot) => {
        return {
          id: spot.id,
          latitude: spot.latitude,
          longitude: spot.longitude,
          title: spot.name,
          iconPath: "/static/images/marker.png",
          width: 32,
          height: 32,
          callout: {
            content: spot.name,
            color: "#333333",
            fontSize: 14,
            borderRadius: 8,
            bgColor: "#FFFFFF",
            padding: 8,
            display: "BYCLICK",
            textAlign: "center"
          },
          label: {
            content: spot.name,
            color: "#333333",
            fontSize: 12,
            anchorX: 0,
            anchorY: -65,
            borderWidth: 0,
            borderColor: "#FFFFFF",
            borderRadius: 4,
            bgColor: "#FFFFFF",
            padding: 4,
            textAlign: "center"
          }
        };
      });
      if (this.userLocation) {
        this.markers.push({
          id: 0,
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude,
          iconPath: "/static/images/user-location.png",
          width: 32,
          height: 32,
          zIndex: 99
        });
      }
    },
    // 标记点击事件
    onMarkerTap(e) {
      const markerId = e.markerId;
      const spot = this.spots.find((item) => item.id === markerId);
      if (spot) {
        this.showSpotDetail(spot);
      }
    },
    // 气泡点击事件
    onCalloutTap(e) {
      const markerId = e.markerId;
      const spot = this.spots.find((item) => item.id === markerId);
      if (spot) {
        this.showSpotDetail(spot);
      }
    },
    // 地图区域变化事件
    onMapRegionChange() {
    },
    // 放大地图
    zoomIn() {
      if (this.scale < 20) {
        this.scale++;
      }
    },
    // 缩小地图
    zoomOut() {
      if (this.scale > 5) {
        this.scale--;
      }
    },
    // 重置地图视图
    resetMapView() {
      if (this.userLocation) {
        this.latitude = Number(this.userLocation.latitude);
        this.longitude = Number(this.userLocation.longitude);
      } else if (this.spots.length > 0) {
        this.latitude = Number(this.spots[0].latitude);
        this.longitude = Number(this.spots[0].longitude);
      }
      this.scale = 15;
    },
    // 定位用户
    locateUser() {
      if (this.userLocation) {
        this.latitude = Number(this.userLocation.latitude);
        this.longitude = Number(this.userLocation.longitude);
        this.scale = 16;
      } else {
        this.getUserLocation();
      }
    },
    // 进入全屏模式
    enterFullscreen() {
      this.isMapFullscreen = true;
      this.updateMapHeight();
    },
    // 退出全屏模式
    exitFullscreen() {
      this.isMapFullscreen = false;
      this.updateMapHeight();
    },
    // 更新地图高度
    updateMapHeight() {
      if (this.isMapFullscreen) {
        this.mapHeight = "100vh";
      } else {
        if (!this.mapHeight || this.mapHeight === "100vh") {
          this.mapHeight = "400rpx";
        }
        this.$nextTick(() => {
          const searchBarHeight = 110;
          const mapHeightValue = parseInt(this.mapHeight);
          this.listContainerHeight = `calc(100vh - ${searchBarHeight}rpx - ${mapHeightValue}rpx)`;
        });
      }
    },
    // 搜索景点
    searchSpots() {
      this.loadSpots();
    },
    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
      this.loadSpots();
    },
    // 切换筛选面板
    toggleFilterPanel() {
      this.showFilterPanel = !this.showFilterPanel;
    },
    // 重置筛选
    resetFilter() {
      this.selectedType = "all";
      this.sortBy = "distance";
      this.searchKeyword = "";
      this.activeCategory = "all";
      this.loadSpots();
      this.showFilterPanel = false;
    },
    // 应用筛选
    applyFilter() {
      this.activeCategory = this.selectedType;
      this.loadSpots();
      this.showFilterPanel = false;
    },
    // 按分类筛选
    filterByCategory(category) {
      this.activeCategory = category;
      this.loadSpots();
    },
    // 显示景点详情
    showSpotDetail(spot) {
      this.currentSpot = spot;
      this.showDetailPopup = true;
    },
    // 关闭景点详情弹窗
    closeDetailPopup() {
      this.showDetailPopup = false;
    },
    // 导航到景点详情页
    navigateToSpot(id) {
      common_vendor.index.navigateTo({
        url: "/pages/scenicspot/detail?id=" + id
      });
    },
    // 导航到景点
    navigateTo(spot) {
      common_vendor.index.openLocation({
        latitude: parseFloat(spot.latitude),
        longitude: parseFloat(spot.longitude),
        name: spot.name,
        address: spot.address || "景区内",
        scale: 18
      });
    },
    // 触摸开始事件
    onTouchStart(e) {
      if (this.isMapFullscreen)
        return;
      this.touchStartY = e.changedTouches[0].clientY;
    },
    // 触摸结束事件
    onTouchEnd(e) {
      if (this.isMapFullscreen)
        return;
      this.touchEndY = e.changedTouches[0].clientY;
      const deltaY = this.touchStartY - this.touchEndY;
      if (deltaY > this.touchThreshold) {
        this.mapHeight = "200rpx";
        this.listContainerHeight = "calc(100vh - 110rpx - 200rpx)";
      } else if (deltaY < -this.touchThreshold) {
        this.mapHeight = "400rpx";
        this.listContainerHeight = "calc(100vh - 110rpx - 400rpx)";
      }
    },
    // 计算两点之间的距离（米）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371e3;
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;
      return distance;
    },
    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI / 180);
    }
  }
};
if (!Array) {
  const _component_search_bar = common_vendor.resolveComponent("search-bar");
  const _component_filter_panel = common_vendor.resolveComponent("filter-panel");
  const _component_map_container = common_vendor.resolveComponent("map-container");
  const _component_category_tabs = common_vendor.resolveComponent("category-tabs");
  const _component_spot_list = common_vendor.resolveComponent("spot-list");
  const _component_spot_detail_popup = common_vendor.resolveComponent("spot-detail-popup");
  const _component_loading_overlay = common_vendor.resolveComponent("loading-overlay");
  (_component_search_bar + _component_filter_panel + _component_map_container + _component_category_tabs + _component_spot_list + _component_spot_detail_popup + _component_loading_overlay)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o($options.onSearchInput),
    b: common_vendor.o($options.searchSpots),
    c: common_vendor.o($options.clearSearch),
    d: common_vendor.o($options.toggleFilterPanel),
    e: common_vendor.p({
      keyword: $data.searchKeyword
    }),
    f: common_vendor.o($options.resetFilter),
    g: common_vendor.o($options.applyFilter),
    h: common_vendor.o($options.toggleFilterPanel),
    i: common_vendor.p({
      show: $data.showFilterPanel,
      ["spot-types"]: $data.spotTypes,
      ["selected-type"]: $data.selectedType,
      ["sort-by"]: $data.sortBy
    }),
    j: common_vendor.o($options.onMarkerTap),
    k: common_vendor.o($options.onCalloutTap),
    l: common_vendor.o($options.onMapRegionChange),
    m: common_vendor.o($options.zoomIn),
    n: common_vendor.o($options.zoomOut),
    o: common_vendor.o($options.resetMapView),
    p: common_vendor.o($options.locateUser),
    q: common_vendor.o($options.enterFullscreen),
    r: common_vendor.o($options.exitFullscreen),
    s: common_vendor.p({
      latitude: Number($data.latitude),
      longitude: Number($data.longitude),
      scale: $data.scale,
      markers: $data.markers,
      ["is-fullscreen"]: $data.isMapFullscreen,
      ["map-height"]: $data.mapHeight
    }),
    t: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    v: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args)),
    w: !$data.isMapFullscreen
  }, !$data.isMapFullscreen ? {
    x: common_vendor.o($options.filterByCategory),
    y: common_vendor.p({
      categories: $data.spotTypes,
      ["active-category"]: $data.activeCategory
    }),
    z: common_vendor.o($options.loadMoreSpots),
    A: common_vendor.o($options.refreshSpots),
    B: common_vendor.o($options.showSpotDetail),
    C: common_vendor.o($options.navigateToSpot),
    D: common_vendor.o($options.navigateTo),
    E: common_vendor.o($options.resetFilter),
    F: common_vendor.p({
      spots: $data.filteredSpots,
      ["spot-types"]: $data.spotTypes,
      ["has-more"]: $data.hasMore,
      loading: $data.loading,
      refreshing: $data.refreshing
    }),
    G: $data.listContainerHeight
  } : {}, {
    H: common_vendor.o($options.closeDetailPopup),
    I: common_vendor.o($options.navigateToSpot),
    J: common_vendor.o($options.navigateTo),
    K: common_vendor.p({
      show: $data.showDetailPopup,
      spot: $data.currentSpot
    }),
    L: common_vendor.p({
      show: $data.loading
    }),
    M: $data.isMapFullscreen ? 1 : ""
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/guide/guide.js.map
