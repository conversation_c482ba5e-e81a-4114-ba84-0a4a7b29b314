"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      id: 0,
      spot: {
        name: "",
        description: "",
        images: [],
        address: "",
        opening_hours: "",
        ticket_price: "",
        tips: "",
        latitude: 0,
        longitude: 0,
        type: "",
        rating: 4.5,
        tags: [],
        contact: ""
      },
      markers: [],
      loading: true,
      loadError: false,
      errorMsg: "加载失败，请重试",
      statusBarHeight: 20,
      isFavorite: false,
      descExpanded: false,
      relatedSpots: [],
      userLocation: null,
      // 景点类型选项
      spotTypes: [
        { label: "全部", value: "all" },
        { label: "自然风光", value: "nature" },
        { label: "历史古迹", value: "history" },
        { label: "文化场馆", value: "culture" },
        { label: "休闲娱乐", value: "entertainment" }
      ]
    };
  },
  onLoad(options) {
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.getUserLocation();
    if (options.id) {
      this.id = options.id;
      this.loadSpotDetail();
      this.checkFavoriteStatus();
    } else {
      this.loading = false;
      this.loadError = true;
      this.errorMsg = "参数错误";
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  onShareAppMessage() {
    return {
      title: this.spot.name,
      path: "/pages/scenicspot/detail?id=" + this.id,
      imageUrl: this.spot.images && this.spot.images.length > 0 ? this.spot.images[0] : ""
    };
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载景点详情
    loadSpotDetail() {
      this.loading = true;
      this.loadError = false;
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getScenicSpotDetail",
        method: "GET",
        data: {
          id: this.id
        },
        success: (res) => {
          this.loading = false;
          if (res.data.code === 1) {
            this.spot = res.data.data.info;
            if (!this.spot.tags || this.spot.tags.length === 0) {
              this.spot.tags = ["景点", "旅游", this.getSpotTypeLabel(this.spot.type)];
            }
            this.createMarkers();
            this.loadRelatedSpots();
          } else {
            this.loadError = true;
            this.errorMsg = res.data.msg || "加载失败，请重试";
          }
        },
        fail: () => {
          this.loading = false;
          this.loadError = true;
          this.errorMsg = "网络错误，请稍后再试";
        }
      });
    },
    // 获取用户位置
    getUserLocation() {
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          this.userLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };
        }
      });
    },
    // 创建地图标记
    createMarkers() {
      this.markers = [{
        id: 1,
        latitude: Number(this.spot.latitude),
        longitude: Number(this.spot.longitude),
        title: this.spot.name,
        iconPath: "/static/images/marker.png",
        width: 32,
        height: 32,
        callout: {
          content: this.spot.name,
          color: "#333333",
          fontSize: 14,
          borderRadius: 8,
          bgColor: "#FFFFFF",
          padding: 8,
          display: "ALWAYS"
        }
      }];
    },
    // 导航到景点
    navigateTo() {
      common_vendor.index.openLocation({
        latitude: parseFloat(this.spot.latitude),
        longitude: parseFloat(this.spot.longitude),
        name: this.spot.name,
        address: this.spot.address || "景区内",
        scale: 18
      });
    },
    // 预览图片
    previewImage(index) {
      if (this.spot.images && this.spot.images.length > 0) {
        common_vendor.index.previewImage({
          current: index,
          urls: this.spot.images
        });
      }
    },
    // 分享景点
    shareSpot() {
    },
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      common_vendor.index.getStorage({
        key: "favoriteSpots",
        success: (res) => {
          let favorites = res.data || [];
          if (this.isFavorite) {
            if (!favorites.includes(this.id)) {
              favorites.push(this.id);
            }
            common_vendor.index.showToast({
              title: "收藏成功",
              icon: "success"
            });
          } else {
            favorites = favorites.filter((id) => id !== this.id);
            common_vendor.index.showToast({
              title: "已取消收藏",
              icon: "none"
            });
          }
          common_vendor.index.setStorage({
            key: "favoriteSpots",
            data: favorites
          });
        },
        fail: () => {
          let favorites = [];
          if (this.isFavorite) {
            favorites.push(this.id);
            common_vendor.index.showToast({
              title: "收藏成功",
              icon: "success"
            });
          }
          common_vendor.index.setStorage({
            key: "favoriteSpots",
            data: favorites
          });
        }
      });
    },
    // 检查收藏状态
    checkFavoriteStatus() {
      common_vendor.index.getStorage({
        key: "favoriteSpots",
        success: (res) => {
          let favorites = res.data || [];
          this.isFavorite = favorites.includes(this.id);
        }
      });
    },
    // 切换描述展开/收起状态
    toggleDescExpand() {
      this.descExpanded = !this.descExpanded;
    },
    // 加载相关推荐景点
    loadRelatedSpots() {
      common_vendor.index.request({
        url: this.$baseUrl + "/api/getScenicSpots",
        method: "GET",
        data: {
          type: this.spot.type,
          exclude_id: this.id,
          limit: 5
        },
        success: (res) => {
          if (res.data.code === 1) {
            this.relatedSpots = res.data.data.list || [];
            if (this.userLocation && this.relatedSpots.length > 0) {
              this.relatedSpots.forEach((spot) => {
                spot.distance = this.calculateDistance(
                  this.userLocation.latitude,
                  this.userLocation.longitude,
                  spot.latitude,
                  spot.longitude
                );
              });
              this.relatedSpots.sort((a, b) => a.distance - b.distance);
            }
          }
        }
      });
    },
    // 跳转到景点详情
    goToSpotDetail(id) {
      common_vendor.index.navigateTo({
        url: "/pages/scenicspot/detail?id=" + id
      });
    },
    // 获取景点类型标签
    getSpotTypeLabel(type) {
      const typeObj = this.spotTypes.find((item) => item.value === type);
      return typeObj ? typeObj.label : "景点";
    },
    // 格式化距离
    formatDistance(distance) {
      if (distance < 1e3) {
        return Math.round(distance) + "m";
      } else {
        return (distance / 1e3).toFixed(1) + "km";
      }
    },
    // 计算两点之间的距离（米）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371e3;
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;
      return distance;
    },
    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI / 180);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.loadError ? {
    c: common_assets._imports_0$2,
    d: common_vendor.t($data.errorMsg),
    e: common_vendor.o((...args) => $options.loadSpotDetail && $options.loadSpotDetail(...args))
  } : common_vendor.e({
    f: $data.spot.images && $data.spot.images.length > 0
  }, $data.spot.images && $data.spot.images.length > 0 ? {
    g: common_vendor.f($data.spot.images, (item, index, i0) => {
      return {
        a: item,
        b: index,
        c: common_vendor.o(($event) => $options.previewImage(index), index)
      };
    })
  } : {
    h: common_assets._imports_1$1
  }, {
    i: $data.spot.type
  }, $data.spot.type ? {
    j: common_vendor.t($options.getSpotTypeLabel($data.spot.type))
  } : {}, {
    k: common_vendor.t($data.spot.name),
    l: common_vendor.f(Math.floor($data.spot.rating || 4.5), (n, k0, i0) => {
      return {
        a: n
      };
    }),
    m: ($data.spot.rating || 4.5) % 1 > 0
  }, ($data.spot.rating || 4.5) % 1 > 0 ? {} : {}, {
    n: common_vendor.t($data.spot.rating || 4.5),
    o: $data.spot.tags && $data.spot.tags.length
  }, $data.spot.tags && $data.spot.tags.length ? {
    p: common_vendor.f($data.spot.tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    })
  } : {}, {
    q: common_vendor.t($data.spot.description || "暂无描述"),
    r: $data.descExpanded ? 1 : "",
    s: $data.spot.description && $data.spot.description.length > 100
  }, $data.spot.description && $data.spot.description.length > 100 ? common_vendor.e({
    t: common_vendor.t($data.descExpanded ? "收起" : "展开"),
    v: !$data.descExpanded
  }, !$data.descExpanded ? {} : {}, {
    w: common_vendor.o((...args) => $options.toggleDescExpand && $options.toggleDescExpand(...args))
  }) : {}, {
    x: common_vendor.t($data.spot.address || "暂无地址信息"),
    y: common_vendor.t($data.spot.opening_hours || "暂无开放时间信息"),
    z: common_vendor.t($data.spot.ticket_price || "暂无票价信息"),
    A: $data.spot.contact
  }, $data.spot.contact ? {
    B: common_vendor.t($data.spot.contact)
  } : {}, {
    C: $data.spot.tips
  }, $data.spot.tips ? {
    D: common_vendor.t($data.spot.tips)
  } : {}, {
    E: Number($data.spot.latitude),
    F: Number($data.spot.longitude),
    G: $data.markers,
    H: common_vendor.o((...args) => $options.navigateTo && $options.navigateTo(...args)),
    I: $data.relatedSpots.length > 0
  }, $data.relatedSpots.length > 0 ? {
    J: common_vendor.f($data.relatedSpots, (item, index, i0) => {
      return common_vendor.e({
        a: item.cover || "/static/images/no-image.jpg",
        b: common_vendor.t(item.name),
        c: item.distance
      }, item.distance ? {
        d: common_vendor.t($options.formatDistance(item.distance))
      } : {}, {
        e: index,
        f: common_vendor.o(($event) => $options.goToSpotDetail(item.id), index)
      });
    })
  } : {}), {
    b: $data.loadError,
    K: common_vendor.o((...args) => $options.shareSpot && $options.shareSpot(...args)),
    L: common_vendor.t($data.isFavorite ? "已收藏" : "收藏"),
    M: $data.isFavorite ? 1 : "",
    N: common_vendor.o((...args) => $options.toggleFavorite && $options.toggleFavorite(...args)),
    O: common_vendor.o((...args) => $options.navigateTo && $options.navigateTo(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/scenicspot/detail.js.map
