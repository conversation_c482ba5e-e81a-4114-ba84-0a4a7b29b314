"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_ChatService = require("../../utils/ChatService.js");
const ChatMessage = () => "../../components/chat/ChatMessage.js";
const ChatInput = () => "../../components/chat/ChatInput.js";
const ChatTypingIndicator = () => "../../components/chat/ChatTypingIndicator.js";
const MarkdownStyles = () => "../../components/chat/MarkdownStyles.js";
const _sfc_main = {
  // 注册组件
  components: {
    ChatMessage,
    ChatInput,
    ChatTypingIndicator,
    MarkdownStyles
  },
  data() {
    return {
      chatList: [],
      scrollTop: 0,
      isTyping: false,
      sessionId: "",
      userId: "",
      chatService: null,
      inputMessage: ""
      // 保留以兼容旧代码
    };
  },
  onLoad() {
    this.sessionId = "miniapp_" + Math.random().toString(36).substring(2, 15);
    let deviceId = common_vendor.index.getStorageSync("deviceId");
    if (!deviceId) {
      deviceId = "device_" + Math.random().toString(36).substring(2, 15) + Date.now().toString(36);
      common_vendor.index.setStorageSync("deviceId", deviceId);
    }
    this.userId = common_vendor.index.getStorageSync("userId") || "user_" + Math.random().toString(36).substring(2, 15);
    common_vendor.index.setStorageSync("userId", this.userId);
    this.chatService = new utils_ChatService.ChatService({
      baseUrl: this.$baseUrl,
      userId: this.userId,
      deviceId,
      sessionId: this.sessionId,
      platform: "miniapp"
    });
    const welcomeMessage = "您好！我是景区智能助理，请问有什么可以帮助您的？";
    const welcomeMessageObj = {
      isUser: false,
      message: welcomeMessage,
      parsedMessage: this.parseMarkdown(welcomeMessage),
      time: this.formatTime(/* @__PURE__ */ new Date()),
      isStreaming: false,
      functionCalls: []
      // 初始欢迎消息没有函数调用
    };
    this.chatList.push(welcomeMessageObj);
    this.$nextTick(() => {
      this.scrollToBottom();
    });
  },
  methods: {
    // 处理发送消息
    handleSendMessage(message) {
      if (!message.trim())
        return;
      this.chatList.push({
        isUser: true,
        message,
        time: this.formatTime(/* @__PURE__ */ new Date())
      });
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      this.isTyping = true;
      this.chatService.sendMessage(
        message,
        // 成功回调
        (response) => {
          this.isTyping = false;
          const newMessage = {
            isUser: false,
            message: response.text,
            parsedMessage: this.parseMarkdown(response.text),
            time: this.formatTime(/* @__PURE__ */ new Date()),
            isStreaming: false,
            functionCalls: response.functionCalls || []
          };
          this.chatList.push(newMessage);
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        },
        // 失败回调
        (errorMsg) => {
          this.isTyping = false;
          this.chatList.push({
            isUser: false,
            message: errorMsg,
            parsedMessage: this.parseMarkdown(errorMsg),
            time: this.formatTime(/* @__PURE__ */ new Date()),
            isStreaming: false,
            // 确保不使用流式显示
            functionCalls: []
            // 确保没有函数调用
          });
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      );
    },
    // 旧的发送消息方法（保留以兼容现有代码）
    sendMessage() {
      if (!this.inputMessage.trim())
        return;
      this.handleSendMessage(this.inputMessage);
      this.inputMessage = "";
    },
    // 滚动到底部
    scrollToBottom() {
      this.scrollTop = 1e5;
    },
    // 加载更多消息（暂未实现）
    loadMoreMessages() {
      common_vendor.index.__f__("log", "at pages/chat/chat.vue:210", "加载更多消息");
    },
    // 格式化日期
    formatDate(date) {
      const today = /* @__PURE__ */ new Date();
      if (date.getDate() === today.getDate() && date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear()) {
        return "";
      }
      const month = this.padZero(date.getMonth() + 1);
      const day = this.padZero(date.getDate());
      if (date.getFullYear() === today.getFullYear()) {
        return month + "月" + day + "日";
      }
      const year = date.getFullYear();
      return year + "年" + month + "月" + day + "日";
    },
    // 格式化时间
    formatTime(date) {
      const hour = this.padZero(date.getHours());
      const minute = this.padZero(date.getMinutes());
      return hour + ":" + minute;
    },
    // 补零
    padZero(num) {
      return num < 10 ? "0" + num : num;
    },
    // 解析Markdown
    parseMarkdown(text) {
      if (!text)
        return "";
      try {
        let html = common_vendor.marked(text);
        html = html.replace(/<h1/g, '<h1 class="markdown-h1"').replace(/<h2/g, '<h2 class="markdown-h2"').replace(/<h3/g, '<h3 class="markdown-h3"').replace(/<h4/g, '<h4 class="markdown-h4"').replace(/<h5/g, '<h5 class="markdown-h5"').replace(/<h6/g, '<h6 class="markdown-h6"').replace(/<p/g, '<p class="markdown-p"').replace(/<ul/g, '<ul class="markdown-ul"').replace(/<ol/g, '<ol class="markdown-ol"').replace(/<li/g, '<li class="markdown-li"').replace(/<blockquote/g, '<blockquote class="markdown-blockquote"').replace(/<pre/g, '<pre class="markdown-pre"').replace(/<code/g, '<code class="markdown-code"').replace(/<a/g, '<a class="markdown-a"').replace(/<img/g, '<img class="markdown-img"').replace(/<table/g, '<table class="markdown-table"').replace(/<th/g, '<th class="markdown-th"').replace(/<td/g, '<td class="markdown-td"');
        return html;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/chat/chat.vue:282", "Markdown解析错误:", error);
        return text;
      }
    }
  }
};
if (!Array) {
  const _component_chat_message = common_vendor.resolveComponent("chat-message");
  const _component_chat_typing_indicator = common_vendor.resolveComponent("chat-typing-indicator");
  const _component_chat_input = common_vendor.resolveComponent("chat-input");
  const _component_markdown_styles = common_vendor.resolveComponent("markdown-styles");
  (_component_chat_message + _component_chat_typing_indicator + _component_chat_input + _component_markdown_styles)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.chatList.length > 0
  }, $data.chatList.length > 0 ? {} : {}, {
    b: common_vendor.f($data.chatList, (item, index, i0) => {
      return {
        a: index,
        b: "5da4d40c-0-" + i0,
        c: common_vendor.p({
          ["is-user"]: item.isUser,
          message: item.message,
          ["parsed-message"]: item.parsedMessage,
          time: item.time,
          ["is-streaming"]: item.isStreaming,
          ["function-calls"]: item.functionCalls
        })
      };
    }),
    c: common_vendor.p({
      visible: $data.isTyping
    }),
    d: $data.scrollTop,
    e: common_vendor.o((...args) => $options.loadMoreMessages && $options.loadMoreMessages(...args)),
    f: common_vendor.o($options.handleSendMessage)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/chat/chat.js.map
