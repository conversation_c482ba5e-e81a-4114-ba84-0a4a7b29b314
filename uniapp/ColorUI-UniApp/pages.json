{
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {}
		},
		{
			"path": "pages/basics/layout",
			"style": {}
		},
		{
			"path": "pages/basics/background",
			"style": {}
		},
		{
			"path": "pages/basics/text",
			"style": {}
		},
		{
			"path": "pages/basics/icon",
			"style": {}
		},
		{
			"path": "pages/basics/button",
			"style": {}
		},
		{
			"path": "pages/basics/design",
			"style": {}
		},
		{
			"path": "pages/basics/tag",
			"style": {}
		},
		{
			"path": "pages/basics/avatar",
			"style": {}
		},
		{
			"path": "pages/basics/progress",
			"style": {}
		},
		{
			"path": "pages/basics/shadow",
			"style": {}
		},
		{
			"path": "pages/basics/loading",
			"style": {}
		},
		{
			"path": "pages/component/bar",
			"style": {}
		},
		{
			"path": "pages/component/nav",
			"style": {}
		},
		{
			"path": "pages/component/list",
			"style": {}
		},
		{
			"path": "pages/component/card",
			"style": {}
		},
		{
			"path": "pages/component/form",
			"style": {}
		},
		{
			"path": "pages/component/timeline",
			"style": {}
		},
		{
			"path": "pages/component/chat",
			"style": {}
		},
		{
			"path": "pages/component/swiper",
			"style": {}
		},
		{
			"path": "pages/component/modal",
			"style": {}
		},
		{
			"path": "pages/component/steps",
			"style": {}
		}, {
			"path": "pages/plugin/indexes",
			"style": {}
		}, {
			"path": "pages/plugin/animation",
			"style": {}
		}, {
			"path": "pages/plugin/drawer",
			"style": {}
		}, {
			"path": "pages/plugin/verticalnav",
			"style": {}
		}
	],
	"globalStyle": {
		"mp-alipay": {
			/* 支付宝小程序特有相关 */
			"transparentTitle": "always",
			"allowsBounceVertical": "NO"
		},
		"navigationBarBackgroundColor": "#0081ff",
		"navigationBarTitleText": "ColorUi for UniApp",
		"navigationStyle": "custom",
		"navigationBarTextStyle": "white"
	},
	"usingComponts": true,
		"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
				"name": "表单", //模式名称
				"path": "pages/component/form", //启动页面
				"query": "" //启动参数
			}
		]
	}

}
