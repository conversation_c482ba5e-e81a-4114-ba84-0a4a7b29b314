# 聊天页面优化任务

## 问题描述

当前聊天页面存在以下问题：
1. 页面路由未正确配置，导致导航错误
2. Markdown内容渲染不正确
3. UI设计较为简单，缺乏现代感
4. 缺少真实的头像图片
5. 动画效果不够流畅

## 解决方案

### 1. 页面路由配置

在`pages.json`中添加聊天页面路由：

```json
{
  "path": "pages/chat/chat",
  "style": {
    "navigationBarTitleText": "智能问答"
  }
}
```

### 2. Markdown渲染优化

1. 确保正确导入marked库：
   ```javascript
   import { marked } from 'marked';
   ```

2. 修改parseMarkdown方法，确保正确调用marked函数：
   ```javascript
   parseMarkdown(text) {
     if (!text) return '';
     try {
       const html = marked(text);
       return html;
     } catch (error) {
       console.error('Markdown解析错误:', error);
       return text;
     }
   }
   ```

3. 在App.vue中添加全局样式，确保rich-text组件内的HTML元素能够正确显示样式。

### 3. UI设计优化

使用Tailwind CSS重新设计页面，包括：

1. 聊天气泡样式优化：
   - 用户消息使用蓝色背景，右侧圆角，左上角尖角
   - 助手消息使用白色背景，左侧圆角，右上角尖角
   - 添加轻微阴影效果

2. 布局优化：
   - 用户消息靠右对齐
   - 助手消息靠左对齐
   - 消息时间居中显示

3. 输入框优化：
   - 使用圆角设计
   - 添加发送按钮动效
   - 优化输入框高度和内边距

4. 添加真实头像：
   - 用户头像使用默认用户图标或可自定义头像
   - 助手头像使用景区助理形象

### 4. 动画效果优化

1. 优化"正在输入"指示器动画：
   ```css
   @keyframes bounce {
     0%, 100% {
       transform: translateY(0);
     }
     50% {
       transform: translateY(-5px);
     }
   }
   
   .animate-bounce {
     animation: bounce 1s infinite;
   }
   ```

2. 添加消息发送和接收动画效果。

### 5. 资源准备

1. 创建`static/images`目录
2. 添加用户头像图片：`user-avatar.jpg`
3. 添加助手头像图片：`assistant-avatar.jpg`

## 实现步骤

1. 修改`pages.json`，添加聊天页面路由
2. 更新`chat.vue`模板部分，使用Tailwind CSS类名
3. 更新`chat.vue`样式部分，添加自定义样式
4. 准备并添加头像图片
5. 在App.vue中添加全局样式
6. 测试页面效果，确保所有功能正常工作

## 预期效果

1. 页面能够正常导航
2. Markdown内容能够正确渲染
3. 聊天界面具有现代感，符合微信小程序设计规范
4. 动画效果流畅自然
5. 头像图片真实美观
