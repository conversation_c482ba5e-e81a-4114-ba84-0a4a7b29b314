# Markdown渲染优化任务

## 问题描述

当前聊天页面中的Markdown内容渲染存在以下问题：
1. Markdown解析可能不正确
2. 样式应用不一致
3. rich-text组件内的HTML元素样式不正确

## 解决方案

### 1. 确保正确导入和使用marked库

```javascript
// 正确的导入方式
import { marked } from 'marked';

// 正确的使用方式
parseMarkdown(text) {
  if (!text) return '';
  try {
    const html = marked(text);
    return html;
  } catch (error) {
    console.error('Markdown解析错误:', error);
    return text;
  }
}
```

### 2. 配置marked选项以适应小程序环境

```javascript
// 配置marked选项
marked.setOptions({
  gfm: true, // 启用GitHub风格的Markdown
  breaks: true, // 启用换行符转换为<br>
  headerIds: false, // 禁用标题ID生成，避免小程序环境中的问题
  mangle: false, // 禁用链接文本修改
  sanitize: false, // 不进行HTML转义
  smartLists: true, // 使用更智能的列表行为
  smartypants: false, // 禁用引号和破折号的智能转换
  xhtml: false // 不使用自闭合标签
});
```

### 3. 在App.vue中添加全局样式

在App.vue的style标签中添加以下样式，确保rich-text组件内的HTML元素能够正确显示样式：

```css
/* Markdown全局样式 - 用于rich-text组件 */
rich-text {
  font-size: 28rpx;
  line-height: 1.6;
}

rich-text h1, rich-text h2, rich-text h3,
rich-text h4, rich-text h5, rich-text h6 {
  font-weight: 600;
  margin: 16rpx 0 8rpx 0;
  line-height: 1.4;
}

rich-text h1 { font-size: 36rpx; }
rich-text h2 { font-size: 32rpx; }
rich-text h3 { font-size: 30rpx; }
rich-text h4, rich-text h5, rich-text h6 { font-size: 28rpx; }

rich-text p {
  margin-bottom: 12rpx;
}

rich-text ul, rich-text ol {
  margin: 12rpx 0;
  padding-left: 32rpx;
}

rich-text li {
  margin-bottom: 6rpx;
}

rich-text blockquote {
  border-left: 8rpx solid #e2e8f0;
  padding-left: 16rpx;
  color: #4a5568;
  margin: 12rpx 0;
}

rich-text code {
  background-color: #f7fafc;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-family: monospace;
  font-size: 24rpx;
}

rich-text pre {
  background-color: #f7fafc;
  padding: 16rpx;
  border-radius: 8rpx;
  overflow-x: auto;
  margin: 12rpx 0;
}

rich-text pre code {
  background-color: transparent;
  padding: 0;
}

rich-text a {
  color: #3182ce;
  text-decoration: none;
}

rich-text img {
  max-width: 100%;
  height: auto;
  margin: 12rpx 0;
  border-radius: 8rpx;
}

rich-text table {
  width: 100%;
  border-collapse: collapse;
  margin: 12rpx 0;
}

rich-text th, rich-text td {
  border: 2rpx solid #e2e8f0;
  padding: 8rpx 12rpx;
  text-align: left;
}

rich-text th {
  background-color: #f7fafc;
}
```

### 4. 优化rich-text组件的使用

在模板中正确使用rich-text组件：

```html
<!-- 助手消息 -->
<view v-else class="bg-white text-gray-800 px-4 py-2 rounded-lg rounded-tl-none shadow-sm">
  <rich-text :nodes="item.parsedMessage"></rich-text>
</view>
```

### 5. 处理特殊Markdown元素

对于一些特殊的Markdown元素，可能需要额外的处理：

```javascript
// 处理代码块
const processCodeBlocks = (html) => {
  // 为代码块添加样式类
  return html.replace(/<pre><code>/g, '<pre><code class="code-block">');
};

// 处理表格
const processTables = (html) => {
  // 为表格添加样式类
  return html.replace(/<table>/g, '<table class="md-table">');
};

// 在parseMarkdown方法中使用
parseMarkdown(text) {
  if (!text) return '';
  try {
    let html = marked(text);
    html = processCodeBlocks(html);
    html = processTables(html);
    return html;
  } catch (error) {
    console.error('Markdown解析错误:', error);
    return text;
  }
}
```

## 实施步骤

1. 更新marked库的导入和使用方式
2. 配置marked选项
3. 在App.vue中添加全局样式
4. 优化rich-text组件的使用
5. 添加特殊Markdown元素的处理
6. 测试不同类型的Markdown内容

## 测试用例

准备以下Markdown测试用例，确保各种元素都能正确渲染：

```markdown
# 标题1
## 标题2
### 标题3

普通段落文本

**粗体文本** *斜体文本* ~~删除线文本~~

- 无序列表项1
- 无序列表项2
  - 嵌套列表项

1. 有序列表项1
2. 有序列表项2

> 引用文本
> 多行引用

`行内代码`

```javascript
// 代码块
function hello() {
  console.log('Hello World');
}
```

[链接文本](https://example.com)

![图片描述](https://example.com/image.jpg)

| 表头1 | 表头2 |
|-------|-------|
| 单元格1 | 单元格2 |
| 单元格3 | 单元格4 |
```

## 注意事项

1. 小程序环境对HTML支持有限，某些复杂的HTML结构可能无法正确渲染
2. rich-text组件的样式隔离特性可能导致样式应用不一致
3. 考虑使用其他Markdown解析库，如mini-markdown或wemark，它们可能更适合小程序环境
4. 对于复杂的Markdown内容，可能需要自定义渲染器
