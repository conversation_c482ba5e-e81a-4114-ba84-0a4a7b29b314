
根据我们在插件中设置的测试数据，您可以尝试以下问题来获得正常回复：

关于门票的问题：
"景区门票多少钱？"
"门票价格是多少？"
"儿童票多少钱？"
"老人票有优惠吗？"
关于开放时间的问题：
"景区开放时间是几点到几点？"
"景区什么时候开门？"
"景区营业时间是多久？"
"淡季和旺季的开放时间有区别吗？"
关于景点的问题：
"景区有哪些著名景点？"
"推荐游玩的景点有哪些？"
"青山瀑布在哪里？"
"天空栈道需要另外收费吗？"
关于交通的问题：
"如何前往景区？"
"从市区怎么去景区？"
"有公交车到景区吗？"
"自驾去景区怎么走？"
关于餐饮的问题：
"景区内有餐厅吗？"
"景区有什么吃的？"
"古镇风情街有什么特色小吃？"
"景区内餐饮价格如何？"
这些问题都与我们在知识库中添加的示例数据相匹配，系统应该能够提供相应的回答。如果问题与知识库中的内容相似度较高（超过配置的置信度阈值，默认为0.8），系统会直接返回知识库中的答案；如果相似度不够高，系统会调用DeepSeek API生成回答。

此外，您也可以询问关于已添加的景点的具体信息，如：

"青山瀑布有什么特色？"
"翠谷幽林适合什么时候去？"
"天空栈道安全吗？"
"古镇风情街有什么好玩的？"
"百花园什么时候花最多？"
这些问题虽然没有完全匹配知识库中的条目，但系统会根据已有的景点信息和DeepSeek AI的能力来生成合理的回答。