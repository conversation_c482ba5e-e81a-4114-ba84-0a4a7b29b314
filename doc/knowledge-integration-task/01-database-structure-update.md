# 任务1: 数据库结构更新

## 任务描述
更新知识库表结构，以支持景区/景点数据复用和Markdown内容格式。

## 涉及文件

### 需要修改的文件
1. `install.sql` - 更新表结构定义
2. `application/admin/model/dsassistant/Knowledge.php` - 更新模型定义
3. `application/admin/validate/dsassistant/Knowledge.php` - 更新验证规则

### 需要创建的文件
无

## 具体修改内容

### 1. 修改 `install.sql`

在 `fa_ds_knowledge` 表结构中：
- 将 `question` 字段重命名为 `title`
- 将 `answer` 字段重命名为 `content`
- 确保使用 `tags` 字段（而非 `keywords`）作为标签/关键词字段
- 添加以下新字段:
  ```sql
  `content_type` varchar(20) DEFAULT 'description' COMMENT '内容类型',
  `source_type` varchar(20) DEFAULT NULL COMMENT '来源类型',
  `source_id` int(10) DEFAULT NULL COMMENT '来源ID',
  `structured_data` text COMMENT '结构化数据(JSON格式)',
  `media_refs` text COMMENT '媒体引用(JSON格式)',
  ```
- 修改 `valid_from` 和 `valid_until` 字段名（原 `start_time` 和 `end_time`）

### 2. 修改 `application/admin/model/dsassistant/Knowledge.php`

- 更新模型属性和方法，适配新字段
- 添加新字段的访问器和修改器
- 示例修改:
  ```php
  // 追加属性
  protected $append = [
      'content_type_text',
      'source_type_text',
      'valid_from_text',
      'valid_until_text',
      'status_text',
      'vector_time_text'
  ];

  // 添加内容类型列表
  public function getContentTypeList()
  {
      return [
          'description' => __('Content type description'),
          'faq' => __('Content type faq'),
          'guide' => __('Content type guide'),
          'data' => __('Content type data'),
          'story' => __('Content type story'),
          'warning' => __('Content type warning'),
          'seasonal' => __('Content type seasonal')
      ];
  }

  // 添加来源类型列表
  public function getSourceTypeList()
  {
      return [
          'scenic' => __('Source type scenic'),
          'scenicspot' => __('Source type scenicspot'),
          'manual' => __('Source type manual'),
          'imported' => __('Source type imported'),
          'generated' => __('Source type generated')
      ];
  }
  ```

### 3. 修改 `application/admin/validate/dsassistant/Knowledge.php`

- 更新验证规则，适配新字段
- 示例修改:
  ```php
  protected $rule = [
      'title' => 'require',
      'content' => 'require',
      'content_type' => 'in:description,faq,guide,data,story,warning,seasonal',
      'source_type' => 'in:scenic,scenicspot,manual,imported,generated',
  ];
  ```

## 注意事项
1. 需要创建数据迁移脚本，将现有的 `question` 和 `answer` 字段数据迁移到新字段
2. 确保向量搜索相关功能在结构变更后仍能正常工作
3. 保持字段命名的一致性，特别是日期字段（valid_from/valid_until）

## 预计工作量
2-3人天
