# 任务3: 数据转换引擎开发

## 任务描述
开发数据转换引擎，将景区/景点数据转换为知识库条目，支持Markdown格式内容生成。采用基于模板的设计，通过数据库管理模板配置，实现灵活的数据转换。

## 涉及文件

### 需要创建的文件
1. `library/TemplateConverter.php` - 基于模板的通用转换器
2. `application/admin/view/dsassistant/converter_template/execute.html` - 执行转换视图
3. `application/admin/view/dsassistant/converter_template/initialize.html` - 初始化模板视图

### 需要修改的文件
1. `application/admin/controller/dsassistant/ConverterTemplate.php` - 添加执行转换和初始化模板方法
2. `public/assets/js/backend/dsassistant/converter_template.js` - 添加执行转换和初始化模板功能
3. `application/admin/lang/zh-cn/dsassistant/converter_template.php` - 添加相关翻译

## 具体实现内容

### 1. 数据库表设计

使用`fa_ds_converter_template`表存储转换模板配置：

```sql
CREATE TABLE `fa_ds_converter_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `source_table` varchar(100) NOT NULL COMMENT '数据源表名',
  `target_table` varchar(100) NOT NULL DEFAULT 'fa_ds_knowledge' COMMENT '目标表名',
  `source_type` varchar(30) NOT NULL COMMENT '数据类型',
  `content_type` varchar(30) NOT NULL COMMENT '内容类型',
  `template_content` text NOT NULL COMMENT '模板内容(JSON)',
  `sync_mode` enum('full','increment') NOT NULL DEFAULT 'full' COMMENT '同步模式',
  `sync_field` varchar(50) DEFAULT NULL COMMENT '增量同步字段',
  `last_sync_time` int(10) DEFAULT NULL COMMENT '上次同步时间',
  `last_sync_id` int(10) DEFAULT NULL COMMENT '上次同步ID',
  `where_condition` varchar(255) DEFAULT NULL COMMENT '查询条件',
  `order_field` varchar(50) DEFAULT 'id' COMMENT '排序字段',
  `order_direction` enum('asc','desc') DEFAULT 'asc' COMMENT '排序方向',
  `batch_size` int(10) DEFAULT 100 COMMENT '每批处理数量',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转换模板';
```

### 2. 创建 `library/TemplateConverter.php`

基于模板的通用转换器，实现模板驱动的数据转换：

```php
<?php

namespace addons\dsassistant\library;

use think\Db;
use think\Exception;
use think\Log;

/**
 * 模板转换器
 * 基于JSON模板将数据源转换为目标数据
 */
class TemplateConverter
{
    /**
     * 执行转换
     * @param int $templateId 模板ID
     * @param bool $force 是否强制全量同步
     * @return array 转换结果
     */
    public static function convert($templateId, $force = false)
    {
        try {
            // 获取模板信息
            $template = Db::name('ds_converter_template')->where('id', $templateId)->find();
            if (!$template) {
                throw new Exception("模板不存在");
            }

            // 检查模板状态
            if ($template['status'] != 'normal') {
                throw new Exception("模板状态不正常，无法执行转换");
            }

            // 构建查询条件
            $query = Db::name($template['source_table']);

            // 应用WHERE条件
            if (!empty($template['where_condition'])) {
                $query->where($template['where_condition']);
            }

            // 增量同步
            if ($template['sync_mode'] == 'increment' && !$force) {
                if (!empty($template['sync_field']) && !empty($template['last_sync_time'])) {
                    $query->where($template['sync_field'] . ' > ' . $template['last_sync_time']);
                } elseif (!empty($template['sync_field']) && !empty($template['last_sync_id'])) {
                    $query->where($template['sync_field'] . ' > ' . $template['last_sync_id']);
                }
            }

            // 应用排序
            if (!empty($template['order_field'])) {
                $query->order($template['order_field'], $template['order_direction'] ?: 'asc');
            }

            // 获取总记录数
            $total = $query->count();
            if ($total == 0) {
                return [
                    'code' => 1,
                    'msg' => '没有需要转换的数据',
                    'data' => [
                        'total' => 0,
                        'success' => 0,
                        'error' => 0
                    ]
                ];
            }

            // 分批处理
            $batchSize = $template['batch_size'] ?: 100;
            $successCount = 0;
            $errorCount = 0;
            $lastId = 0;
            $lastTime = 0;

            for ($offset = 0; $offset < $total; $offset += $batchSize) {
                $sourceData = $query->limit($offset, $batchSize)->select();

                foreach ($sourceData as $data) {
                    try {
                        // 应用模板转换
                        $result = self::applyTemplate($template, $data);

                        // 保存到目标表
                        if ($result) {
                            // 检查是否已存在相同记录
                            $exists = Db::name($template['target_table'])
                                ->where('source_type', $template['source_type'])
                                ->where('source_id', $data['id'])
                                ->where('content_type', $template['content_type'])
                                ->find();

                            if ($exists) {
                                // 更新记录
                                Db::name($template['target_table'])
                                    ->where('id', $exists['id'])
                                    ->update($result);
                            } else {
                                // 插入记录
                                Db::name($template['target_table'])->insert($result);
                            }

                            $successCount++;
                        }

                        // 记录最后处理的ID和时间
                        if (isset($data['id']) && $data['id'] > $lastId) {
                            $lastId = $data['id'];
                        }

                        if (isset($data[$template['sync_field']]) && $data[$template['sync_field']] > $lastTime) {
                            $lastTime = $data[$template['sync_field']];
                        }
                    } catch (Exception $e) {
                        Log::error("转换数据失败: " . $e->getMessage() . ", 数据: " . json_encode($data, JSON_UNESCAPED_UNICODE));
                        $errorCount++;
                    }
                }
            }

            // 更新模板同步状态
            $updateData = [
                'updatetime' => time()
            ];

            if ($lastId > 0) {
                $updateData['last_sync_id'] = $lastId;
            }

            if ($lastTime > 0) {
                $updateData['last_sync_time'] = $lastTime;
            }

            Db::name('ds_converter_template')
                ->where('id', $templateId)
                ->update($updateData);

            return [
                'code' => 1,
                'msg' => '转换完成',
                'data' => [
                    'total' => $total,
                    'success' => $successCount,
                    'error' => $errorCount
                ]
            ];
        } catch (Exception $e) {
            Log::error("执行转换失败: " . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '执行转换失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 应用模板转换数据
     * @param array $template 模板配置
     * @param array $data 源数据
     * @return array|false 转换后的数据，失败返回false
     */
    protected static function applyTemplate($template, $data)
    {
        try {
            // 解析模板内容
            $templateContent = json_decode($template['template_content'], true);
            if (!$templateContent) {
                throw new Exception("模板内容格式错误");
            }

            // 替换模板中的变量
            $result = self::replaceVariables($templateContent, $data);

            // 添加必要的字段
            $result['source_type'] = $template['source_type'];
            $result['content_type'] = $template['content_type'];
            $result['source_id'] = $data['id'];

            // 如果没有设置创建时间，添加当前时间
            if (!isset($result['createtime'])) {
                $result['createtime'] = time();
            }

            // 如果没有设置更新时间，添加当前时间
            if (!isset($result['updatetime'])) {
                $result['updatetime'] = time();
            }

            return $result;
        } catch (Exception $e) {
            Log::error("应用模板失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 替换变量
     * @param mixed $template 模板
     * @param array $data 数据
     * @return mixed 替换后的结果
     */
    protected static function replaceVariables($template, $data)
    {
        if (is_string($template)) {
            // 替换字符串中的变量
            return preg_replace_callback('/{([^}]+)}/', function($matches) use ($data) {
                $key = $matches[1];
                return isset($data[$key]) ? $data[$key] : '';
            }, $template);
        } elseif (is_array($template)) {
            // 递归处理数组
            $result = [];
            foreach ($template as $key => $value) {
                $result[$key] = self::replaceVariables($value, $data);
            }
            return $result;
        } else {
            // 其他类型直接返回
            return $template;
        }
    }

    /**
     * 批量执行转换
     * @param array $templateIds 模板ID数组
     * @param bool $force 是否强制全量同步
     * @return array 转换结果
     */
    public static function batchConvert($templateIds, $force = false)
    {
        $results = [];
        foreach ($templateIds as $templateId) {
            $results[$templateId] = self::convert($templateId, $force);
        }
        return $results;
    }
}
```

### 3. 模板示例

景区基本描述模板示例：

```json
{
  "title": "{name}",
  "content": "# {name}\n\n{description}\n\n## 区域类型\n{type}\n\n",
  "tags": "{name},景区,描述",
  "weight": 100,
  "status": "normal",
  "has_vector": 0
}
```

景点游玩提示模板示例：

```json
{
  "title": "{name}游玩提示",
  "content": "# {name}游玩提示\n\n{tips}\n\n## 通用提示\n- 请保管好个人财物\n- 爱护景区环境，不要乱扔垃圾\n- 遵守景区规定，文明游览\n",
  "tags": "{name},景点,游玩提示,注意事项,指南",
  "weight": 90,
  "status": "normal",
  "has_vector": 0
}
```

### 4. 控制器方法

在`ConverterTemplate`控制器中添加执行转换和初始化模板的方法：

```php
/**
 * 执行转换
 */
public function execute($ids = null)
{
    $row = $this->model->get($ids);
    if (!$row) {
        $this->error(__('No Results were found'));
    }

    $force = $this->request->param('force/b', false);

    // 执行转换
    $result = \addons\dsassistant\library\TemplateConverter::convert($ids, $force);

    if ($result['code']) {
        $this->success($result['msg'], null, $result['data']);
    } else {
        $this->error($result['msg']);
    }
}

/**
 * 批量执行转换
 */
public function executeAll()
{
    $ids = $this->request->param('ids');
    $force = $this->request->param('force/b', false);

    if (empty($ids)) {
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    $ids = explode(',', $ids);

    // 批量执行转换
    $results = \addons\dsassistant\library\TemplateConverter::batchConvert($ids, $force);

    // 处理结果...
}

/**
 * 初始化模板
 */
public function initialize()
{
    if ($this->request->isPost()) {
        $templates = $this->getInitialTemplates();

        // 创建或更新模板...
    }

    return $this->view->fetch();
}
```

## 设计优势

1. **简化设计**：使用单一的通用转换器，不需要为每种数据类型创建专门的转换器类
2. **模板驱动**：所有转换逻辑通过JSON模板配置实现，无需修改代码
3. **数据库管理**：通过数据库管理模板，支持动态配置
4. **增量同步**：支持基于时间或ID的增量同步，提高效率
5. **批量处理**：支持分批处理数据，避免内存溢出
6. **灵活配置**：可以为同一数据源配置多个不同内容类型的模板

## 使用方法

1. **初始化模板**：使用预定义的模板初始化系统
2. **执行转换**：选择模板执行转换，支持单个或批量执行
3. **自定义模板**：根据需要创建或修改模板，无需编写代码

## 注意事项
1. 确保模板内容格式正确，符合JSON规范
2. 模板中的变量使用`{field_name}`格式，会自动替换为数据源中对应字段的值
3. 增量同步依赖于同步字段，确保该字段存在且有值
4. 控制每批处理的数据量，避免性能问题

## 预计工作量
3-4人天
