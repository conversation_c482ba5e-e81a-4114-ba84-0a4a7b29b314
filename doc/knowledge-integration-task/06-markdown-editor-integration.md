# 任务6: Markdown编辑器集成

## 任务描述
在知识库编辑界面集成所见即所得的Markdown编辑器，替代原有的文本编辑框，提供更好的内容编辑体验。

## 涉及文件

### 需要修改的文件
1. `application/admin/view/dsassistant/knowledge/add.html` - 添加页面
2. `application/admin/view/dsassistant/knowledge/edit.html` - 编辑页面
3. `public/assets/js/backend/dsassistant/knowledge.js` - 添加编辑器初始化代码

### 需要创建的文件
1. `public/assets/libs/editor-md/` - 编辑器库文件夹（如选择Editor.md）

## 具体实现内容

### 1. 安装Markdown编辑器库

选择Editor.md作为Markdown编辑器（也可以选择其他编辑器）：

```bash
# 下载Editor.md
mkdir -p public/assets/libs/editor-md
wget -O editor-md.zip https://github.com/pandao/editor.md/archive/refs/heads/master.zip
unzip editor-md.zip -d public/assets/libs/
mv public/assets/libs/editor.md-master/* public/assets/libs/editor-md/
rm -rf public/assets/libs/editor.md-master editor-md.zip
```

### 2. 修改 `application/admin/view/dsassistant/knowledge/add.html`

替换原有的文本编辑框为Markdown编辑器：

```html
<div class="form-group">
    <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
    <div class="col-xs-12 col-sm-8">
        <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="">
    </div>
</div>

<div class="form-group">
    <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
    <div class="col-xs-12 col-sm-8">
        <div id="markdown-editor">
            <textarea id="c-content" data-rule="required" class="form-control" name="row[content]" rows="15"></textarea>
        </div>
    </div>
</div>

<div class="form-group">
    <label class="control-label col-xs-12 col-sm-2">{:__('Content_type')}:</label>
    <div class="col-xs-12 col-sm-8">
        {:build_select('row[content_type]', $contentTypeList, null, ['class'=>'form-control selectpicker', 'data-rule'=>'required'])}
    </div>
</div>

<!-- 其他字段... -->
```

### 3. 修改 `application/admin/view/dsassistant/knowledge/edit.html`

类似地修改编辑页面：

```html
<div class="form-group">
    <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
    <div class="col-xs-12 col-sm-8">
        <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}">
    </div>
</div>

<div class="form-group">
    <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
    <div class="col-xs-12 col-sm-8">
        <div id="markdown-editor">
            <textarea id="c-content" data-rule="required" class="form-control" name="row[content]" rows="15">{$row.content|htmlentities}</textarea>
        </div>
    </div>
</div>

<div class="form-group">
    <label class="control-label col-xs-12 col-sm-2">{:__('Content_type')}:</label>
    <div class="col-xs-12 col-sm-8">
        {:build_select('row[content_type]', $contentTypeList, $row.content_type, ['class'=>'form-control selectpicker', 'data-rule'=>'required'])}
    </div>
</div>

<!-- 其他字段... -->
```

### 4. 修改 `public/assets/js/backend/dsassistant/knowledge.js`

添加编辑器初始化代码：

```javascript
define(['jquery', 'bootstrap', 'backend', 'table', 'form', '../libs/editor-md/editormd.min'], function ($, undefined, Backend, Table, Form, editormd) {

    var Controller = {
        _queryString: '',
        index: function () {
            // 现有代码...
        },
        
        add: function () {
            Controller.api.bindevent();
            
            // 初始化Markdown编辑器
            Controller.initEditor();
        },
        
        edit: function () {
            Controller.api.bindevent();
            
            // 初始化Markdown编辑器
            Controller.initEditor();
        },
        
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            
            // 初始化Markdown编辑器
            initEditor: function() {
                // 检查编辑器元素是否存在
                if ($("#markdown-editor").length > 0) {
                    // 配置编辑器
                    var editor = editormd("markdown-editor", {
                        width: "100%",
                        height: 500,
                        path: "/assets/libs/editor-md/lib/",
                        toolbarIcons: function() {
                            return [
                                "undo", "redo", "|", 
                                "bold", "italic", "quote", "|", 
                                "h1", "h2", "h3", "h4", "h5", "h6", "|", 
                                "list-ul", "list-ol", "hr", "|",
                                "link", "image", "code", "table", "|",
                                "preview", "watch", "fullscreen"
                            ];
                        },
                        // 开启所见即所得模式
                        mode: "markdown",
                        // 开启实时预览
                        watch: true,
                        // 保存HTML到隐藏域
                        saveHTMLToTextarea: false,
                        // 图片上传
                        imageUpload: true,
                        imageFormats: ["jpg", "jpeg", "gif", "png", "webp"],
                        imageUploadURL: "/dsassistant/ajax/upload",
                        // 设置语言
                        lang: {
                            name: "zh-cn",
                            description: "开源在线Markdown编辑器",
                            tocTitle: "目录",
                            toolbar: {
                                // 工具栏按钮提示
                                undo: "撤销",
                                redo: "重做",
                                bold: "粗体",
                                italic: "斜体",
                                quote: "引用",
                                h1: "标题1",
                                h2: "标题2",
                                h3: "标题3",
                                h4: "标题4",
                                h5: "标题5",
                                h6: "标题6",
                                "list-ul": "无序列表",
                                "list-ol": "有序列表",
                                hr: "分隔线",
                                link: "链接",
                                image: "图片",
                                code: "代码",
                                table: "表格",
                                preview: "预览",
                                watch: "实时预览",
                                fullscreen: "全屏"
                            }
                        },
                        // 自定义模板
                        onload: function() {
                            // 添加模板按钮
                            var toolbar = this.toolbar;
                            toolbar.append('<div class="editormd-menu"><button type="button" class="btn btn-default btn-sm" id="insert-template">插入模板</button></div>');
                            
                            // 绑定模板按钮事件
                            $("#insert-template").on("click", function() {
                                // 显示模板选择对话框
                                var templates = {
                                    "景点描述": "# 景点名称\n\n景点描述内容\n\n## 基本信息\n- **地址**：景点地址\n- **开放时间**：开放时间\n- **门票**：票价信息\n\n## 特色亮点\n1. 特色一\n2. 特色二\n3. 特色三\n\n## 游玩提示\n- 提示一\n- 提示二",
                                    "开放信息": "# 开放信息\n\n## 开放时间\n- **常规时间**：9:00-17:00\n- **节假日**：8:00-18:00\n- **闭馆日**：周一闭馆\n\n## 门票信息\n- **成人票**：￥50\n- **儿童票**：￥25\n- **优惠政策**：老人、军人、残疾人等凭证免票",
                                    "交通指南": "# 交通指南\n\n## 公共交通\n1. **公交**：乘坐X路、Y路到Z站下车\n2. **地铁**：乘坐A线到B站下车\n\n## 自驾路线\n从市区出发，沿着C路行驶约D公里，见E标志右转即到\n\n## 停车信息\n停车场位于F位置，收费标准：G元/小时"
                                };
                                
                                var html = '<div class="template-dialog"><select class="form-control">';
                                for (var name in templates) {
                                    html += '<option value="' + name + '">' + name + '</option>';
                                }
                                html += '</select><div class="template-preview" style="margin-top:10px;border:1px solid #ddd;padding:10px;max-height:300px;overflow:auto;"></div></div>';
                                
                                layer.open({
                                    type: 1,
                                    title: '选择模板',
                                    area: ['500px', '450px'],
                                    content: html,
                                    success: function(layero, index) {
                                        var $select = $(layero).find("select");
                                        var $preview = $(layero).find(".template-preview");
                                        
                                        // 显示预览
                                        function updatePreview() {
                                            var name = $select.val();
                                            $preview.html('<pre>' + templates[name] + '</pre>');
                                        }
                                        
                                        // 初始预览
                                        updatePreview();
                                        
                                        // 选择变化时更新预览
                                        $select.on("change", updatePreview);
                                        
                                        // 添加插入按钮
                                        layer.setBtn(layero, ['插入', '取消']);
                                        
                                        // 绑定插入事件
                                        $(layero).find(".layui-layer-btn0").on("click", function() {
                                            var name = $select.val();
                                            editor.insertValue(templates[name]);
                                            layer.close(index);
                                        });
                                    }
                                });
                            });
                        }
                    });
                }
            }
        }
    };
    return Controller;
});
```

### 5. 添加编辑器样式和依赖

在 `application/admin/view/dsassistant/knowledge/add.html` 和 `application/admin/view/dsassistant/knowledge/edit.html` 的头部添加：

```html
<style>
    .editormd-menu {
        float: right;
        margin-right: 10px;
    }
</style>
```

## 注意事项
1. 确保编辑器能正确加载和初始化
2. 提供适当的模板和工具栏按钮，便于非技术管理员使用
3. 配置图片上传功能，确保能正确上传和显示图片
4. 考虑编辑器的响应式布局，适应不同屏幕尺寸

## 预计工作量
2-3人天
