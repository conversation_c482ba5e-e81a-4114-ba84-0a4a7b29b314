# 任务2: 向量搜索代码适配

## 任务描述
调整向量搜索相关代码，使其适配新的知识库表结构和字段命名。

## 涉及文件

### 需要修改的文件
1. `library/VectorSearchFactory.php` - 更新向量搜索工厂类
2. `library/vectordb/VectorDBFactory.php` - 更新向量数据库工厂类
3. `application/admin/controller/dsassistant/Knowledge.php` - 更新向量索引方法
4. `application/api/controller/dsassistant/VectorSearch.php` - 更新API控制器

### 需要创建的文件
无

## 具体修改内容

### 1. 修改 `library/VectorSearchFactory.php`

- 更新向量搜索实现，使用 `title` 和 `content` 字段替代 `question` 和 `answer`
- 示例修改:
  ```php
  // 修改搜索方法
  public function search($query, $topK = 5)
  {
      // 使用title和content字段生成向量
      // ...
  }
  ```

### 2. 修改 `library/vectordb/VectorDBFactory.php`

- 更新元数据结构定义，适配新字段
- 修改 `createStandardMetadata` 方法，处理新的字段结构
- 示例修改:
  ```php
  const METADATA_SCHEMA = [
      // 内容信息
      'content' => [
          'title' => '',        // 标题/主题
          'content' => '',      // 内容
          'summary' => ''       // 内容摘要
      ],
      // 分类信息
      'classification' => [
          'content_type' => '', // 内容类型
          'category_id' => 0,   // 分类ID
          'category' => '',     // 分类名称
          'tags' => []          // 标签/关键词数组
      ],
      // 来源信息
      'source' => [
          'source_type' => '',  // 来源类型
          'source_id' => 0,     // 来源ID
      ],
      // ...其他字段保持不变
  ];
  
  // 更新createStandardMetadata方法
  public static function createStandardMetadata($knowledgeRecord)
  {
      $metadata = [
          'content' => [
              'title' => $knowledgeRecord['title'] ?? '',
              'content' => $knowledgeRecord['content'] ?? '',
              // ...
          ],
          // ...其他字段处理
      ];
      
      return $metadata;
  }
  ```

### 3. 修改 `application/admin/controller/dsassistant/Knowledge.php`

- 更新向量索引方法，适配新字段
- 示例修改:
  ```php
  public function vectorIndexSingle($ids = null)
  {
      // 使用新字段结构生成向量
      // ...
  }
  ```

### 4. 修改 `application/api/controller/dsassistant/VectorSearch.php`

- 更新API方法，适配新字段
- 示例修改:
  ```php
  public function search()
  {
      $question = $this->request->post('question');
      // 使用新字段结构进行搜索
      // ...
  }
  ```

## 注意事项
1. 确保向量生成逻辑正确处理Markdown格式的内容
2. 保持向量搜索API的兼容性，避免影响前端调用
3. 考虑添加字段映射层，以支持旧版本API调用

## 预计工作量
3-4人天
