# 任务7: Excel批量导入功能

## 任务描述
开发Excel批量导入功能，允许管理员通过Excel模板批量导入景区/景点数据到知识库。

## 涉及文件

### 需要创建的文件
1. `application/admin/controller/dsassistant/Import.php` - 导入控制器
2. `application/admin/view/dsassistant/import/index.html` - 导入页面
3. `application/admin/view/dsassistant/import/preview.html` - 预览页面
4. `public/assets/js/backend/dsassistant/import.js` - 导入JS
5. `public/example/scenic_import_template.xlsx` - 景区导入模板
6. `public/example/scenicspot_import_template.xlsx` - 景点导入模板

### 需要修改的文件
无

## 具体实现内容

### 1. 创建 `application/admin/controller/dsassistant/Import.php`

```php
<?php

namespace app\admin\controller\dsassistant;

use app\common\controller\Backend;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\Db;
use think\Exception;
use addons\dsassistant\library\KnowledgeConverter;

/**
 * 知识库批量导入
 */
class Import extends Backend
{
    protected $noNeedRight = ['downloadTemplate'];
    
    /**
     * 导入首页
     */
    public function index()
    {
        if ($this->request->isPost()) {
            $file = $this->request->file('file');
            if (!$file) {
                $this->error(__('Please select a file to import'));
            }
            
            $importType = $this->request->post('import_type');
            if (!in_array($importType, ['scenic', 'scenicspot'])) {
                $this->error(__('Invalid import type'));
            }
            
            // 上传Excel文件
            $info = $file->validate(['ext' => 'xlsx,xls'])->move(ROOT_PATH . 'public' . DS . 'uploads' . DS . 'import');
            if (!$info) {
                $this->error($file->getError());
            }
            
            $filePath = $info->getSaveName();
            $fullPath = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'import' . DS . $filePath;
            
            try {
                // 读取Excel文件
                $spreadsheet = IOFactory::load($fullPath);
                $worksheet = $spreadsheet->getActiveSheet();
                $data = $worksheet->toArray();
                
                // 移除标题行
                $headers = array_shift($data);
                
                // 验证数据
                $validData = [];
                $errors = [];
                
                foreach ($data as $rowIndex => $row) {
                    // 跳过空行
                    if (empty(array_filter($row))) {
                        continue;
                    }
                    
                    $rowData = [];
                    $rowErrors = [];
                    
                    // 根据导入类型处理不同的字段
                    if ($importType == 'scenic') {
                        // 景区数据处理
                        if (empty($row[0])) { // 名称
                            $rowErrors[] = __('Name cannot be empty');
                        } else {
                            $rowData['name'] = $row[0];
                        }
                        
                        $rowData['description'] = $row[1] ?? ''; // 描述
                        $rowData['type'] = $row[2] ?? 'circle'; // 类型
                        $rowData['center_lat'] = $row[3] ?? 0; // 中心点纬度
                        $rowData['center_lng'] = $row[4] ?? 0; // 中心点经度
                        $rowData['radius'] = $row[5] ?? 0; // 半径
                        $rowData['status'] = $row[6] ?? 'normal'; // 状态
                    } else {
                        // 景点数据处理
                        if (empty($row[0])) { // 名称
                            $rowErrors[] = __('Name cannot be empty');
                        } else {
                            $rowData['name'] = $row[0];
                        }
                        
                        // 所属景区
                        if (!empty($row[1])) {
                            $scenic = Db::name('ds_scenic')->where('name', $row[1])->find();
                            if ($scenic) {
                                $rowData['scenic_id'] = $scenic['id'];
                            } else {
                                $rowErrors[] = __('Scenic area not found') . ': ' . $row[1];
                            }
                        } else {
                            $rowErrors[] = __('Scenic area cannot be empty');
                        }
                        
                        $rowData['description'] = $row[2] ?? ''; // 描述
                        $rowData['longitude'] = $row[3] ?? 0; // 经度
                        $rowData['latitude'] = $row[4] ?? 0; // 纬度
                        $rowData['address'] = $row[5] ?? ''; // 地址
                        $rowData['opening_hours'] = $row[6] ?? ''; // 开放时间
                        $rowData['ticket_price'] = $row[7] ?? ''; // 票价
                        $rowData['tips'] = $row[8] ?? ''; // 游玩提示
                        $rowData['status'] = $row[9] ?? 'normal'; // 状态
                    }
                    
                    if (empty($rowErrors)) {
                        $validData[] = $rowData;
                    } else {
                        $errors[] = __('Row %d', $rowIndex + 2) . ': ' . implode(', ', $rowErrors);
                    }
                }
                
                // 如果有错误，返回错误信息
                if (!empty($errors)) {
                    $this->error(__('Data validation failed') . '<br>' . implode('<br>', $errors));
                }
                
                // 保存数据到会话，用于预览和导入
                session('import_data', $validData);
                session('import_type', $importType);
                
                // 跳转到预览页面
                $this->success(__('Data validated successfully'), url('preview'));
            } catch (Exception $e) {
                $this->error(__('Failed to parse Excel file') . ': ' . $e->getMessage());
            }
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 预览导入数据
     */
    public function preview()
    {
        $importData = session('import_data');
        $importType = session('import_type');
        
        if (empty($importData) || empty($importType)) {
            $this->error(__('No import data found'), url('index'));
        }
        
        if ($this->request->isPost()) {
            $knowledgeTypes = $this->request->post('knowledge_types/a');
            if (empty($knowledgeTypes)) {
                $this->error(__('Please select at least one knowledge type'));
            }
            
            // 开始导入
            $converter = new KnowledgeConverter();
            $knowledgeModel = new \app\admin\model\dsassistant\Knowledge();
            
            $successCount = 0;
            $errorCount = 0;
            
            // 根据导入类型处理数据
            if ($importType == 'scenic') {
                // 景区数据导入
                foreach ($importData as $data) {
                    try {
                        // 先保存到景区表
                        $scenicId = Db::name('ds_scenic')->insertGetId($data);
                        
                        if ($scenicId) {
                            $data['id'] = $scenicId;
                            
                            // 转换为知识库条目
                            $options = ['types' => $knowledgeTypes];
                            $knowledgeItems = $converter::convert('scenic', $data, $options);
                            
                            // 保存到知识库
                            foreach ($knowledgeItems as $item) {
                                $result = $knowledgeModel->save($item);
                                if ($result) {
                                    $successCount++;
                                } else {
                                    $errorCount++;
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $errorCount++;
                        \think\Log::error("导入景区数据失败: " . $e->getMessage());
                    }
                }
            } else {
                // 景点数据导入
                foreach ($importData as $data) {
                    try {
                        // 先保存到景点表
                        $spotId = Db::name('ds_scenic_spot')->insertGetId($data);
                        
                        if ($spotId) {
                            $data['id'] = $spotId;
                            
                            // 转换为知识库条目
                            $options = ['types' => $knowledgeTypes];
                            $knowledgeItems = $converter::convert('scenicspot', $data, $options);
                            
                            // 保存到知识库
                            foreach ($knowledgeItems as $item) {
                                $result = $knowledgeModel->save($item);
                                if ($result) {
                                    $successCount++;
                                } else {
                                    $errorCount++;
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $errorCount++;
                        \think\Log::error("导入景点数据失败: " . $e->getMessage());
                    }
                }
            }
            
            // 清除会话数据
            session('import_data', null);
            session('import_type', null);
            
            if ($errorCount > 0) {
                $this->success(__('Import completed with some errors') . "<br>" . __('Success: %d, Error: %d', $successCount, $errorCount), url('index'));
            } else {
                $this->success(__('Import completed successfully') . "<br>" . __('Total imported: %d', $successCount), url('index'));
            }
        }
        
        $this->assign('importData', $importData);
        $this->assign('importType', $importType);
        
        return $this->view->fetch();
    }
    
    /**
     * 下载导入模板
     */
    public function downloadTemplate()
    {
        $type = $this->request->param('type');
        if (!in_array($type, ['scenic', 'scenicspot'])) {
            $this->error(__('Invalid template type'));
        }
        
        $file = ROOT_PATH . 'public' . DS . 'example' . DS . $type . '_import_template.xlsx';
        if (!file_exists($file)) {
            $this->error(__('Template file not found'));
        }
        
        // 下载文件
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename=' . basename($file));
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));
        ob_clean();
        flush();
        readfile($file);
        exit;
    }
}
```

### 2. 创建 `application/admin/view/dsassistant/import/index.html`

```html
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>批量导入到知识库</em></div>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h4>导入说明</h4>
                    <p>1. 请先下载对应的Excel模板文件</p>
                    <p>2. 按照模板格式填写数据</p>
                    <p>3. 上传填写好的Excel文件</p>
                    <p>4. 系统会自动验证数据并生成预览</p>
                    <p>5. 确认无误后，点击导入按钮完成导入</p>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">景区数据导入</div>
                            <div class="panel-body">
                                <p>导入景区数据并生成相关知识库条目</p>
                                <p><a href="{:url('downloadTemplate')}?type=scenic" class="btn btn-info">下载景区导入模板</a></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">景点数据导入</div>
                            <div class="panel-body">
                                <p>导入景点数据并生成相关知识库条目</p>
                                <p><a href="{:url('downloadTemplate')}?type=scenicspot" class="btn btn-info">下载景点导入模板</a></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form id="import-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('index')}" enctype="multipart/form-data">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">导入类型:</label>
                        <div class="col-xs-12 col-sm-8">
                            <div class="radio">
                                <label><input type="radio" name="import_type" value="scenic" checked> 景区数据</label>
                            </div>
                            <div class="radio">
                                <label><input type="radio" name="import_type" value="scenicspot"> 景点数据</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">选择文件:</label>
                        <div class="col-xs-12 col-sm-8">
                            <div class="input-group">
                                <input id="c-file" class="form-control" size="50" name="file" type="file" data-rule="required" data-msg-required="请选择Excel文件">
                                <div class="input-group-addon no-border no-padding">
                                    <span><button type="button" id="plupload-file" class="btn btn-danger plupload" data-input-id="c-file"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                </div>
                            </div>
                            <div class="help-block">支持 .xlsx, .xls 格式</div>
                        </div>
                    </div>
                    
                    <div class="form-group layer-footer">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <div class="col-xs-12 col-sm-8">
                            <button type="submit" class="btn btn-success btn-embossed">验证并预览</button>
                            <button type="reset" class="btn btn-default btn-embossed">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
```

### 3. 创建 `application/admin/view/dsassistant/import/preview.html`

```html
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>导入数据预览</em></div>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <p>请确认以下数据无误，然后选择要生成的知识类型并点击"确认导入"按钮。</p>
                    <p>导入类型: <strong>{$importType == 'scenic' ? '景区数据' : '景点数据'}</strong></p>
                    <p>数据条数: <strong>{:count($importData)}</strong></p>
                </div>
                
                <form id="import-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('preview')}">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">选择要生成的知识类型:</label>
                        <div class="col-xs-12 col-sm-8">
                            {if $importType == 'scenic'}
                            <div class="checkbox">
                                <label><input type="checkbox" name="knowledge_types[]" value="description" checked> 基本描述</label>
                            </div>
                            <div class="checkbox">
                                <label><input type="checkbox" name="knowledge_types[]" value="info" checked> 开放信息</label>
                            </div>
                            <div class="checkbox">
                                <label><input type="checkbox" name="knowledge_types[]" value="location" checked> 位置交通</label>
                            </div>
                            {else}
                            <div class="checkbox">
                                <label><input type="checkbox" name="knowledge_types[]" value="description" checked> 基本描述</label>
                            </div>
                            <div class="checkbox">
                                <label><input type="checkbox" name="knowledge_types[]" value="tips" checked> 游玩提示</label>
                            </div>
                            <div class="checkbox">
                                <label><input type="checkbox" name="knowledge_types[]" value="ticket" checked> 票价信息</label>
                            </div>
                            <div class="checkbox">
                                <label><input type="checkbox" name="knowledge_types[]" value="opening" checked> 开放时间</label>
                            </div>
                            {/if}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">数据预览:</label>
                        <div class="col-xs-12 col-sm-8">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            {if $importType == 'scenic'}
                                            <th>名称</th>
                                            <th>描述</th>
                                            <th>类型</th>
                                            <th>中心点纬度</th>
                                            <th>中心点经度</th>
                                            <th>状态</th>
                                            {else}
                                            <th>名称</th>
                                            <th>所属景区</th>
                                            <th>描述</th>
                                            <th>经度</th>
                                            <th>纬度</th>
                                            <th>开放时间</th>
                                            <th>票价</th>
                                            <th>状态</th>
                                            {/if}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {foreach $importData as $row}
                                        <tr>
                                            {if $importType == 'scenic'}
                                            <td>{$row.name}</td>
                                            <td>{$row.description|substr=0,30}...</td>
                                            <td>{$row.type}</td>
                                            <td>{$row.center_lat}</td>
                                            <td>{$row.center_lng}</td>
                                            <td>{$row.status}</td>
                                            {else}
                                            <td>{$row.name}</td>
                                            <td>{$row.scenic_id}</td>
                                            <td>{$row.description|substr=0,30}...</td>
                                            <td>{$row.longitude}</td>
                                            <td>{$row.latitude}</td>
                                            <td>{$row.opening_hours}</td>
                                            <td>{$row.ticket_price}</td>
                                            <td>{$row.status}</td>
                                            {/if}
                                        </tr>
                                        {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group layer-footer">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <div class="col-xs-12 col-sm-8">
                            <button type="submit" class="btn btn-success btn-embossed">确认导入</button>
                            <a href="{:url('index')}" class="btn btn-default btn-embossed">返回</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
```

### 4. 创建 `public/assets/js/backend/dsassistant/import.js`

```javascript
define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            Form.api.bindevent($("form[role=form]"));
            
            // 文件上传
            $("input[name='file']").change(function() {
                var fileInput = this;
                var fileName = fileInput.value;
                
                // 检查文件扩展名
                var extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
                if (extension != 'xlsx' && extension != 'xls') {
                    Layer.alert('请选择Excel文件 (.xlsx, .xls)');
                    fileInput.value = '';
                    return false;
                }
            });
        },
        
        preview: function () {
            Form.api.bindevent($("form[role=form]"));
            
            // 至少选择一种知识类型
            $("form").submit(function() {
                if ($("input[name='knowledge_types[]']:checked").length === 0) {
                    Layer.alert('请至少选择一种知识类型');
                    return false;
                }
                return true;
            });
        },
        
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
```

### 5. 创建Excel模板文件

需要创建两个Excel模板文件：
- `public/example/scenic_import_template.xlsx` - 景区导入模板
- `public/example/scenicspot_import_template.xlsx` - 景点导入模板

这些模板应包含适当的列标题和示例数据，以及填写说明。

## 注意事项
1. 确保Excel解析库（PhpSpreadsheet）已正确安装
2. 提供清晰的模板和导入说明
3. 添加适当的数据验证和错误处理
4. 考虑大批量导入的性能问题，可能需要分批处理

## 预计工作量
3-4人天
