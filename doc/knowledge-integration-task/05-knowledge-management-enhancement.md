# 任务5: 知识库管理界面增强

## 任务描述
增强知识库管理界面，支持按来源和内容类型过滤，添加"查看关联数据"和"重新生成"功能。

## 涉及文件

### 需要修改的文件
1. `application/admin/controller/dsassistant/Knowledge.php` - 添加新功能
2. `application/admin/view/dsassistant/knowledge/index.html` - 更新列表页面
3. `public/assets/js/backend/dsassistant/knowledge.js` - 添加新功能按钮和事件

### 需要创建的文件
1. `application/admin/view/dsassistant/knowledge/regenerate.html` - 重新生成页面

## 具体实现内容

### 1. 修改 `application/admin/controller/dsassistant/Knowledge.php`

添加新方法和增强现有方法：

```php
/**
 * 初始化
 */
public function _initialize()
{
    parent::_initialize();
    $this->model = new \app\admin\model\dsassistant\Knowledge;
    $this->assign("statusList", $this->model->getStatusList());
    $this->assign("contentTypeList", $this->model->getContentTypeList());
    $this->assign("sourceTypeList", $this->model->getSourceTypeList());
}

/**
 * 查看
 */
public function index()
{
    //当前是否为关联查询
    $this->relationSearch = true;
    //设置过滤方法
    $this->request->filter(['strip_tags', 'trim']);
    if ($this->request->isAjax()) {
        //如果发送的来源是Selectpage，则转发到Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();

        $list = $this->model
                ->with(['category'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

        foreach ($list as $row) {
            $row->getRelation('category')->visible(['category']);
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }
    return $this->view->fetch();
}

/**
 * 查看关联数据
 */
public function viewSource()
{
    $id = $this->request->param('ids');
    if (!$id) {
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }
    
    $knowledge = $this->model->find($id);
    if (!$knowledge) {
        $this->error(__('Knowledge not found'));
    }
    
    // 检查是否有来源信息
    if (empty($knowledge['source_type']) || empty($knowledge['source_id'])) {
        $this->error(__('No source information available'));
    }
    
    // 根据来源类型跳转到相应页面
    switch ($knowledge['source_type']) {
        case 'scenic':
            $url = url('dsassistant/scenic/edit', ['ids' => $knowledge['source_id']]);
            break;
        case 'scenicspot':
            $url = url('dsassistant/scenicspot/edit', ['ids' => $knowledge['source_id']]);
            break;
        default:
            $this->error(__('Unsupported source type'));
    }
    
    $this->success('', $url);
}

/**
 * 重新生成知识内容
 */
public function regenerate()
{
    $id = $this->request->param('ids');
    if (!$id) {
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }
    
    if ($this->request->isPost()) {
        $knowledge = $this->model->find($id);
        if (!$knowledge) {
            $this->error(__('Knowledge not found'));
        }
        
        // 检查是否有来源信息
        if (empty($knowledge['source_type']) || empty($knowledge['source_id'])) {
            $this->error(__('No source information available'));
        }
        
        try {
            // 获取源数据
            $sourceData = null;
            switch ($knowledge['source_type']) {
                case 'scenic':
                    $sourceModel = new \app\admin\model\dsassistant\Scenic();
                    $sourceData = $sourceModel->find($knowledge['source_id']);
                    break;
                case 'scenicspot':
                    $sourceModel = new \app\admin\model\dsassistant\Scenicspot();
                    $sourceData = $sourceModel->find($knowledge['source_id']);
                    break;
                default:
                    $this->error(__('Unsupported source type'));
            }
            
            if (!$sourceData) {
                $this->error(__('Source data not found'));
            }
            
            // 使用转换引擎重新生成内容
            $converter = new \addons\dsassistant\library\KnowledgeConverter();
            $options = ['types' => [$knowledge['content_type']]];
            $items = $converter::convert($knowledge['source_type'], $sourceData->toArray(), $options);
            
            // 找到匹配的内容类型
            $newData = null;
            foreach ($items as $item) {
                if ($item['content_type'] == $knowledge['content_type']) {
                    $newData = $item;
                    break;
                }
            }
            
            if (!$newData) {
                $this->error(__('Failed to generate new content'));
            }
            
            // 更新知识内容
            $knowledge->title = $newData['title'];
            $knowledge->content = $newData['content'];
            $knowledge->tags = $newData['tags'];
            $knowledge->structured_data = $newData['structured_data'];
            $knowledge->has_vector = 0; // 标记需要重新生成向量
            $knowledge->save();
            
            $this->success(__('Content regenerated successfully'));
        } catch (\Exception $e) {
            $this->error(__('Failed to regenerate content') . ': ' . $e->getMessage());
        }
    }
    
    $knowledge = $this->model->find($id);
    $this->assign('knowledge', $knowledge);
    return $this->view->fetch();
}
```

### 2. 修改 `application/admin/view/dsassistant/knowledge/index.html`

更新列表页面，添加过滤选项：

```html
<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('dsassistant/knowledge/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('dsassistant/knowledge/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('dsassistant/knowledge/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-import {:$auth->check('dsassistant/knowledge/import')?'':'hide'}" title="{:__('Import')}" id="btn-import-file" data-url="ajax/upload" data-mimetype="csv,xls,xlsx" data-multiple="false"><i class="fa fa-upload"></i> {:__('Import')}</a>

                        <div class="dropdown btn-group {:$auth->check('dsassistant/knowledge/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>

                        <a class="btn btn-success btn-recyclebin btn-dialog {:$auth->check('dsassistant/knowledge/recyclebin')?'':'hide'}" href="dsassistant/knowledge/recyclebin" title="{:__('Recycle bin')}"><i class="fa fa-recycle"></i> {:__('Recycle bin')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('dsassistant/knowledge/edit')}"
                           data-operate-del="{:$auth->check('dsassistant/knowledge/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
```

### 3. 修改 `public/assets/js/backend/dsassistant/knowledge.js`

更新JS文件，添加新功能按钮和过滤器：

```javascript
define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        _queryString: '',
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'dsassistant/knowledge/index' + Controller._queryString,
                    add_url: 'dsassistant/knowledge/add' + Controller._queryString,
                    edit_url: 'dsassistant/knowledge/edit',
                    del_url: 'dsassistant/knowledge/del',
                    multi_url: 'dsassistant/knowledge/multi',
                    import_url: 'dsassistant/knowledge/import',
                    table: 'ds_knowledge',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'title', title: __('Title'), operate: 'LIKE'},
                        {field: 'content_type', title: __('Content_type'), searchList: {"description":__('Content type description'),"faq":__('Content type faq'),"guide":__('Content type guide'),"data":__('Content type data'),"story":__('Content type story'),"warning":__('Content type warning'),"seasonal":__('Content type seasonal')}, formatter: Table.api.formatter.normal},
                        {field: 'source_type', title: __('Source_type'), searchList: {"scenic":__('Source type scenic'),"scenicspot":__('Source type scenicspot'),"manual":__('Source type manual'),"imported":__('Source type imported'),"generated":__('Source type generated')}, formatter: Table.api.formatter.normal},
                        {field: 'tags', title: __('Tags'), operate: 'LIKE'},
                        {field: 'weight', title: __('Weight')},
                        {field: 'valid_from', title: __('Valid_from'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'valid_until', title: __('Valid_until'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Status normal'),"hidden":__('Status hidden')}, formatter: Table.api.formatter.status},
                        {field: 'has_vector', title: __('Has_vector')},
                        {field: 'category.category', title: __('Category.category'), operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: function (value, row, index) {
                            var table = this.table;
                            // 操作栏按钮
                            var buttons = [];
                            
                            // 编辑
                            buttons.push({
                                name: 'edit',
                                icon: 'fa fa-pencil',
                                title: __('Edit'),
                                extend: 'data-toggle="tooltip"',
                                classname: 'btn btn-xs btn-success btn-editone',
                            });
                            
                            // 删除
                            buttons.push({
                                name: 'del',
                                icon: 'fa fa-trash',
                                title: __('Delete'),
                                extend: 'data-toggle="tooltip"',
                                classname: 'btn btn-xs btn-danger btn-delone'
                            });
                            
                            // 查看关联数据（仅当有来源信息时显示）
                            if (row.source_type && row.source_id) {
                                buttons.push({
                                    name: 'view-source',
                                    icon: 'fa fa-link',
                                    title: __('View source'),
                                    extend: 'data-toggle="tooltip"',
                                    classname: 'btn btn-xs btn-info btn-view-source',
                                    url: 'dsassistant/knowledge/viewSource'
                                });
                                
                                // 重新生成
                                buttons.push({
                                    name: 'regenerate',
                                    icon: 'fa fa-refresh',
                                    title: __('Regenerate'),
                                    extend: 'data-toggle="tooltip"',
                                    classname: 'btn btn-xs btn-warning btn-dialog',
                                    url: 'dsassistant/knowledge/regenerate'
                                });
                            }
                            
                            return Table.api.formatter.operate.call(this, value, row, index, {buttons: buttons});
                        }}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        
        // 其他方法...
    };
    return Controller;
});
```

### 4. 创建 `application/admin/view/dsassistant/knowledge/regenerate.html`

重新生成页面：

```html
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>重新生成知识内容</em></div>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <p>此操作将从原始数据源重新生成知识内容。</p>
                    <p>当前知识来源: <strong>{$knowledge.source_type|ucfirst}</strong> (ID: {$knowledge.source_id})</p>
                    <p>内容类型: <strong>{$knowledge.content_type}</strong></p>
                    <p>注意: 重新生成将覆盖当前内容，且需要重新生成向量表示。</p>
                </div>
                <form id="regenerate-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('regenerate')}?ids={$Think.get.ids}">
                    <div class="form-group layer-footer">
                        <div class="col-xs-12">
                            <button type="submit" class="btn btn-success btn-embossed">确认重新生成</button>
                            <button type="reset" class="btn btn-default btn-embossed">取消</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
```

## 注意事项
1. 确保新增的过滤功能正确实现
2. "查看关联数据"功能应正确跳转到源数据页面
3. "重新生成"功能应保留知识条目ID和其他元数据，只更新内容相关字段
4. 添加适当的错误处理和用户提示

## 预计工作量
2-3人天
