# 任务4: 景区/景点导入功能开发

## 任务描述
在景区和景点管理界面中添加"导入到知识库"功能，允许管理员将景区/景点数据一键导入到知识库。

## 涉及文件

### 需要修改的文件
1. `application/admin/controller/dsassistant/Scenic.php` - 添加导入功能
2. `application/admin/controller/dsassistant/Scenicspot.php` - 添加导入功能
3. `public/assets/js/backend/dsassistant/scenic.js` - 添加导入按钮
4. `public/assets/js/backend/dsassistant/scenicspot.js` - 添加导入按钮

### 需要创建的文件
1. `application/admin/view/dsassistant/scenic/import_knowledge.html` - 景区导入向导页面
2. `application/admin/view/dsassistant/scenicspot/import_knowledge.html` - 景点导入向导页面

## 具体实现内容

### 1. 修改 `application/admin/controller/dsassistant/Scenic.php`

添加导入到知识库的方法：

```php
/**
 * 导入到知识库
 */
public function importKnowledge()
{
    $ids = $this->request->param('ids');
    if (!$ids) {
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }
    
    if ($this->request->isPost()) {
        $types = $this->request->post('types/a');
        if (!$types) {
            $this->error(__('Please select at least one knowledge type'));
        }
        
        // 获取景区数据
        $ids = explode(',', $ids);
        $scenicList = $this->model->where('id', 'in', $ids)->select();
        
        if (!$scenicList) {
            $this->error(__('No valid data found'));
        }
        
        // 使用转换引擎生成知识库条目
        $converter = new \addons\dsassistant\library\KnowledgeConverter();
        $options = ['types' => $types];
        
        $knowledgeModel = new \app\admin\model\dsassistant\Knowledge();
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($scenicList as $scenic) {
            try {
                // 转换数据
                $knowledgeItems = $converter::convert('scenic', $scenic->toArray(), $options);
                
                // 保存到知识库
                foreach ($knowledgeItems as $item) {
                    $result = $knowledgeModel->save($item);
                    if ($result) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                }
            } catch (\Exception $e) {
                $errorCount++;
                \think\Log::error("导入景区到知识库失败: " . $e->getMessage());
            }
        }
        
        if ($errorCount > 0) {
            $this->success("导入完成，成功: {$successCount}，失败: {$errorCount}");
        } else {
            $this->success("导入成功，共导入 {$successCount} 条知识");
        }
        return;
    }
    
    // 获取景区数据用于预览
    $ids = explode(',', $ids);
    $scenicList = $this->model->where('id', 'in', $ids)->select();
    $this->assign('scenicList', $scenicList);
    
    return $this->view->fetch();
}
```

### 2. 修改 `application/admin/controller/dsassistant/Scenicspot.php`

添加类似的导入到知识库方法，但针对景点数据。

### 3. 创建 `application/admin/view/dsassistant/scenic/import_knowledge.html`

景区导入向导页面：

```html
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>导入到知识库</em></div>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <form id="import-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('importKnowledge')}?ids={$Think.get.ids}">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">选择要生成的知识类型:</label>
                        <div class="col-xs-12 col-sm-8">
                            <div class="checkbox">
                                <label><input type="checkbox" name="types[]" value="description" checked> 基本描述</label>
                            </div>
                            <div class="checkbox">
                                <label><input type="checkbox" name="types[]" value="info" checked> 开放信息</label>
                            </div>
                            <div class="checkbox">
                                <label><input type="checkbox" name="types[]" value="location" checked> 位置交通</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">预览:</label>
                        <div class="col-xs-12 col-sm-8">
                            <div class="panel panel-default">
                                <div class="panel-heading">将导入以下景区数据</div>
                                <div class="panel-body">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>名称</th>
                                                <th>描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach $scenicList as $scenic}
                                            <tr>
                                                <td>{$scenic.id}</td>
                                                <td>{$scenic.name}</td>
                                                <td>{$scenic.description|substr=0,50}...</td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group layer-footer">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <div class="col-xs-12 col-sm-8">
                            <button type="submit" class="btn btn-success btn-embossed">确认导入</button>
                            <button type="reset" class="btn btn-default btn-embossed">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
```

### 4. 创建 `application/admin/view/dsassistant/scenicspot/import_knowledge.html`

景点导入向导页面，类似于景区导入页面，但选项和预览内容针对景点数据。

### 5. 修改 `public/assets/js/backend/dsassistant/scenic.js`

添加导入按钮：

```javascript
// 在表格初始化后添加自定义按钮
table.on('post-body.bs.table', function (e, settings, json, xhr) {
    var importBtn = '<a href="javascript:;" class="btn btn-info btn-import-knowledge btn-xs"><i class="fa fa-book"></i> 导入到知识库</a>';
    
    // 添加到操作列
    $(".btn-editone", this).each(function () {
        $(this).parent().prepend(importBtn);
    });
    
    // 绑定点击事件
    $(".btn-import-knowledge", this).click(function () {
        var id = $(this).closest("tr").attr("data-id");
        var url = "dsassistant/scenic/importKnowledge?ids=" + id;
        Fast.api.open(url, "导入到知识库", {area: ['800px', '600px']});
    });
});

// 添加批量导入按钮
$(".toolbar").prepend('<div class="btn-group"><a href="javascript:;" class="btn btn-info btn-batch-import-knowledge"><i class="fa fa-book"></i> 批量导入到知识库</a></div>');

// 绑定批量导入按钮事件
$(document).on("click", ".btn-batch-import-knowledge", function () {
    var ids = Table.api.selectedids(table);
    if (ids.length === 0) {
        Layer.alert("请选择要导入的景区");
        return;
    }
    var url = "dsassistant/scenic/importKnowledge?ids=" + ids.join(",");
    Fast.api.open(url, "批量导入到知识库", {area: ['800px', '600px']});
});
```

### 6. 修改 `public/assets/js/backend/dsassistant/scenicspot.js`

添加类似的导入按钮和事件处理。

## 注意事项
1. 确保导入功能能够处理单个和批量导入场景
2. 提供清晰的预览和选项，让管理员了解将生成什么内容
3. 添加适当的错误处理和日志记录
4. 考虑导入过程中的性能问题，特别是批量导入时

## 预计工作量
3-4人天
