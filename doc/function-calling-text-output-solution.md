# Function Calling 文本输出解决方案

## 问题描述

用户反馈在启用Function Calling后，输入"介绍一下天空栈道"时，AI只返回函数调用结果而没有文本说明，导致用户体验不佳。

## 问题分析

### 根本原因

当AI模型启用Function Calling时，模型可能会认为某些问题最好通过函数调用来回答，而选择不提供文本说明。这是因为：

1. **模型行为特性**：DeepSeek等大模型在Function Calling模式下，可能优先选择函数调用而忽略文本输出
2. **系统提示词不够明确**：没有明确指示模型在调用函数时也要提供文本说明
3. **用户期望不匹配**：用户期望看到文字解释，而不仅仅是功能卡片

### 期望的行为

理想的Function Calling响应应该包含：
- **文本说明**：向用户解释正在做什么，提供友好的交互体验
- **函数调用**：提供具体的功能操作，如显示地图、查看详情等

例如：
```json
{
    "answer": "我为您查询了天空栈道的详细信息，您可以点击下方卡片查看完整的景点介绍。",
    "functionCalls": [
        {
            "name": "showScenicSpotDetails",
            "parameters": {
                "spotId": "123",
                "spotName": "天空栈道"
            }
        }
    ]
}
```

## 解决方案

### 1. 增强系统提示词

#### 方案A：硬编码增强提示词
在`DsAssistant.php`的`callDeepSeek`方法中，当启用Function Calling时自动添加增强提示词：

```php
// 如果启用了函数调用，增强系统提示词
$systemPrompt = $baseSystemPrompt;
if ($enableFunctions) {
    $functionCallingPrompt = "重要提示：当你需要调用函数时，请同时提供文字说明。即使你调用了函数，也要用自然语言向用户解释你正在做什么或提供相关信息。不要只返回函数调用而不提供任何文字回复。例如：\n- 如果调用景点详情函数，请说明你正在为用户查询该景点的详细信息\n- 如果调用地图函数，请告诉用户你正在为他们显示位置信息\n- 始终保持友好和专业的语气，让用户感受到你的帮助";
    $systemPrompt .= "\n\n" . $functionCallingPrompt;
}
```

#### 方案B：可配置的增强提示词（推荐）
在配置文件中添加`function_calling_prompt`配置项，允许管理员自定义Function Calling的提示词：

```php
[
    'name' => 'function_calling_prompt',
    'title' => 'Function Calling提示词',
    'type' => 'textarea',
    'group' => 'function_calling',
    'value' => "重要提示：当你需要调用函数时，请同时提供文字说明...",
    'tip' => '当启用Function Calling时，会将此提示词附加到系统提示词中'
]
```

### 2. 优化提示词内容

#### 核心原则
- **明确指示**：明确告诉AI必须同时提供文本和函数调用
- **具体示例**：提供具体的示例说明期望的行为
- **用户体验导向**：强调用户体验和友好交互

#### 推荐的提示词模板
```
重要提示：当你需要调用函数时，请同时提供文字说明。即使你调用了函数，也要用自然语言向用户解释你正在做什么或提供相关信息。不要只返回函数调用而不提供任何文字回复。

具体要求：
1. 总是先用文字回应用户的问题
2. 解释你将要执行的操作
3. 保持友好和专业的语气
4. 让用户感受到你的帮助

示例：
- 如果调用景点详情函数，请说："我为您查询了[景点名称]的详细信息，您可以点击下方卡片查看完整介绍。"
- 如果调用地图函数，请说："我为您准备了[地点]的位置信息，点击下方可以查看地图导航。"
- 如果调用搜索函数，请说："我帮您搜索了相关信息，以下是为您找到的结果。"
```

### 3. 实现步骤

#### 步骤1：更新配置文件
在`config.php`中添加Function Calling提示词配置：

```php
[
    'name' => 'function_calling_prompt',
    'title' => 'Function Calling提示词',
    'type' => 'textarea',
    'group' => 'function_calling',
    'content' => [],
    'value' => "重要提示：当你需要调用函数时，请同时提供文字说明...",
    'rule' => '',
    'msg' => '',
    'tip' => '当启用Function Calling时，会将此提示词附加到系统提示词中，确保AI同时提供文本说明和函数调用',
    'ok' => '',
    'extend' => '',
]
```

#### 步骤2：修改DsAssistant.php
在`callDeepSeek`方法中使用配置的提示词：

```php
// 如果启用了函数调用，增强系统提示词
$systemPrompt = $baseSystemPrompt;
if ($enableFunctions) {
    $functionCallingPrompt = $config['function_calling_prompt'] ?? "默认提示词...";
    $systemPrompt .= "\n\n" . $functionCallingPrompt;
    
    Log::record("启用Function Calling，已添加增强提示词", 'info');
}
```

#### 步骤3：创建测试命令
创建测试命令验证修改效果：

```bash
php think dsassistant:test-function-calling
```

### 4. 测试验证

#### 测试用例
1. "介绍一下天空栈道" - 应该返回文本说明 + 景点详情函数调用
2. "天坛公园在哪里" - 应该返回文本说明 + 地图位置函数调用
3. "显示颐和园的位置" - 应该返回文本说明 + 地图函数调用
4. "我想了解故宫的详细信息" - 应该返回文本说明 + 景点详情函数调用

#### 期望结果
每个测试用例都应该返回：
- `answer`字段包含有意义的文本说明
- `functionCalls`数组包含相应的函数调用
- 用户体验友好，既有文字解释又有功能操作

### 5. 备选方案

如果增强提示词仍然无法解决问题，可以考虑以下备选方案：

#### 方案A：后处理补充文本
在`DsAssistant.php`中检查返回结果，如果只有函数调用而无文本，自动补充默认文本：

```php
// 检查是否只有函数调用而无文本
if (empty($result['content']) && !empty($result['function_calls'])) {
    $result['content'] = "我为您找到了相关信息，请查看下方的详细内容。";
}
```

#### 方案B：调整模型参数
尝试调整API调用参数，如`temperature`、`top_p`等，影响模型的输出行为。

#### 方案C：使用不同的模型
如果当前模型在Function Calling方面表现不佳，可以考虑切换到其他支持Function Calling的模型。

### 6. 监控和优化

#### 日志监控
添加详细的日志记录，监控Function Calling的使用情况：

```php
Log::info('DeepSeek API函数调用content字段: ' . ($content === '' ? '空字符串' : $content));
Log::info('DeepSeek API函数调用处理后的结果: ' . json_encode($returnResult, JSON_UNESCAPED_UNICODE));
```

#### 用户反馈收集
收集用户对Function Calling体验的反馈，持续优化提示词和交互方式。

#### A/B测试
可以设置不同的提示词版本，通过A/B测试找到最佳的提示词配置。

## 总结

通过增强系统提示词，明确指示AI模型在使用Function Calling时必须同时提供文本说明，可以有效解决只返回函数调用而无文本的问题。这种方案既保持了Function Calling的功能性，又确保了良好的用户体验。

关键要点：
1. **明确指示**：在系统提示词中明确要求同时提供文本和函数调用
2. **具体示例**：提供具体的示例指导AI的行为
3. **可配置性**：允许管理员根据需要调整提示词
4. **持续监控**：通过日志和用户反馈持续优化
