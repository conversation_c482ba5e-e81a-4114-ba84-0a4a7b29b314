# Function Calling 可扩展设计方案

## 背景

DeepSeek支持function calling功能，允许AI助手调用预定义的函数来执行特定操作，如在地图上显示位置、展示景点详情等。为了确保插件升级不会覆盖用户自定义的函数，需要设计一个可扩展的函数定义管理方案。

## 问题分析

### 当前实现的局限性

目前的实现方式是在`FunctionDefinitions.php`类中硬编码定义所有函数，存在以下问题：

1. **升级冲突**：当用户修改或添加了自己的函数定义，而插件发布新版本时，用户的修改会在升级过程中被覆盖。

2. **扩展性差**：用户无法轻松地添加自己的函数定义，必须直接修改插件代码。

3. **维护困难**：随着函数数量增加，集中在一个文件中管理所有函数定义会变得难以维护。

4. **定制化需求**：不同用户可能有不同的函数需求，硬编码方式无法满足这种多样性。

### 理想的可扩展解决方案应具备的特性

1. **向后兼容**：插件升级不应覆盖用户自定义的函数定义。

2. **模块化**：函数定义应该能够模块化管理，便于添加和移除。

3. **配置驱动**：应该能通过配置而非代码修改来启用/禁用特定函数。

4. **插件机制**：理想情况下，应该提供一种"插件中的插件"机制，允许用户创建自己的函数定义模块。

5. **版本管理**：能够处理不同版本的函数定义，确保兼容性。

## 解决方案：目录扫描机制结合配置驱动

### 1. 目录结构设计

```
addons/dsassistant/functions/
├── core/           # 核心函数定义（随插件更新）
│   ├── map.php
│   └── scenic.php
└── custom/         # 用户自定义函数（不随插件更新）
    ├── user_map.php
    └── restaurant.php
```

### 2. 函数定义文件格式

每个函数定义文件应返回一个标准格式的函数定义数组：

```php
<?php
// addons/dsassistant/functions/core/map.php

return [
    'name' => 'showOnMap',
    'description' => '显示景点在地图上的位置',
    'parameters' => [
        'type' => 'object',
        'properties' => [
            'spotId' => [
                'type' => 'string',
                'description' => '景点ID'
            ],
            'spotName' => [
                'type' => 'string',
                'description' => '景点名称'
            ],
            'latitude' => [
                'type' => 'number',
                'description' => '纬度'
            ],
            'longitude' => [
                'type' => 'number',
                'description' => '经度'
            ]
        ],
        'required' => ['spotName']
    ],
    'enabled' => true,  // 默认启用状态
    'priority' => 100   // 优先级，数字越小优先级越高
];
```

### 3. 函数加载器设计

创建一个`FunctionLoader`类负责扫描目录并加载函数定义：

```php
<?php
namespace addons\dsassistant\library;

class FunctionLoader
{
    // 函数定义目录
    protected $corePath = '';
    protected $customPath = '';
    
    // 缓存的函数定义
    protected $functions = null;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->corePath = ADDON_PATH . 'dsassistant/functions/core/';
        $this->customPath = ADDON_PATH . 'dsassistant/functions/custom/';
    }
    
    /**
     * 获取所有可用的函数定义
     * 
     * @param bool $forceReload 是否强制重新加载
     * @return array 函数定义数组
     */
    public function getAllFunctions($forceReload = false)
    {
        if ($this->functions === null || $forceReload) {
            $this->loadFunctions();
        }
        
        return $this->functions;
    }
    
    /**
     * 加载所有函数定义
     */
    protected function loadFunctions()
    {
        $this->functions = [];
        
        // 1. 加载核心函数定义
        $coreFunctions = $this->loadFromDirectory($this->corePath);
        
        // 2. 加载用户自定义函数定义
        $customFunctions = $this->loadFromDirectory($this->customPath);
        
        // 3. 合并函数定义（用户定义优先）
        $this->functions = array_merge($coreFunctions, $customFunctions);
        
        // 4. 根据配置过滤和排序
        $this->filterAndSortFunctions();
    }
    
    /**
     * 从目录加载函数定义
     * 
     * @param string $directory 目录路径
     * @return array 函数定义数组
     */
    protected function loadFromDirectory($directory)
    {
        $functions = [];
        
        if (!is_dir($directory)) {
            return $functions;
        }
        
        $files = glob($directory . '*.php');
        foreach ($files as $file) {
            $function = include $file;
            if (is_array($function) && isset($function['name'])) {
                $functions[$function['name']] = $function;
            }
        }
        
        return $functions;
    }
    
    /**
     * 根据配置过滤和排序函数
     */
    protected function filterAndSortFunctions()
    {
        // 获取数据库中的函数配置
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $enabledFunctions = isset($config['enabled_functions']) ? json_decode($config['enabled_functions'], true) : [];
        
        // 过滤禁用的函数
        foreach ($this->functions as $name => $function) {
            // 如果配置中明确设置了启用状态，使用配置值
            if (isset($enabledFunctions[$name])) {
                $this->functions[$name]['enabled'] = (bool)$enabledFunctions[$name];
            }
            
            // 过滤掉禁用的函数
            if (isset($this->functions[$name]['enabled']) && $this->functions[$name]['enabled'] === false) {
                unset($this->functions[$name]);
            }
        }
        
        // 按优先级排序
        uasort($this->functions, function($a, $b) {
            $priorityA = isset($a['priority']) ? $a['priority'] : 999;
            $priorityB = isset($b['priority']) ? $b['priority'] : 999;
            return $priorityA - $priorityB;
        });
    }
    
    /**
     * 获取用于DeepSeek API的函数定义
     * 
     * @return array 适用于DeepSeek API的函数定义数组
     */
    public function getFunctionsForDeepSeek()
    {
        $functions = $this->getAllFunctions();
        $result = [];
        
        foreach ($functions as $function) {
            // 移除非DeepSeek API需要的字段
            $apiFunction = $function;
            unset($apiFunction['enabled']);
            unset($apiFunction['priority']);
            
            $result[] = $apiFunction;
        }
        
        return $result;
    }
}
```

### 4. 配置管理

在管理界面中添加函数管理页面，允许管理员启用/禁用特定函数：

1. 创建函数管理表格，显示所有可用函数
2. 提供启用/禁用开关
3. 将配置存储在数据库中

### 5. 集成到现有系统

修改`DsAssistant`模型中的`callDeepSeek`方法，使用`FunctionLoader`加载函数定义：

```php
// 如果启用函数调用
if ($enableFunctions) {
    // 使用函数加载器获取函数定义
    $functionLoader = new \addons\dsassistant\library\FunctionLoader();
    $functions = $functionLoader->getFunctionsForDeepSeek();
    
    // 调用支持函数的API
    $result = $deepseek->chatCompletionWithFunctions($messages, $functions, $model);
    
    Log::record("使用函数调用，函数数量: " . count($functions), 'info');
    if (!empty($result['function_calls'])) {
        Log::record("DeepSeek返回了函数调用，数量: " . count($result['function_calls']), 'info');
    }
    
    return $result;
}
```

### 6. 升级处理

在插件升级脚本中添加以下逻辑：

1. 只更新`core`目录中的函数定义
2. 保留`custom`目录中的所有文件
3. 检查核心函数定义的变更，提示可能的兼容性问题

## 实施步骤

1. 创建函数目录结构
2. 实现`FunctionLoader`类
3. 将现有函数定义迁移到新的目录结构
4. 修改`DsAssistant`模型，使用`FunctionLoader`
5. 添加函数管理界面
6. 更新升级脚本，确保用户自定义函数不被覆盖

## 优势

- **清晰的分离**：核心函数和用户自定义函数明确分离
- **简单易用**：用户只需创建PHP文件即可添加新函数
- **灵活配置**：通过管理界面可以轻松启用/禁用函数
- **升级安全**：插件升级不会覆盖用户自定义函数
- **性能优化**：支持函数定义缓存，减少文件系统操作

## 注意事项

1. 确保函数定义文件的安全性，防止恶意代码注入
2. 提供详细的文档，指导用户如何正确添加自定义函数
3. 考虑添加函数版本控制，以处理API变更
4. 实现函数定义的验证机制，确保格式正确
