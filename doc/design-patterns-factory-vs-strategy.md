# 工厂模式与策略模式的选择指南

## 概述

工厂模式和策略模式是两种常用的设计模式，它们解决不同的问题，适用于不同的场景。本文档分析这两种模式的适用场景和选择依据，帮助开发者在适当的情况下选择正确的设计模式。

## 工厂模式的适用场景

工厂模式主要用于**对象创建**，当您需要：

1. **封装对象创建逻辑**：当创建对象的过程复杂，或者需要根据不同条件创建不同类型的对象时
2. **隐藏具体实现类**：客户端不需要知道具体的实现类，只需要知道接口
3. **管理对象族**：创建一系列相关或相互依赖的对象
4. **延迟实例化**：在需要时才创建对象，而不是提前创建

### 工厂模式的典型应用场景

- **数据库连接**：根据配置创建不同类型的数据库连接（MySQL、PostgreSQL等）
- **UI组件创建**：根据平台或主题创建不同风格的UI组件
- **第三方API集成**：如DeepSeekFactory，根据配置选择不同的API提供商
- **文件解析器**：根据文件类型创建不同的解析器（XML、JSON、CSV等）

### 工厂模式的代码示例

```php
// 工厂接口
interface DatabaseFactory {
    public function createConnection();
}

// 具体工厂实现
class MySQLFactory implements DatabaseFactory {
    public function createConnection() {
        return new MySQLConnection();
    }
}

class PostgreSQLFactory implements DatabaseFactory {
    public function createConnection() {
        return new PostgreSQLConnection();
    }
}

// 客户端代码
function connectToDatabase($dbType) {
    $factory = null;
    
    if ($dbType == 'mysql') {
        $factory = new MySQLFactory();
    } else if ($dbType == 'postgresql') {
        $factory = new PostgreSQLFactory();
    }
    
    $connection = $factory->createConnection();
    $connection->connect();
}
```

## 策略模式的适用场景

策略模式主要用于**算法选择**，当您需要：

1. **在运行时选择算法**：根据上下文或用户选择使用不同的算法
2. **封装算法族**：将一系列算法封装起来，使它们可以互相替换
3. **避免条件语句**：替代复杂的条件判断逻辑
4. **隔离算法实现细节**：客户端不需要知道算法的具体实现

### 策略模式的典型应用场景

- **排序算法**：根据数据特性选择不同的排序算法（快速排序、归并排序等）
- **支付方式**：根据用户选择使用不同的支付方式（信用卡、PayPal、微信支付等）
- **验证策略**：根据不同的验证需求使用不同的验证算法
- **价格计算**：根据不同的促销策略计算价格（正常价格、折扣价格、会员价格等）

### 策略模式的代码示例

```php
// 策略接口
interface PaymentStrategy {
    public function pay($amount);
}

// 具体策略实现
class CreditCardPayment implements PaymentStrategy {
    public function pay($amount) {
        echo "Paying $amount using Credit Card";
    }
}

class PayPalPayment implements PaymentStrategy {
    public function pay($amount) {
        echo "Paying $amount using PayPal";
    }
}

class WeChatPayment implements PaymentStrategy {
    public function pay($amount) {
        echo "Paying $amount using WeChat Pay";
    }
}

// 上下文类
class PaymentContext {
    private $strategy;
    
    public function setStrategy(PaymentStrategy $strategy) {
        $this->strategy = $strategy;
    }
    
    public function executePayment($amount) {
        $this->strategy->pay($amount);
    }
}

// 客户端代码
$context = new PaymentContext();

// 用户选择支付方式
$paymentMethod = $_POST['payment_method'];

if ($paymentMethod == 'credit_card') {
    $context->setStrategy(new CreditCardPayment());
} else if ($paymentMethod == 'paypal') {
    $context->setStrategy(new PayPalPayment());
} else if ($paymentMethod == 'wechat') {
    $context->setStrategy(new WeChatPayment());
}

$context->executePayment(100);
```

## 两种模式的区别

| 特性 | 工厂模式 | 策略模式 |
|------|---------|---------|
| 主要目的 | 创建对象 | 选择算法 |
| 关注点 | 如何创建对象 | 如何执行操作 |
| 客户端知识 | 不需要知道具体实现类 | 不需要知道具体算法实现 |
| 扩展方式 | 添加新的产品类和工厂方法 | 添加新的策略类 |
| 典型接口 | `createProduct()` | `execute()`, `calculate()` |
| 设计意图 | 封装对象创建过程 | 封装可互换的算法 |
| 依赖关系 | 客户端依赖于抽象工厂 | 上下文依赖于抽象策略 |

## 在景区助手系统中的应用

在景区助手系统中，这两种模式的应用示例：

1. **工厂模式**：
   - `DeepSeekFactory` - 创建不同云平台的DeepSeek实现
   - `VectorSearchFactory` - 创建不同的向量搜索实现
   - `EmbeddingFactory` - 创建不同的向量嵌入实现

2. **策略模式**：
   - `StrategyFactory` 和 `AnswerStrategyInterface` - 选择不同的回答生成策略
   - 如 `RagStrategy`、`TraditionalStrategy` 等

### 工厂模式示例（DeepSeekFactory）

```php
// 工厂类
class DeepSeekFactory
{
    public static function getInstance()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $provider = $config['deepseek_provider'] ?? 'official';
        
        return self::create($provider);
    }
    
    public static function create($provider)
    {
        switch ($provider) {
            case 'official':
                return new OfficialDeepSeek();
            case 'tencent':
                return new TencentDeepSeek();
            case 'baidu':
                return new BaiduDeepSeek();
            // 更多提供商...
        }
    }
}

// 客户端代码
$deepseek = DeepSeekFactory::getInstance();
$response = $deepseek->chatCompletion($messages);
```

### 策略模式示例（AnswerStrategy）

```php
// 策略工厂
class StrategyFactory
{
    public static function getStrategy($type = null, $config = [])
    {
        if ($type === null) {
            // 根据配置决定使用哪种策略
            $ragEnabled = isset($config['rag_config']['enabled']) ? 
                $config['rag_config']['enabled'] : false;
                
            $type = $ragEnabled ? 'rag' : 'traditional';
        }
        
        switch ($type) {
            case 'rag':
                return new RagStrategy();
            case 'traditional':
                return new TraditionalStrategy();
            // 更多策略...
        }
    }
}

// 客户端代码
$strategy = StrategyFactory::getStrategy(null, $config);
$result = $strategy->generateAnswer($question, $sessionId, $config);
```

## 何时选择工厂模式

当您面临以下情况时，应该选择工厂模式：

1. 需要创建的对象类型在运行时才能确定
2. 对象的创建过程复杂或需要特定的配置
3. 希望将对象的创建与使用分离
4. 系统需要处理多种类型的相关对象
5. 对象的创建涉及资源分配和管理

## 何时选择策略模式

当您面临以下情况时，应该选择策略模式：

1. 有多种算法或行为可以互相替换
2. 需要在运行时根据条件选择不同的行为
3. 有复杂的条件判断逻辑需要简化
4. 希望避免使用大量的if-else语句
5. 算法的变化独立于使用它的客户端

## 两种模式的结合使用

在实际应用中，这两种模式经常结合使用：

1. 使用工厂模式创建策略对象
2. 使用策略模式执行不同的算法

例如：
```php
// 使用工厂模式获取策略对象
$strategy = StrategyFactory::getStrategy(null, $config);

// 使用策略模式生成回答
$result = $strategy->generateAnswer($question, $sessionId, $config);
```

## 总结

- **工厂模式**：关注"创建什么"，解决对象创建的问题
- **策略模式**：关注"如何做"，解决算法选择的问题

选择哪种模式取决于您要解决的问题类型：
- 如果是对象创建问题，选择工厂模式
- 如果是算法选择问题，选择策略模式

在复杂系统中，这两种模式往往会一起使用，相互配合，提高系统的灵活性和可维护性。
