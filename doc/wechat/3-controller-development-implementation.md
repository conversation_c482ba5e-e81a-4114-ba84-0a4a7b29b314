# 微信公众号Function Calling控制器开发实现文档

## 1. 概述

本文档记录了微信公众号Function Calling功能的控制器开发实现过程。该功能允许DeepSeek AI模型在回答用户问题时调用预定义的函数，实现更丰富的交互体验。

## 2. 实现架构

整个功能的实现架构如下：

```
用户 -> 微信公众号 -> Wechat控制器 -> DeepSeek API -> 函数调用 -> H5页面展示
```

主要组件包括：

1. **Wechat控制器**：处理微信消息，调用DeepSeek API，解析函数调用结果
2. **WechatSession模型**：管理用户会话数据，保存函数调用信息
3. **H5控制器**：处理H5页面请求，展示函数调用结果
4. **H5视图文件**：展示函数调用结果的页面模板

## 3. 控制器实现

### 3.1 Wechat控制器修改

修改了`addons/dsassistant/controller/Wechat.php`文件，主要添加了以下方法：

1. **handleTextMessage**：处理用户文本消息，支持函数调用
   - 检查是否是菜单选择
   - 调用DeepSeek API获取回答和函数调用
   - 根据函数调用数量生成不同的回复

2. **handleMenuSelection**：处理用户菜单选择
   - 从会话中获取保存的函数调用
   - 执行选择的函数

3. **generateNewsForFunction**：生成图文消息
   - 根据函数调用生成图文消息
   - 包含标题、描述、图片和链接

4. **getFunctionTitle**：获取函数标题
   - 根据函数名和参数生成友好的标题

5. **getFunctionImage**：获取函数图片
   - 根据函数名获取对应的图片URL

6. **generateUrl**：生成H5页面URL
   - 根据函数名和参数生成H5页面URL

7. **saveActionsToSession**：保存函数调用到会话
   - 使用WechatSession模型保存函数调用信息

8. **getActionsFromSession**：从会话中获取函数调用
   - 使用WechatSession模型获取函数调用信息

9. **executeFunction**：执行函数
   - 根据函数名执行不同的操作
   - 生成对应的图文消息

### 3.2 H5控制器实现

创建了`addons/dsassistant/controller/wechat/H5.php`控制器，主要包含以下方法：

1. **index**：首页
   - 展示功能列表

2. **map**：地图页面
   - 展示景点位置
   - 支持导航

3. **scenic**：景点详情页面
   - 展示景点信息
   - 支持查看地图和预订门票

4. **search**：搜索页面
   - 搜索景区周边设施
   - 支持快速搜索

5. **ticket**：订票页面
   - 展示门票信息
   - 支持在线预订

6. **weather**：天气页面
   - 展示天气信息
   - 提供游玩建议

## 4. 视图文件实现

创建了以下H5视图文件：

1. **index.html**：首页
   - 展示功能列表
   - 支持微信分享

2. **map.html**：地图页面
   - 使用百度地图API
   - 支持微信导航

3. **scenic.html**：景点详情页面
   - 展示景点信息
   - 支持查看地图和预订门票

4. **search.html**：搜索页面
   - 搜索景区周边设施
   - 支持快速搜索

5. **ticket.html**：订票页面
   - 展示门票信息
   - 支持在线预订

6. **weather.html**：天气页面
   - 展示天气信息
   - 提供游玩建议

## 5. 函数调用流程

1. 用户发送消息到微信公众号
2. Wechat控制器接收消息并调用DeepSeek API
3. DeepSeek API返回回答和函数调用信息
4. Wechat控制器解析函数调用信息
5. 如果只有一个函数调用，直接生成图文消息
6. 如果有多个函数调用，保存到会话并提供选择菜单
7. 用户选择菜单项后，执行对应的函数
8. 生成图文消息，链接到H5页面
9. H5控制器处理请求并渲染页面

## 6. 会话管理

使用WechatSession模型管理用户会话数据，主要包括：

1. **saveSessionData**：保存会话数据
   - 保存函数调用信息到数据库
   - 设置过期时间

2. **getSessionData**：获取会话数据
   - 从数据库获取函数调用信息
   - 检查是否过期

## 7. 注意事项

1. 确保微信公众号配置正确，包括服务器URL、Token等
2. 确保DeepSeek API配置正确，包括API密钥等
3. 确保函数定义正确，包括函数名、参数等
4. 确保H5页面可以正常访问，包括域名配置等
5. 确保微信JS SDK配置正确，包括安全域名等
6. 确保图片资源可以正常访问，包括CDN配置等

## 8. 后续优化

1. 添加更多函数定义，支持更多场景
2. 优化H5页面UI，提升用户体验
3. 添加更多交互功能，如语音输入、图片识别等
4. 优化会话管理，支持多轮对话
5. 添加用户行为分析，优化AI回答质量
6. 添加缓存机制，提升响应速度
7. 添加错误处理，提高系统稳定性

## 9. 总结

通过修改Wechat控制器和创建H5控制器，实现了微信公众号Function Calling功能。该功能允许DeepSeek AI模型在回答用户问题时调用预定义的函数，实现更丰富的交互体验。用户可以通过微信公众号查询景点信息、查看地图、预订门票、查询天气等，大大提升了用户体验。
