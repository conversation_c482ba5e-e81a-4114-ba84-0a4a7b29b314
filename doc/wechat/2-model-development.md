# 2. 模型开发

**预计工时**：0.5人天

## 任务概述

创建WechatSession模型类，实现会话数据的CRUD操作，为微信公众号Function Calling功能提供数据持久化支持。

## 子任务

### 2.1 创建WechatSession模型

- **任务描述**：创建WechatSession模型类，实现会话数据的CRUD操作
- **技术要点**：
  - 实现saveData方法，保存会话数据
  - 实现getData方法，获取会话数据
  - 实现cleanExpired方法，清理过期数据
- **验收标准**：
  - 模型方法能正确保存和获取数据
  - 过期数据能被正确清理

**模型代码示例**：

```php
<?php
namespace addons\dsassistant\model;

use think\Model;

class WechatSession extends Model
{
    // 表名
    protected $name = 'dsassistant_wechat_session';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    /**
     * 保存用户会话数据
     *
     * @param string $openid 用户OpenID
     * @param string $key 会话键名
     * @param mixed $data 会话数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function saveData($openid, $key, $data, $expire = 1800)
    {
        $session = self::where('openid', $openid)->where('key', $key)->find();

        if (!$session) {
            $session = new self();
            $session->openid = $openid;
            $session->key = $key;
        }

        $session->session_data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $session->expire_time = time() + $expire;
        $session->save();

        return true;
    }

    /**
     * 获取用户会话数据
     *
     * @param string $openid 用户OpenID
     * @param string $key 会话键名
     * @return mixed|null
     */
    public static function getData($openid, $key)
    {
        $session = self::where('openid', $openid)
            ->where('key', $key)
            ->where('expire_time', '>', time())
            ->find();

        if (!$session) {
            return null;
        }

        return json_decode($session->session_data, true);
    }

    /**
     * 清理过期会话数据
     *
     * @return int 清理的记录数
     */
    public static function cleanExpired()
    {
        return self::where('expire_time', '<', time())->delete();
    }

    /**
     * 删除用户会话数据
     *
     * @param string $openid 用户OpenID
     * @param string $key 会话键名
     * @return bool
     */
    public static function removeData($openid, $key)
    {
        return self::where('openid', $openid)->where('key', $key)->delete();
    }

    /**
     * 清理指定用户的所有会话数据
     *
     * @param string $openid 用户OpenID
     * @return int 清理的记录数
     */
    public static function cleanUserData($openid)
    {
        return self::where('openid', $openid)->delete();
    }
}
```

## 注意事项

1. 确保模型与数据库表名对应
2. 添加适当的数据验证和错误处理
3. 考虑会话数据的安全性，避免敏感信息泄露
4. 定期清理过期数据，避免数据库膨胀

## 依赖关系

- 上游依赖：数据库开发
- 下游依赖：控制器开发

## 负责人

待定

## 计划完成时间

待定
