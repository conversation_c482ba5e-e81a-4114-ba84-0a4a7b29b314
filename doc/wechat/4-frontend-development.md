# 4. 前端开发

**预计工时**：3人天

## 任务概述

设计并实现微信公众号Function Calling功能的H5页面，包括地图、景点详情、搜索、订票和天气等页面，提供良好的用户体验。

## 子任务

### 4.1 创建H5页面基础模板

- **任务描述**：创建H5页面的基础模板，包含公共样式和脚本
- **技术要点**：
  - 设计移动端友好的页面布局
  - 实现响应式设计
  - 添加微信JSSDK支持
- **验收标准**：
  - 页面在各种移动设备上显示正常
  - 基础功能（如返回、分享）正常工作

**基础模板示例**：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{$title|default='景区智能助理'}</title>
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/weui.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/wechat-h5.css">
    <script src="__CDN__/assets/addons/dsassistant/js/zepto.min.js"></script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="javascript:history.back();" class="back-btn">
                <i class="weui-icon-back"></i>
            </a>
            <h1 class="title">{$title|default='景区智能助理'}</h1>
        </div>
        
        <div class="content">
            {__CONTENT__}
        </div>
        
        <div class="footer">
            <p>© {$site.name} {date('Y')}</p>
        </div>
    </div>
    
    <script src="__CDN__/assets/addons/dsassistant/js/wechat-h5.js"></script>
    <script>
        // 配置微信JSSDK
        wx.config({
            debug: false,
            appId: '{$wxConfig.appId}',
            timestamp: {$wxConfig.timestamp},
            nonceStr: '{$wxConfig.nonceStr}',
            signature: '{$wxConfig.signature}',
            jsApiList: [
                'updateAppMessageShareData',
                'updateTimelineShareData',
                'openLocation',
                'getLocation'
            ]
        });
        
        wx.ready(function() {
            // 配置分享信息
            wx.updateAppMessageShareData({
                title: '{$shareData.title|default=$title}',
                desc: '{$shareData.desc|default="景区智能助理为您提供贴心服务"}',
                link: '{$shareData.link|default=request()->url(true)}',
                imgUrl: '{$shareData.imgUrl|default=$site.cdnurl . "/assets/addons/dsassistant/img/share-logo.jpg"}'
            });
            
            wx.updateTimelineShareData({
                title: '{$shareData.title|default=$title}',
                link: '{$shareData.link|default=request()->url(true)}',
                imgUrl: '{$shareData.imgUrl|default=$site.cdnurl . "/assets/addons/dsassistant/img/share-logo.jpg"}'
            });
        });
    </script>
</body>
</html>
```

### 4.2 实现地图页面

- **任务描述**：实现显示景点位置的地图页面
- **技术要点**：
  - 集成地图API（如腾讯地图、百度地图）
  - 显示景点位置和信息窗口
  - 实现导航功能
- **验收标准**：
  - 能正确显示景点位置
  - 导航功能正常工作

**地图页面示例**：

```html
<div class="map-container">
    <div id="map" style="width: 100%; height: 100%;"></div>
    
    <div class="map-info">
        <h2>{$spotName}</h2>
        <p class="address">{$address}</p>
        <div class="actions">
            <a href="javascript:;" class="btn-navigate" onclick="navigate()">导航到这里</a>
            <a href="{:addon_url('dsassistant/wechat/h5/scenic', ['spotId' => $spotId, 'spotName' => $spotName])}" class="btn-detail">查看详情</a>
        </div>
    </div>
</div>

<script>
    // 初始化地图
    var map = new qq.maps.Map(document.getElementById("map"), {
        center: new qq.maps.LatLng({$latitude}, {$longitude}),
        zoom: 15
    });
    
    // 添加标记
    var marker = new qq.maps.Marker({
        position: new qq.maps.LatLng({$latitude}, {$longitude}),
        map: map
    });
    
    // 添加信息窗口
    var infoWindow = new qq.maps.InfoWindow({
        map: map
    });
    infoWindow.open();
    infoWindow.setContent('<div style="padding:5px;">{$spotName}</div>');
    infoWindow.setPosition(new qq.maps.LatLng({$latitude}, {$longitude}));
    
    // 导航功能
    function navigate() {
        wx.openLocation({
            latitude: {$latitude}, // 纬度，浮点数，范围为90 ~ -90
            longitude: {$longitude}, // 经度，浮点数，范围为180 ~ -180
            name: '{$spotName}', // 位置名
            address: '{$address}', // 地址详情说明
            scale: 15, // 地图缩放级别,整形值,范围从1~28。默认为最大
            infoUrl: '{:request()->url(true)}' // 在查看位置界面底部显示的超链接,可点击跳转
        });
    }
</script>
```

### 4.3 实现景点详情页面

- **任务描述**：实现显示景点详细信息的页面
- **技术要点**：
  - 设计景点信息展示布局
  - 实现图片轮播
  - 添加景点介绍、开放时间等信息
- **验收标准**：
  - 景点信息显示完整
  - 页面交互流畅

### 4.4 实现搜索页面

- **任务描述**：实现搜索附近设施的页面
- **技术要点**：
  - 集成搜索API
  - 实现搜索结果列表
  - 添加筛选和排序功能
- **验收标准**：
  - 搜索功能正常工作
  - 结果显示准确

### 4.5 实现订票页面

- **任务描述**：实现景点门票预订页面
- **技术要点**：
  - 设计订票表单
  - 实现日期选择和票种选择
  - 集成支付功能
- **验收标准**：
  - 订票流程完整
  - 表单验证正确

### 4.6 实现天气页面

- **任务描述**：实现显示天气信息的页面
- **技术要点**：
  - 集成天气API
  - 显示当前天气和预报
  - 添加温度、湿度等详细信息
- **验收标准**：
  - 天气信息显示准确
  - 页面更新及时

### 4.7 准备函数图片资源

- **任务描述**：准备各类函数对应的图片资源
- **技术要点**：
  - 设计或选择适合的图标和图片
  - 优化图片大小和格式
- **验收标准**：
  - 图片风格统一
  - 图片大小适中，加载速度快

**需要准备的图片资源**：

1. 地图图标 (map.jpg)
2. 景点详情图标 (scenic.jpg)
3. 搜索图标 (search.jpg)
4. 订票图标 (ticket.jpg)
5. 天气图标 (weather.jpg)
6. 默认图标 (default.jpg)

## 注意事项

1. 确保页面在各种移动设备上显示正常
2. 优化页面加载速度，减少资源大小
3. 添加适当的错误处理和加载提示
4. 考虑网络不稳定情况下的用户体验
5. 确保微信JSSDK配置正确

## 依赖关系

- 上游依赖：控制器开发
- 下游依赖：测试与优化

## 负责人

待定

## 计划完成时间

待定
