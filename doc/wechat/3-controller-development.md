# 3. 控制器开发

**预计工时**：2人天

## 任务概述

修改现有的Wechat控制器，添加处理函数调用的方法，并创建H5页面的控制器方法，实现微信公众号Function Calling功能的核心逻辑。

## 子任务

### 3.1 修改Wechat控制器

- **任务描述**：修改现有的Wechat控制器，添加处理函数调用的方法
- **技术要点**：
  - 实现handleQuestion方法，处理用户问题并调用DeepSeek API
  - 实现handleMenuSelection方法，处理用户菜单选择
- **验收标准**：
  - 控制器能正确接收和处理微信消息
  - 能正确调用DeepSeek API并解析响应

**控制器代码示例**：

```php
<?php
namespace addons\dsassistant\controller;

use addons\dsassistant\library\deepseek\DeepSeekFactory;
use addons\dsassistant\model\WechatSession;

class Wechat extends Base
{
    protected $wechat;
    protected $deepseek;
    
    public function _initialize()
    {
        parent::_initialize();
        $this->wechat = new \WeChat\Receive(config('wechat.'));
        $this->deepseek = DeepSeekFactory::getInstance();
    }
    
    /**
     * 处理微信消息
     */
    public function index()
    {
        $this->wechat->valid();
        
        // 处理用户消息
        $receiveMessage = $this->wechat->getReceive();
        if ($receiveMessage instanceof \WeChat\Contracts\Message) {
            // 获取用户OpenID
            $openid = $receiveMessage->FromUserName;
            
            // 处理文本消息
            if ($receiveMessage->MsgType == 'text') {
                $content = $receiveMessage->Content;
                
                // 检查是否是菜单选择
                if (preg_match('/^[1-9]\d*$/', $content)) {
                    return $this->handleMenuSelection($openid, $content);
                }
                
                // 正常对话流程
                return $this->handleQuestion($openid, $content);
            }
            
            // 处理其他类型消息...
        }
        
        return $this->wechat->text('欢迎使用景区智能助理！')->reply();
    }
    
    /**
     * 处理用户问题
     */
    protected function handleQuestion($openid, $question)
    {
        // 调用DeepSeek API获取回答和函数调用
        $response = $this->callDeepSeekApi($question, $openid);
        
        // 提取文本回答和函数调用
        $answer = $response['answer'];
        $functionCalls = $response['functionCalls'] ?? [];
        
        // 如果没有函数调用，直接返回文本回答
        if (empty($functionCalls)) {
            return $this->wechat->text($answer)->reply();
        }
        
        // 如果只有一个函数调用，直接返回图文消息
        if (count($functionCalls) == 1) {
            return $this->generateNewsForFunction($answer, $functionCalls[0]);
        }
        
        // 如果有多个函数调用，保存到会话中并提供选择菜单
        $this->saveActionsToSession($openid, $functionCalls);
        
        // 构建回复消息，包含文本回答和选择菜单
        $reply = $answer . "\n\n您可以选择以下操作：\n";
        foreach ($functionCalls as $index => $call) {
            $reply .= ($index + 1) . ". " . $this->getFunctionTitle($call) . "\n";
        }
        
        return $this->wechat->text($reply)->reply();
    }
    
    /**
     * 处理菜单选择
     */
    protected function handleMenuSelection($openid, $selection)
    {
        // 从会话中获取保存的函数调用
        $functionCalls = $this->getActionsFromSession($openid);
        
        // 检查选择是否有效
        $index = (int)$selection - 1;
        if (!isset($functionCalls[$index])) {
            return $this->wechat->text('无效的选择，请重新输入您的问题。')->reply();
        }
        
        // 执行选择的函数
        $functionCall = $functionCalls[$index];
        return $this->executeFunction($openid, $functionCall);
    }
    
    /**
     * 调用DeepSeek API
     */
    protected function callDeepSeekApi($question, $openid)
    {
        // 调用DeepSeek API
        $result = $this->deepseek->chat([
            'question' => $question,
            'user_id' => $openid,
            'platform' => 'wechat'
        ]);
        
        return $result;
    }
}
```

### 3.2 实现辅助方法

- **任务描述**：在Wechat控制器中实现处理函数调用的辅助方法
- **技术要点**：
  - 实现generateNewsForFunction方法，生成图文消息
  - 实现getFunctionTitle方法，获取函数标题
  - 实现getFunctionImage方法，获取函数图片
  - 实现generateUrl方法，生成H5页面URL
  - 实现saveActionsToSession和getActionsFromSession方法，管理会话数据
  - 实现executeFunction方法，执行函数操作
- **验收标准**：
  - 辅助方法能正确生成图文消息
  - 会话数据能正确保存和获取
  - 函数能正确执行

**辅助方法代码示例**：

```php
/**
 * 生成图文消息
 */
protected function generateNewsForFunction($answer, $functionCall)
{
    $article = [
        'title' => $this->getFunctionTitle($functionCall),
        'description' => $answer,
        'picurl' => $this->getFunctionImage($functionCall['name']),
        'url' => $this->generateUrl($functionCall['name'], $functionCall['parameters'])
    ];
    
    return $this->wechat->news([$article])->reply();
}

/**
 * 获取函数标题
 */
protected function getFunctionTitle($functionCall)
{
    switch ($functionCall['name']) {
        case 'showOnMap':
            return $functionCall['parameters']['spotName'] . '的位置';
        case 'showScenicSpotDetails':
            return $functionCall['parameters']['spotName'] . '详情';
        case 'searchNearby':
            return '搜索附近' . ($functionCall['parameters']['keyword'] ?? '地点');
        case 'bookTicket':
            return '预订' . ($functionCall['parameters']['spotName'] ?? '景点') . '门票';
        case 'getWeather':
            return ($functionCall['parameters']['location'] ?? '当地') . '天气';
        default:
            return $functionCall['name'];
    }
}

/**
 * 获取函数图片
 */
protected function getFunctionImage($functionName)
{
    $baseUrl = config('site.cdnurl') . '/assets/addons/dsassistant/images/functions/';
    
    $images = [
        'showOnMap' => 'map.jpg',
        'showScenicSpotDetails' => 'scenic.jpg',
        'searchNearby' => 'search.jpg',
        'bookTicket' => 'ticket.jpg',
        'getWeather' => 'weather.jpg'
    ];
    
    return $baseUrl . ($images[$functionName] ?? 'default.jpg');
}

/**
 * 生成H5页面URL
 */
protected function generateUrl($functionName, $parameters)
{
    $baseUrl = request()->domain() . '/addons/dsassistant/wechat/h5/';
    
    switch ($functionName) {
        case 'showOnMap':
            return $baseUrl . 'map?' . http_build_query([
                'spotId' => $parameters['spotId'] ?? '',
                'spotName' => $parameters['spotName'] ?? '',
                'latitude' => $parameters['latitude'] ?? '',
                'longitude' => $parameters['longitude'] ?? ''
            ]);
        case 'showScenicSpotDetails':
            return $baseUrl . 'scenic?' . http_build_query([
                'spotId' => $parameters['spotId'] ?? '',
                'spotName' => $parameters['spotName'] ?? ''
            ]);
        // 其他函数的URL生成...
        default:
            return $baseUrl . 'index';
    }
}

/**
 * 保存函数调用到会话
 */
protected function saveActionsToSession($openid, $functionCalls)
{
    return WechatSession::saveData($openid, 'function_calls', $functionCalls, 1800); // 30分钟有效
}

/**
 * 从会话中获取函数调用
 */
protected function getActionsFromSession($openid)
{
    return WechatSession::getData($openid, 'function_calls') ?: [];
}

/**
 * 执行函数
 */
protected function executeFunction($openid, $functionCall)
{
    // 根据函数名执行不同操作
    switch ($functionCall['name']) {
        case 'showOnMap':
        case 'showScenicSpotDetails':
        case 'searchNearby':
        case 'bookTicket':
        case 'getWeather':
            // 这些函数都通过图文消息实现
            return $this->generateNewsForFunction('请点击查看详细信息', $functionCall);
        
        // 可以添加其他直接在公众号中执行的函数
        
        default:
            return $this->wechat->text('暂不支持该操作')->reply();
    }
}
```

### 3.3 创建H5页面控制器

- **任务描述**：创建处理H5页面请求的控制器方法
- **技术要点**：
  - 创建map、scenic、search、ticket、weather等方法
  - 处理URL参数并传递给视图
- **验收标准**：
  - 控制器方法能正确接收和处理请求
  - 能正确渲染对应的视图

**H5控制器代码示例**：

```php
<?php
namespace addons\dsassistant\controller\wechat;

use app\common\controller\Frontend;

class H5 extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    
    public function _initialize()
    {
        parent::_initialize();
        // 加载公共资源
        $this->loadCommonResources();
    }
    
    /**
     * 加载公共资源
     */
    protected function loadCommonResources()
    {
        // 加载CSS和JS
        $this->view->assign('site', config('site'));
    }
    
    /**
     * 地图页面
     */
    public function map()
    {
        $params = $this->request->get();
        
        $this->view->assign('spotId', $params['spotId'] ?? '');
        $this->view->assign('spotName', $params['spotName'] ?? '');
        $this->view->assign('latitude', $params['latitude'] ?? '');
        $this->view->assign('longitude', $params['longitude'] ?? '');
        
        return $this->view->fetch();
    }
    
    /**
     * 景点详情页面
     */
    public function scenic()
    {
        $params = $this->request->get();
        
        $this->view->assign('spotId', $params['spotId'] ?? '');
        $this->view->assign('spotName', $params['spotName'] ?? '');
        
        // 获取景点详情数据
        // ...
        
        return $this->view->fetch();
    }
    
    /**
     * 搜索页面
     */
    public function search()
    {
        $params = $this->request->get();
        
        $this->view->assign('keyword', $params['keyword'] ?? '');
        $this->view->assign('latitude', $params['latitude'] ?? '');
        $this->view->assign('longitude', $params['longitude'] ?? '');
        
        return $this->view->fetch();
    }
    
    /**
     * 订票页面
     */
    public function ticket()
    {
        $params = $this->request->get();
        
        $this->view->assign('spotId', $params['spotId'] ?? '');
        $this->view->assign('spotName', $params['spotName'] ?? '');
        
        // 获取门票信息
        // ...
        
        return $this->view->fetch();
    }
    
    /**
     * 天气页面
     */
    public function weather()
    {
        $params = $this->request->get();
        
        $this->view->assign('location', $params['location'] ?? '');
        
        // 获取天气数据
        // ...
        
        return $this->view->fetch();
    }
}
```

## 注意事项

1. 确保微信API调用正确，处理可能的错误
2. 添加适当的日志记录，便于问题排查
3. 考虑会话数据的安全性，避免敏感信息泄露
4. 优化响应速度，提高用户体验

## 依赖关系

- 上游依赖：模型开发
- 下游依赖：前端开发

## 负责人

待定

## 计划完成时间

待定
