# 5. 测试与优化

**预计工时**：1人天

## 任务概述

对微信公众号Function Calling功能进行全面测试，包括单元测试、集成测试、性能优化和安全性测试，确保系统稳定可靠。

## 子任务

### 5.1 单元测试

- **任务描述**：编写单元测试，测试关键功能
- **技术要点**：
  - 测试会话管理功能
  - 测试函数调用处理逻辑
- **验收标准**：
  - 单元测试覆盖率达到80%以上
  - 所有测试用例通过

**单元测试示例**：

```php
<?php
namespace tests\addons\dsassistant\model;

use addons\dsassistant\model\WechatSession;
use PHPUnit\Framework\TestCase;

class WechatSessionTest extends TestCase
{
    protected $openid = 'test_openid';
    protected $key = 'test_key';
    protected $data = ['foo' => 'bar'];

    public function setUp()
    {
        // 清理测试数据
        WechatSession::where('openid', $this->openid)->delete();
    }

    public function testSaveData()
    {
        // 测试保存数据
        $result = WechatSession::saveData($this->openid, $this->key, $this->data);
        $this->assertTrue($result);

        // 验证数据已保存
        $session = WechatSession::where('openid', $this->openid)
            ->where('key', $this->key)
            ->find();
        $this->assertNotNull($session);
        $this->assertEquals(json_encode($this->data, JSON_UNESCAPED_UNICODE), $session->session_data);
    }

    public function testGetData()
    {
        // 先保存数据
        WechatSession::saveData($this->openid, $this->key, $this->data);

        // 测试获取数据
        $result = WechatSession::getData($this->openid, $this->key);
        $this->assertEquals($this->data, $result);
    }

    public function testGetExpiredData()
    {
        // 创建一个过期的会话
        $session = new WechatSession();
        $session->openid = $this->openid;
        $session->key = 'expired_key';
        $session->session_data = json_encode($this->data, JSON_UNESCAPED_UNICODE);
        $session->expire_time = time() - 100; // 已过期
        $session->save();

        // 测试获取过期数据
        $result = WechatSession::getData($this->openid, 'expired_key');
        $this->assertNull($result);
    }

    public function testCleanExpired()
    {
        // 创建一个过期的会话
        $session = new WechatSession();
        $session->openid = $this->openid;
        $session->key = 'expired_key';
        $session->session_data = json_encode($this->data, JSON_UNESCAPED_UNICODE);
        $session->expire_time = time() - 100; // 已过期
        $session->save();

        // 创建一个未过期的会话
        WechatSession::saveData($this->openid, $this->key, $this->data);

        // 测试清理过期数据
        $count = WechatSession::cleanExpired();
        $this->assertEquals(1, $count);

        // 验证过期数据已删除
        $expired = WechatSession::where('openid', $this->openid)
            ->where('key', 'expired_key')
            ->find();
        $this->assertNull($expired);

        // 验证未过期数据仍存在
        $valid = WechatSession::where('openid', $this->openid)
            ->where('key', $this->key)
            ->find();
        $this->assertNotNull($valid);
    }

    public function tearDown()
    {
        // 清理测试数据
        WechatSession::where('openid', $this->openid)->delete();
    }
}
```

### 5.2 集成测试

- **任务描述**：进行集成测试，测试整体功能
- **技术要点**：
  - 测试微信消息处理流程
  - 测试DeepSeek API调用
  - 测试H5页面跳转
- **验收标准**：
  - 所有功能正常工作
  - 不同场景下的交互流畅

**测试场景**：

1. **单个函数调用场景**：
   - 用户发送"西湖在哪里"
   - 系统返回文本回答和地图卡片
   - 用户点击卡片，跳转到地图页面

2. **多个函数调用场景**：
   - 用户发送"我想去西湖，帮我查一下天气和门票"
   - 系统返回文本回答和选择菜单
   - 用户回复数字选择功能
   - 系统返回对应的卡片

3. **会话过期场景**：
   - 用户发送问题，系统保存函数调用到会话
   - 等待会话过期（可以手动修改数据库中的过期时间）
   - 用户回复数字选择功能
   - 系统返回错误提示

### 5.3 性能优化

- **任务描述**：优化系统性能，提高响应速度
- **技术要点**：
  - 优化数据库查询
  - 添加缓存机制
  - 优化页面加载速度
- **验收标准**：
  - 响应时间在可接受范围内
  - 资源占用合理

**优化措施**：

1. **数据库优化**：
   - 添加适当的索引
   - 优化SQL查询
   - 定期清理过期数据

2. **缓存优化**：
   - 缓存频繁使用的数据
   - 使用Redis等缓存系统
   - 设置合理的缓存过期时间

3. **前端优化**：
   - 压缩CSS和JavaScript文件
   - 优化图片大小和格式
   - 使用CDN加速资源加载
   - 实现懒加载

### 5.4 安全性测试

- **任务描述**：进行安全性测试，确保系统安全
- **技术要点**：
  - 测试输入验证
  - 测试SQL注入防护
  - 测试XSS防护
- **验收标准**：
  - 不存在明显安全漏洞
  - 用户数据得到保护

**安全测试项目**：

1. **输入验证测试**：
   - 测试特殊字符输入
   - 测试超长输入
   - 测试空值输入

2. **SQL注入测试**：
   - 测试在输入中添加SQL语句
   - 测试在URL参数中添加SQL语句

3. **XSS测试**：
   - 测试在输入中添加JavaScript代码
   - 测试在URL参数中添加HTML标签

4. **CSRF测试**：
   - 测试跨站请求伪造攻击

5. **权限测试**：
   - 测试未授权访问
   - 测试越权访问

## 注意事项

1. 测试应覆盖所有功能点和边界情况
2. 优化应在不影响功能的前提下进行
3. 安全测试应在测试环境进行，避免影响生产数据
4. 记录所有测试结果和优化效果，便于后续改进

## 依赖关系

- 上游依赖：前端开发
- 下游依赖：部署与上线

## 负责人

待定

## 计划完成时间

待定
