# DeepSeek多云平台支持实现文档

## 概述

本文档描述了景区助手系统对多个云平台提供的DeepSeek大模型的支持实现。通过工厂模式和策略模式的结合，系统可以灵活切换不同云平台的DeepSeek API，提高系统的可用性和灵活性。

## 实现架构

### 工厂模式实现

1. **DeepSeekInterface接口**：定义所有DeepSeek实现必须提供的方法
   - `chatCompletion(array $messages, string $model)`：调用DeepSeek API生成回答
   - `getSupportedModels()`：获取支持的模型列表

2. **DeepSeekFactory工厂类**：根据配置创建不同云平台的DeepSeek实现
   - `getInstance()`：获取DeepSeek实例
   - `create($provider)`：创建指定提供商的DeepSeek实例

3. **具体实现类**：
   - `OfficialDeepSeek`：官方DeepSeek API实现
   - `TencentDeepSeek`：腾讯云DeepSeek实现
   - `BaiduDeepSeek`：百度智能云DeepSeek实现
   - `AliDeepSeek`：阿里云DeepSeek实现
   - `HuaweiDeepSeek`：华为云DeepSeek实现

### 策略模式集成

1. **AnswerStrategyInterface接口**：定义回答策略的通用接口
   - `generateAnswer($question, $sessionId, $config)`：生成回答

2. **具体策略类**：
   - `RagStrategy`：RAG模式策略，使用DeepSeekFactory调用DeepSeek API
   - `TraditionalStrategy`：传统模式策略，使用DeepSeekFactory调用DeepSeek API

3. **StrategyFactory工厂类**：根据配置创建不同的回答策略
   - `getStrategy($strategyType, $config)`：获取回答策略实例

## 配置项

系统使用以下配置项来控制DeepSeek API的行为：

1. **DeepSeek提供商配置**：
   - `deepseek_provider`：DeepSeek提供商，可选值包括'official'、'tencent'、'baidu'、'ali'、'huawei'
   - `deepseek_api_key`：DeepSeek API密钥（官方DeepSeek）
   - `deepseek_model`：DeepSeek模型，如'deepseek-chat'、'deepseek-chat-pro'

2. **RAG模式配置**：
   - `rag_enabled`：是否启用RAG模式
   - `rag_top_k`：检索结果数量
   - `rag_min_score`：最低得分要求
   - `rag_context_format`：上下文格式
   - `rag_prompt_template`：提示模板

## 代码示例

### DeepSeekFactory使用示例

```php
// 获取DeepSeek实例
$deepseek = DeepSeekFactory::getInstance();

// 调用DeepSeek API
$response = $deepseek->chatCompletion($messages, $model);
```

### 策略模式使用示例

```php
// 获取策略实例
$strategy = StrategyFactory::getStrategy(null, $config);

// 生成回答
$result = $strategy->generateAnswer($question, $sessionId, $config);
```

## 修改内容

1. **新增文件**：
   - `library/deepseek/DeepSeekInterface.php`
   - `library/deepseek/DeepSeekFactory.php`
   - `library/deepseek/OfficialDeepSeek.php`
   - `library/deepseek/TencentDeepSeek.php`
   - `library/deepseek/BaiduDeepSeek.php`
   - `library/deepseek/AliDeepSeek.php`
   - `library/deepseek/HuaweiDeepSeek.php`

2. **修改文件**：
   - `application/common/model/DsAssistant.php`：使用DeepSeekFactory
   - `library/strategy/RagStrategy.php`：使用DeepSeekFactory和独立配置项
   - `library/strategy/TraditionalStrategy.php`：使用DeepSeekFactory
   - `config.php`：添加DeepSeek提供商选项和独立的RAG配置项

## 优势

1. **灵活性**：可以轻松切换不同云平台的DeepSeek API
2. **可扩展性**：可以方便地添加新的云平台支持
3. **可维护性**：使用工厂模式和策略模式，代码结构清晰
4. **容错性**：添加了异常处理，提高系统稳定性
5. **配置简化**：将复杂的数组配置拆分为独立的配置项，更易于管理

## 使用说明

1. 在系统后台管理界面，进入"插件设置 > 景区助手 > DeepSeek配置"
2. 选择DeepSeek提供商和相关配置
3. 根据选择的提供商，配置相应的API密钥和区域信息
4. 保存配置后，系统将自动使用选定的DeepSeek提供商

## 注意事项

1. 不同云平台的DeepSeek API可能有细微差异，系统已尽量兼容这些差异
2. 请确保配置正确的API密钥和区域信息
3. 各云平台的计费标准和限制可能不同，请参考各云平台的官方文档
