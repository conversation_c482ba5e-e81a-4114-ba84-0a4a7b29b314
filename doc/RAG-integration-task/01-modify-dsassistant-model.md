# 步骤1：修改DsAssistant模型

## 目标

修改`DsAssistant.php`模型，实现检索增强生成(RAG)模式，将本地知识库的检索结果作为上下文提供给DeepSeek大模型。

## 需要修改的文件

- `/Users/<USER>/Documents/app/fastadmin/application/common/model/DsAssistant.php`

## 具体修改

1. 修改`handleQuestion`方法，移除当前的二选一逻辑（本地知识库或大模型）
2. 新增`callDeepSeekWithContext`方法，用于将本地知识库检索结果作为上下文提供给DeepSeek
3. 保留日志记录和缓存机制

## 修改要点

```php
// 修改handleQuestion方法
public function handleQuestion($question, $userId = '', $sessionId = '', $platform = 'wechat', $ip = '')
{
    // 保留现有的会话状态和缓存检查代码
    
    // 从本地知识库检索相关信息（获取多条结果）
    $localResults = $this->searchFAQ($question, 3); // 获取前3个最相关的结果
    
    // 准备提供给DeepSeek的上下文信息
    $contextInfo = "";
    if (!empty($localResults)) {
        foreach ($localResults as $result) {
            $contextInfo .= "问题: {$result['question']}\n回答: {$result['answer']}\n\n";
        }
    }
    
    // 调用DeepSeek API，将本地知识库信息作为上下文
    $aiResponse = $this->callDeepSeekWithContext($question, $contextInfo, $sessionId);
    
    // 保留现有的日志记录、缓存和会话状态更新代码
    // 所有回答都标记为source='ai'
}

// 新增callDeepSeekWithContext方法
protected function callDeepSeekWithContext($question, $contextInfo, $sessionId = '')
{
    // 基于现有的callDeepSeek方法修改
    // 在系统提示中加入本地知识库的上下文信息
}
```

## 注意事项

- 确保修改后的代码能够处理本地知识库为空的情况
- 保留现有的错误处理和兜底策略
- 所有回答都标记为`source='ai'`，因为最终回答都是由DeepSeek生成的
