# 步骤3：实现DeepSeek上下文增强方法

## 目标

实现`callDeepSeekWithContext`方法，将本地知识库的检索结果作为上下文提供给DeepSeek大模型。

## 需要修改的文件

- `/Users/<USER>/Documents/app/fastadmin/application/common/model/DsAssistant.php`

## 具体修改

1. 基于现有的`callDeepSeek`方法创建新的`callDeepSeekWithContext`方法
2. 修改系统提示，加入本地知识库的上下文信息
3. 保留会话上下文处理逻辑

## 修改要点

```php
/**
 * 使用本地知识库上下文调用DeepSeek API
 *
 * @param string $question 用户问题
 * @param string $contextInfo 本地知识库提供的上下文信息
 * @param string $sessionId 会话ID
 * @return string AI生成的回答
 */
protected function callDeepSeekWithContext($question, $contextInfo, $sessionId = '')
{
    $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
    $apiKey = $config['deepseek_api_key'];
    $model = $config['deepseek_model'] ?: 'deepseek-chat';
    
    // 基础系统提示
    $basePrompt = $config['system_prompt'] ?: '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。';
    
    // 增强系统提示，加入本地知识库信息
    $systemPrompt = $basePrompt;
    if (!empty($contextInfo)) {
        $systemPrompt .= "\n\n以下是关于用户问题的相关信息，请参考这些信息回答问题，但不要直接复制这些内容，而是用自然、流畅的语言回答：\n\n" . $contextInfo;
    }
    
    // 准备消息（保留现有的会话上下文处理代码）
    $messages = [
        ['role' => 'system', 'content' => $systemPrompt]
    ];
    
    // 处理会话上下文（保留现有代码）
    
    // API调用部分（保留现有代码）
    
    return $result['choices'][0]['message']['content'];
}
```

## 注意事项

- 确保系统提示不会过长，DeepSeek API可能有输入长度限制
- 指导大模型如何使用提供的上下文信息
- 保留现有的错误处理机制
