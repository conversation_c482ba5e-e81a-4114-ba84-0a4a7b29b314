# 步骤2：修改搜索方法

## 目标

修改`searchFAQ`方法，使其能够返回多条相关结果，而不仅仅是最佳匹配。

## 需要修改的文件

- `/Users/<USER>/Documents/app/fastadmin/application/common/model/DsAssistant.php`

## 具体修改

1. 修改`searchFAQ`方法的参数，添加`$topK`参数，用于指定返回的结果数量
2. 修改返回值结构，从返回单个最佳匹配改为返回多个匹配结果的数组

## 修改要点

```php
/**
 * 在本地知识库中搜索问题
 *
 * @param string $question 用户问题
 * @param int $topK 返回的结果数量，默认为1（最佳匹配）
 * @return array 匹配结果数组
 */
protected function searchFAQ($question, $topK = 1)
{
    // 初始化向量搜索实例（保留现有代码）
    
    // 使用向量搜索，获取多条结果
    $vectorResults = $this->vectorSearch->search($question, $topK);
    
    // 记录日志（保留现有代码）
    
    if (!empty($vectorResults)) {
        $results = [];
        
        // 处理所有结果
        foreach ($vectorResults as $result) {
            $results[] = [
                'question' => $result['title'],   // 使用title字段作为question
                'answer' => $result['content'],   // 使用content字段作为answer
                'score' => $result['score']
            ];
        }
        
        return $results;
    } else {
        return [];
    }
}
```

## 注意事项

- 确保向量搜索实例的`search`方法能够返回指定数量的结果
- 保留现有的日志记录功能，以便调试
- 处理返回结果为空的情况
