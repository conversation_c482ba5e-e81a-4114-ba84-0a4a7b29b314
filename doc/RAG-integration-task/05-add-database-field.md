# 步骤5：添加数据库字段

## 目标

在`ds_chat_log`表中添加`extra`字段，用于存储RAG模式的额外信息。

## 需要修改的文件

- `/Users/<USER>/Documents/app/fastadmin/addons/dsassistant/application/admin/model/dsassistant/ChatLog.php`（如果存在）
- 创建数据库迁移文件

## 具体修改

1. 创建数据库迁移SQL文件，添加`extra`字段
2. 更新相关模型文件（如果存在）

## 修改要点

```sql
-- 创建迁移SQL文件
ALTER TABLE `fa_ds_chat_log` ADD COLUMN `extra` TEXT COMMENT 'RAG模式的额外信息，JSON格式';
```

```php
// 如果存在ChatLog模型，更新字段列表
protected $append = ['extra_text'];

public function getExtraTextAttr($value, $data)
{
    $extra = json_decode($data['extra'], true);
    if (empty($extra)) {
        return '';
    }
    
    $text = '';
    if (isset($extra['rag_mode']) && $extra['rag_mode']) {
        $text .= '使用RAG模式';
        if (isset($extra['has_local_context']) && $extra['has_local_context']) {
            $text .= '，使用了本地知识库增强';
        } else {
            $text .= '，未使用本地知识库增强';
        }
    }
    
    return $text;
}
```

## 注意事项

- 确保在执行SQL前备份数据库
- 考虑使用FastAdmin的迁移机制，而不是直接执行SQL
- 如果表中已有数据，考虑为`extra`字段设置默认值
