# 步骤7：测试和调试

## 目标

测试RAG模式的功能，确保系统能够正确地将本地知识库的检索结果作为上下文提供给DeepSeek大模型。

## 测试步骤

1. 创建测试脚本，用于测试RAG模式的各个组件
2. 测试不同类型的问题，观察系统行为
3. 分析日志，确认RAG模式正常工作

## 测试脚本示例

```php
// 创建测试脚本 /Users/<USER>/Documents/app/fastadmin/addons/dsassistant/application/admin/command/TestRag.php

namespace app\admin\command;

use app\common\model\DsAssistant;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Log;

class TestRag extends Command
{
    protected function configure()
    {
        $this->setName('dsassistant:test_rag')
            ->setDescription('测试RAG模式功能');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始测试RAG模式功能");
        
        // 测试问题列表
        $testQuestions = [
            '百花园在哪里？',
            '介绍一下百花园',
            '百花园有什么特色？',
            '景区门票多少钱？',
            '这个景区有什么好玩的地方？'
        ];
        
        $assistant = new DsAssistant();
        
        foreach ($testQuestions as $question) {
            $output->writeln("\n测试问题: {$question}");
            
            // 记录开始时间
            $startTime = microtime(true);
            
            // 调用handleQuestion方法
            $answer = $assistant->handleQuestion($question, 'test_user', 'test_session', 'cli', '127.0.0.1');
            
            // 记录结束时间
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            $output->writeln("回答: {$answer}");
            $output->writeln("处理时间: {$duration}ms");
        }
        
        $output->writeln("\nRAG模式测试完成");
    }
}
```

## 日志分析

1. 检查日志中的RAG相关信息
2. 确认系统是否正确检索本地知识库
3. 确认DeepSeek API是否接收到正确的上下文信息
4. 分析回答质量和处理时间

## 注意事项

- 确保测试环境与生产环境配置相似
- 测试不同类型的问题，包括本地知识库中有和没有的问题
- 记录并分析处理时间，确保RAG模式不会显著增加响应时间
- 比较RAG模式和纯大模型模式的回答质量
