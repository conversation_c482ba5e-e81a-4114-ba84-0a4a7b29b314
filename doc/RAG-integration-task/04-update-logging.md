# 步骤4：更新日志记录

## 目标

更新日志记录功能，记录RAG模式的相关信息，便于调试和优化。

## 需要修改的文件

- `/Users/<USER>/Documents/app/fastadmin/application/common/model/DsAssistant.php`

## 具体修改

1. 在`handleQuestion`方法中添加RAG相关的日志记录
2. 在`saveChatLog`方法中添加额外信息，记录是否使用了本地知识库增强

## 修改要点

```php
// 在handleQuestion方法中添加日志记录
$localResults = $this->searchFAQ($question, 3);
Log::record("从本地知识库检索到 " . count($localResults) . " 条相关信息", 'info');

if (!empty($localResults)) {
    // 记录检索到的信息
    foreach ($localResults as $index => $result) {
        Log::record("本地知识库结果 #{$index}: 问题=\"{$result['question']}\", 得分={$result['score']}", 'info');
    }
    
    // 准备上下文信息
    // ...
    
    Log::record("使用RAG模式调用DeepSeek API", 'info');
} else {
    Log::record("本地知识库中未找到相关信息，使用纯大模型模式", 'info');
}

// 修改saveChatLog方法，添加额外信息
protected function saveChatLog($question, $answer, $source, $score, $userId, $sessionId, $platform, $ip, $hasLocalContext = false)
{
    // 添加额外字段记录是否使用了本地知识库增强
    $extraData = json_encode([
        'rag_mode' => true,
        'has_local_context' => $hasLocalContext
    ]);
    
    Db::name('ds_chat_log')->insert([
        'user_id' => $userId,
        'session_id' => $sessionId,
        'question' => $question,
        'answer' => $answer,
        'source' => $source,  // 始终为'ai'
        'score' => $score,
        'platform' => $platform,
        'ip' => $ip,
        'extra' => $extraData,  // 新增字段，需要确保数据库表中有此字段
        'createtime' => time()
    ]);
}
```

## 注意事项

- 确保不记录过多日志，避免日志文件过大
- 如果`ds_chat_log`表中没有`extra`字段，需要添加此字段
- 考虑添加配置选项，允许管理员控制日志详细程度
