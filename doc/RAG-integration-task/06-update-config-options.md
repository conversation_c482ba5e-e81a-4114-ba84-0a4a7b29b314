# 步骤6：更新配置选项

## 目标

添加RAG模式相关的配置选项，允许管理员控制RAG模式的行为。

## 需要修改的文件

- `/Users/<USER>/Documents/app/fastadmin/addons/dsassistant/config.php`

## 具体修改

1. 添加RAG模式相关的配置选项
2. 为每个选项提供合理的默认值和说明

## 修改要点

```php
// 在config.php中添加以下配置选项
[
    'group' => 'RAG模式设置',
    'type' => 'array',
    'name' => 'rag_config',
    'title' => 'RAG模式配置',
    'value' => [
        'enabled' => 1,                 // 是否启用RAG模式
        'top_k' => 3,                   // 从本地知识库检索的结果数量
        'min_score' => 0.3,             // 本地知识库结果的最低得分要求
        'context_format' => "问题: {question}\n回答: {answer}\n\n", // 上下文格式
        'prompt_template' => "以下是关于用户问题的相关信息，请参考这些信息回答问题，但不要直接复制这些内容，而是用自然、流畅的语言回答：\n\n{context}" // 提示模板
    ],
    'tip' => '配置RAG模式的行为，包括是否启用、检索结果数量等',
    'rule' => '',
    'extend' => ''
]
```

## 注意事项

- 确保配置选项有合理的默认值，即使管理员不进行配置也能正常工作
- 提供详细的说明，帮助管理员理解每个选项的作用
- 考虑添加配置界面，使管理员可以通过界面修改这些选项
