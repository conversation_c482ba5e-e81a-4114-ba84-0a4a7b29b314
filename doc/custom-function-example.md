# 自定义函数示例

本文档提供了如何为dsassistant插件添加自定义函数的示例。

## 函数定义文件格式

每个函数定义文件应返回一个标准格式的函数定义数组，保存在`addons/dsassistant/functions/custom/`目录下。

## 示例：添加餐厅推荐函数

以下是一个自定义函数的示例，用于推荐景区附近的餐厅：

```php
<?php
/**
 * 推荐餐厅函数定义
 * 
 * 用于推荐景区附近的餐厅
 */
return [
    'name' => 'recommendRestaurant',
    'description' => '推荐景区附近的餐厅',
    'parameters' => [
        'type' => 'object',
        'properties' => [
            'cuisine' => [
                'type' => 'string',
                'description' => '菜系类型，如川菜、粤菜等'
            ],
            'priceRange' => [
                'type' => 'string',
                'description' => '价格范围，如经济型、中档、高档等'
            ],
            'distance' => [
                'type' => 'number',
                'description' => '距离景区的最大距离（米）'
            ],
            'numberOfPeople' => [
                'type' => 'integer',
                'description' => '就餐人数'
            ]
        ],
        'required' => ['cuisine']
    ],
    'enabled' => true,  // 默认启用状态
    'priority' => 400   // 优先级，数字越小优先级越高
];
```

将上述代码保存为`addons/dsassistant/functions/custom/recommend_restaurant.php`文件。

## 函数定义字段说明

- `name`：函数名称，必须唯一
- `description`：函数描述，用于告诉AI这个函数的用途
- `parameters`：函数参数定义，遵循JSON Schema格式
  - `type`：参数类型，通常为"object"
  - `properties`：参数属性定义
    - 每个参数包含类型(type)和描述(description)
  - `required`：必填参数列表
- `enabled`：是否默认启用
- `priority`：优先级，数字越小优先级越高

## 注意事项

1. 函数名称必须唯一，不能与核心函数或其他自定义函数重名
2. 参数定义应尽量清晰，以便AI正确理解和使用
3. 添加自定义函数后，需要在管理后台的"函数管理"中启用该函数
4. 插件升级时，`custom`目录中的函数定义不会被覆盖

## 前端处理

如果您的自定义函数需要前端交互，您需要在前端代码中添加相应的处理逻辑。例如，在`ChatMessage.vue`组件中添加对新函数的支持：

```javascript
// 处理函数调用
handleFunctionCall(functionCall) {
  const name = functionCall.name;
  const params = functionCall.parameters;
  
  switch (name) {
    case 'recommendRestaurant':
      // 处理餐厅推荐函数
      this.showRestaurantRecommendation(params);
      break;
    // 其他函数处理...
  }
},

// 显示餐厅推荐
showRestaurantRecommendation(params) {
  // 实现餐厅推荐的UI展示逻辑
  // ...
}
```

## 示例效果

当用户询问"附近有什么好吃的川菜馆"时，AI可能会调用`recommendRestaurant`函数，并传入以下参数：

```json
{
  "cuisine": "川菜",
  "priceRange": "中档",
  "distance": 1000,
  "numberOfPeople": 2
}
```

前端接收到这些参数后，可以展示相应的餐厅推荐卡片或链接，引导用户查看详细信息。
