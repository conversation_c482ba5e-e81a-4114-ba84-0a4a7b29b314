# 景区预警推送功能实现文档

## 功能概述

实现真正的预警推送功能，通过以下渠道：
- 对于微信公众号：集成微信模板消息API
- 对于小程序：集成微信订阅消息API

## 技术实现

### 1. 修改 `library/Wechat.php`

- 添加了模板消息ID属性和构造函数参数
- 优化了XML解析方法，兼容PHP 8.0+
- 改进了获取访问令牌方法，添加了缓存支持
- 添加了获取用户信息方法
- 添加了发送模板消息方法
- 添加了发送预警模板消息方法
- 添加了辅助方法：获取类型文本、级别文本和级别颜色

### 2. 修改 `controller/Wechat.php`

- 增强了事件处理功能，特别是关注和取消关注事件
- 添加了保存用户信息的方法，用于收集用户OpenID
- 当用户关注时，自动保存用户信息到数据库
- 当用户取消关注时，更新用户状态

### 3. 修改 `application/admin/controller/dsassistant/Warning.php`

- 改进了推送预警信息方法
- 添加了微信模板消息推送功能
- 添加了推送成功和失败计数
- 优化了错误处理和日志记录

### 4. 使用现有的用户表结构

- 利用已有的`fa_ds_user_wechat`表存储用户OpenID和关注状态，无需创建新表

## 功能流程

### 用户关注公众号

1. 系统自动保存用户OpenID和基本信息
2. 更新用户关注状态为已关注

### 管理员创建预警信息

1. 在后台创建预警信息（标题、内容、类型、级别等）

### 管理员推送预警信息

1. 选择需要推送的预警信息，点击"推送"按钮
2. 系统记录推送日志
3. 系统获取所有已关注用户的OpenID
4. 系统向每个用户发送微信模板消息
5. 显示推送成功和失败的数量

### 用户接收预警信息

1. 用户收到微信模板消息通知
2. 用户点击消息可跳转到预警详情页面

## 配置要求

确保在 `config.php` 中包含以下微信公众号配置：

```php
'wechat_appid' => 'wx1234567890abcdef', // 公众号AppID
'wechat_appsecret' => 'abcdef1234567890abcdef1234567890', // 公众号AppSecret
'wechat_token' => 'yourtoken', // 公众号Token
'wechat_template_id' => 'ABC1234567890abcdef1234567890ABC', // 预警消息模板ID
```

## 微信模板消息格式

预警模板消息包含以下字段：

| 字段 | 内容 |
|------|------|
| first | 景区预警通知 |
| keyword1 | 预警标题 |
| keyword2 | 预警类型（人流预警、天气预警、交通预警等） |
| keyword3 | 预警级别（提示、警告、危险） |
| keyword4 | 发布时间 |
| remark | 预警内容 |

## 注意事项

- 微信模板消息有每日发送限制，建议合理规划预警推送频率
- 推送功能应考虑分批处理，避免一次性推送过多消息导致超时
- 对于重要预警，可以考虑添加推送失败重试机制
- 用户取消关注后将无法接收预警推送，需在用户重新关注时更新状态

## 未来扩展

- 实现基于用户位置的精准预警推送
- 添加预警推送统计和分析功能
- 支持用户自定义预警接收设置
- 集成更多推送渠道（如短信、APP推送等）