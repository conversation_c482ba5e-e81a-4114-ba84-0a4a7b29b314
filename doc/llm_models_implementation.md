# LLM模型管理实现方案

## 概述

本文档描述了LLM模型管理的实现方案，包括数据库设计、类设计和API调用流程。

## 数据库设计

### 表结构

使用独立的数据库表`fa_ds_llm_models`来管理LLM模型配置，表结构如下：

```sql
CREATE TABLE IF NOT EXISTS `fa_ds_llm_models` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `model_id` varchar(50) NOT NULL COMMENT '模型ID',
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `provider` varchar(50) NOT NULL COMMENT '服务商标识',
  `provider_name` varchar(100) NOT NULL COMMENT '服务商名称',
  `base_url` varchar(255) NOT NULL COMMENT 'API基础URL',
  `api_key` varchar(255) DEFAULT '' COMMENT 'API密钥',
  `supports_functions` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持函数调用',
  `max_tokens` int(11) NOT NULL DEFAULT 4096 COMMENT '最大token数',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认模型',
  `weight` int(10) unsigned DEFAULT 0 COMMENT '排序权重',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `model_id` (`model_id`),
  KEY `provider` (`provider`),
  KEY `enabled` (`enabled`),
  KEY `is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LLM模型配置表';
```

### 默认数据

```sql
INSERT INTO `fa_ds_llm_models` (`model_id`, `name`, `provider`, `provider_name`, `base_url`, `api_key`, `supports_functions`, `max_tokens`, `enabled`, `is_default`, `weight`, `createtime`, `updatetime`) VALUES
('deepseek-reasoner', 'DeepSeek Reasoner', 'deepseek', 'DeepSeek', 'https://api.deepseek.com/v1', '', 1, 4096, 1, 1, 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('deepseek-chat', 'DeepSeek Chat', 'deepseek', 'DeepSeek', 'https://api.deepseek.com/v1', '', 0, 4096, 1, 0, 90, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
```

## 类设计

### LLMModelManager

`LLMModelManager`类负责管理LLM模型配置，提供以下功能：

- 获取所有模型
- 获取指定模型
- 获取默认模型
- 按服务商获取模型
- 添加或更新模型
- 设置默认模型
- 删除模型
- 清除缓存
- 从旧配置迁移数据

### LLMClient

`LLMClient`类负责调用LLM API，提供以下功能：

- 聊天补全
- 带函数调用的聊天补全
- 调用DeepSeek API
- 处理DeepSeek函数调用结果

### LLMConfig

`LLMConfig`类为兼容旧代码而保留，提供以下功能：

- 获取LLM配置
- 获取默认配置
- 保存LLM配置
- 验证配置
- 获取所有服务商
- 获取服务商信息
- 获取所有模型
- 获取模型信息
- 获取默认模型ID
- 获取默认模型信息

## API调用流程

### 基本调用流程

1. 创建LLMClient实例
2. 调用chatCompletion方法
3. 处理返回结果

```php
// 创建LLMClient实例
$client = new LLMClient();

// 调用chatCompletion方法
$messages = [
    ['role' => 'system', 'content' => '你是一个助手'],
    ['role' => 'user', 'content' => '你好']
];
$result = $client->chatCompletion($messages);

// 处理返回结果
echo $result;
```

### 带函数调用的调用流程

1. 创建LLMClient实例
2. 准备函数定义
3. 调用chatCompletionWithFunctions方法
4. 处理返回结果

```php
// 创建LLMClient实例
$client = new LLMClient('deepseek', 'deepseek-reasoner');

// 准备函数定义
$functions = [
    [
        'name' => 'get_weather',
        'description' => '获取指定城市的天气信息',
        'parameters' => [
            'type' => 'object',
            'properties' => [
                'city' => [
                    'type' => 'string',
                    'description' => '城市名称'
                ]
            ],
            'required' => ['city']
        ]
    ]
];

// 调用chatCompletionWithFunctions方法
$messages = [
    ['role' => 'system', 'content' => '你是一个助手'],
    ['role' => 'user', 'content' => '北京今天天气怎么样？']
];
$result = $client->chatCompletionWithFunctions($messages, $functions);

// 处理返回结果
$content = $result['content'];
$functionCalls = $result['function_calls'];

echo "回答内容: $content\n";
echo "函数调用: " . json_encode($functionCalls, JSON_UNESCAPED_UNICODE) . "\n";
```

## 缓存机制

为了提高性能，LLMModelManager使用缓存机制，缓存时间为3600秒（1小时）。缓存的内容包括：

- 所有模型列表
- 指定模型信息
- 默认模型信息
- 按服务商的模型列表

当模型配置发生变化时，会自动清除相关缓存。

## 迁移方案

从旧的配置方式迁移到新的配置方式，可以使用以下方法：

1. 使用LLMModelManager::migrateFromOldConfig方法迁移数据
2. 更新相关代码，使用新的LLMClient类

```php
// 从旧配置迁移数据
$oldConfig = Contentconfig::getConfig('llm_config');
LLMModelManager::migrateFromOldConfig($oldConfig);

// 更新相关代码
// 旧代码
// $client = new LLMClient();
// 新代码
$client = new LLMClient();
```

## 注意事项

1. LLMConfig类仅为兼容旧代码而保留，新代码应直接使用LLMModelManager类
2. LLMClient类的构造函数支持指定服务商和模型ID，如果不指定则使用默认模型
3. 模型配置的API密钥应该妥善保管，避免泄露
4. 当模型配置发生变化时，会自动清除相关缓存，无需手动清除
