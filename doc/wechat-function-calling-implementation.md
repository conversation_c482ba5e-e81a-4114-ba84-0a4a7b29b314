# 微信公众号 Function Calling 实现方案

本文档详细说明了在微信公众号中实现 Function Calling 功能的方案、步骤和所需工作。

## 背景

DeepSeek AI 支持 Function Calling 功能，可以根据用户的问题调用特定函数执行操作。在网页和 App 中，我们可以通过自定义 UI 组件（如卡片）来展示这些功能。但在微信公众号环境中，由于平台限制，我们需要采用不同的方案来实现类似的交互体验。

## 实现方案

我们采用**富文本卡片 + 快捷菜单**的组合方案，这种方案既能提供良好的视觉体验，又能保持对话的流畅性。

### 方案概述

1. **富文本卡片**：对于需要视觉展示的功能（如地图、景点详情），使用图文消息，每个卡片链接到相应的 H5 页面
2. **快捷菜单**：对于简单的查询类操作，在文本消息后附加快捷菜单选项，用户回复数字选择相应功能
3. **状态管理**：在服务器端保存用户会话状态，记录当前可用的函数调用，根据用户的选择执行相应操作

## 实现步骤

### 1. 后端接口调整

#### 1.1 修改 Wechat 控制器

```php
<?php
namespace addons\dsassistant\controller;

use addons\dsassistant\library\deepseek\DeepSeekFactory;
use addons\dsassistant\model\WechatSession;

class Wechat extends Base
{
    protected $wechat;
    protected $deepseek;
    
    public function _initialize()
    {
        parent::_initialize();
        $this->wechat = new \WeChat\Receive(config('wechat.'));
        $this->deepseek = DeepSeekFactory::getInstance();
    }
    
    /**
     * 处理微信消息
     */
    public function index()
    {
        $this->wechat->valid();
        
        // 处理用户消息
        $receiveMessage = $this->wechat->getReceive();
        if ($receiveMessage instanceof \WeChat\Contracts\Message) {
            // 获取用户OpenID
            $openid = $receiveMessage->FromUserName;
            
            // 处理文本消息
            if ($receiveMessage->MsgType == 'text') {
                $content = $receiveMessage->Content;
                
                // 检查是否是菜单选择
                if (preg_match('/^[1-9]\d*$/', $content)) {
                    return $this->handleMenuSelection($openid, $content);
                }
                
                // 正常对话流程
                return $this->handleQuestion($openid, $content);
            }
            
            // 处理其他类型消息...
        }
        
        return $this->wechat->text('欢迎使用景区智能助理！')->reply();
    }
    
    /**
     * 处理用户问题
     */
    protected function handleQuestion($openid, $question)
    {
        // 调用DeepSeek API获取回答和函数调用
        $response = $this->callDeepSeekApi($question, $openid);
        
        // 提取文本回答和函数调用
        $answer = $response['answer'];
        $functionCalls = $response['functionCalls'] ?? [];
        
        // 如果没有函数调用，直接返回文本回答
        if (empty($functionCalls)) {
            return $this->wechat->text($answer)->reply();
        }
        
        // 如果只有一个函数调用，直接返回图文消息
        if (count($functionCalls) == 1) {
            return $this->generateNewsForFunction($answer, $functionCalls[0]);
        }
        
        // 如果有多个函数调用，保存到会话中并提供选择菜单
        $this->saveActionsToSession($openid, $functionCalls);
        
        // 构建回复消息，包含文本回答和选择菜单
        $reply = $answer . "\n\n您可以选择以下操作：\n";
        foreach ($functionCalls as $index => $call) {
            $reply .= ($index + 1) . ". " . $this->getFunctionTitle($call) . "\n";
        }
        
        return $this->wechat->text($reply)->reply();
    }
    
    /**
     * 处理菜单选择
     */
    protected function handleMenuSelection($openid, $selection)
    {
        // 从会话中获取保存的函数调用
        $functionCalls = $this->getActionsFromSession($openid);
        
        // 检查选择是否有效
        $index = (int)$selection - 1;
        if (!isset($functionCalls[$index])) {
            return $this->wechat->text('无效的选择，请重新输入您的问题。')->reply();
        }
        
        // 执行选择的函数
        $functionCall = $functionCalls[$index];
        return $this->executeFunction($openid, $functionCall);
    }
    
    /**
     * 调用DeepSeek API
     */
    protected function callDeepSeekApi($question, $openid)
    {
        // 调用DeepSeek API
        $result = $this->deepseek->chat([
            'question' => $question,
            'user_id' => $openid,
            'platform' => 'wechat'
        ]);
        
        return $result;
    }
    
    // 其他辅助方法...
}
```

#### 1.2 添加会话管理模型

```php
<?php
namespace addons\dsassistant\model;

use think\Model;

class WechatSession extends Model
{
    // 表名
    protected $name = 'dsassistant_wechat_session';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    
    /**
     * 保存用户会话数据
     */
    public static function saveData($openid, $key, $data, $expire = 1800)
    {
        $session = self::where('openid', $openid)->where('key', $key)->find();
        
        if (!$session) {
            $session = new self();
            $session->openid = $openid;
            $session->key = $key;
        }
        
        $session->data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $session->expire_time = time() + $expire;
        $session->save();
        
        return true;
    }
    
    /**
     * 获取用户会话数据
     */
    public static function getData($openid, $key)
    {
        $session = self::where('openid', $openid)
            ->where('key', $key)
            ->where('expire_time', '>', time())
            ->find();
        
        if (!$session) {
            return null;
        }
        
        return json_decode($session->data, true);
    }
    
    /**
     * 清理过期会话数据
     */
    public static function cleanExpired()
    {
        return self::where('expire_time', '<', time())->delete();
    }
}
```

### 2. 数据库表创建

需要创建会话管理表：

```sql
CREATE TABLE IF NOT EXISTS `fa_dsassistant_wechat_session` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `openid` varchar(64) NOT NULL COMMENT '用户OpenID',
  `key` varchar(64) NOT NULL COMMENT '会话键名',
  `data` text COMMENT '会话数据',
  `expire_time` int(10) unsigned NOT NULL COMMENT '过期时间',
  `create_time` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid_key` (`openid`,`key`),
  KEY `expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信会话数据表';
```

### 3. 辅助方法实现

在 Wechat 控制器中添加以下辅助方法：

```php
/**
 * 生成图文消息
 */
protected function generateNewsForFunction($answer, $functionCall)
{
    $article = [
        'title' => $this->getFunctionTitle($functionCall),
        'description' => $answer,
        'picurl' => $this->getFunctionImage($functionCall['name']),
        'url' => $this->generateUrl($functionCall['name'], $functionCall['parameters'])
    ];
    
    return $this->wechat->news([$article])->reply();
}

/**
 * 获取函数标题
 */
protected function getFunctionTitle($functionCall)
{
    switch ($functionCall['name']) {
        case 'showOnMap':
            return $functionCall['parameters']['spotName'] . '的位置';
        case 'showScenicSpotDetails':
            return $functionCall['parameters']['spotName'] . '详情';
        case 'searchNearby':
            return '搜索附近' . ($functionCall['parameters']['keyword'] ?? '地点');
        case 'bookTicket':
            return '预订' . ($functionCall['parameters']['spotName'] ?? '景点') . '门票';
        case 'getWeather':
            return ($functionCall['parameters']['location'] ?? '当地') . '天气';
        default:
            return $functionCall['name'];
    }
}

/**
 * 获取函数图片
 */
protected function getFunctionImage($functionName)
{
    $baseUrl = config('site.cdnurl') . '/assets/addons/dsassistant/images/functions/';
    
    $images = [
        'showOnMap' => 'map.jpg',
        'showScenicSpotDetails' => 'scenic.jpg',
        'searchNearby' => 'search.jpg',
        'bookTicket' => 'ticket.jpg',
        'getWeather' => 'weather.jpg'
    ];
    
    return $baseUrl . ($images[$functionName] ?? 'default.jpg');
}

/**
 * 生成H5页面URL
 */
protected function generateUrl($functionName, $parameters)
{
    $baseUrl = request()->domain() . '/addons/dsassistant/wechat/h5/';
    
    switch ($functionName) {
        case 'showOnMap':
            return $baseUrl . 'map?' . http_build_query([
                'spotId' => $parameters['spotId'] ?? '',
                'spotName' => $parameters['spotName'] ?? '',
                'latitude' => $parameters['latitude'] ?? '',
                'longitude' => $parameters['longitude'] ?? ''
            ]);
        case 'showScenicSpotDetails':
            return $baseUrl . 'scenic?' . http_build_query([
                'spotId' => $parameters['spotId'] ?? '',
                'spotName' => $parameters['spotName'] ?? ''
            ]);
        // 其他函数的URL生成...
        default:
            return $baseUrl . 'index';
    }
}

/**
 * 保存函数调用到会话
 */
protected function saveActionsToSession($openid, $functionCalls)
{
    return WechatSession::saveData($openid, 'function_calls', $functionCalls, 1800); // 30分钟有效
}

/**
 * 从会话中获取函数调用
 */
protected function getActionsFromSession($openid)
{
    return WechatSession::getData($openid, 'function_calls') ?: [];
}

/**
 * 执行函数
 */
protected function executeFunction($openid, $functionCall)
{
    // 根据函数名执行不同操作
    switch ($functionCall['name']) {
        case 'showOnMap':
        case 'showScenicSpotDetails':
        case 'searchNearby':
        case 'bookTicket':
        case 'getWeather':
            // 这些函数都通过图文消息实现
            return $this->generateNewsForFunction('请点击查看详细信息', $functionCall);
        
        // 可以添加其他直接在公众号中执行的函数
        
        default:
            return $this->wechat->text('暂不支持该操作')->reply();
    }
}
```

### 4. H5页面实现

需要创建以下H5页面：

1. **地图页面** (map.html)
2. **景点详情页面** (scenic.html)
3. **搜索页面** (search.html)
4. **订票页面** (ticket.html)
5. **天气页面** (weather.html)

这些页面应该放在 `addons/dsassistant/view/wechat/h5/` 目录下，并创建相应的控制器方法。

## 工作清单

以下是实现微信公众号 Function Calling 功能需要完成的工作清单：

### 后端开发

- [ ] 修改 Wechat 控制器，添加处理函数调用的方法
- [ ] 创建 WechatSession 模型，实现会话管理
- [ ] 创建数据库表 `dsassistant_wechat_session`
- [ ] 实现辅助方法（生成图文消息、获取函数标题等）
- [ ] 创建 H5 页面的控制器方法

### 前端开发

- [ ] 设计并实现地图页面 (map.html)
- [ ] 设计并实现景点详情页面 (scenic.html)
- [ ] 设计并实现搜索页面 (search.html)
- [ ] 设计并实现订票页面 (ticket.html)
- [ ] 设计并实现天气页面 (weather.html)
- [ ] 准备函数图片资源

### 测试与优化

- [ ] 测试单个函数调用场景
- [ ] 测试多个函数调用场景
- [ ] 测试会话过期处理
- [ ] 优化响应速度和用户体验
- [ ] 添加错误处理和日志记录

## 注意事项

1. **会话管理**：定期清理过期会话数据，避免数据库膨胀
2. **安全性**：验证所有用户输入，防止注入攻击
3. **性能优化**：缓存常用数据，减少数据库查询
4. **用户体验**：保持回复消息简洁明了，避免过长的文本
5. **兼容性**：确保H5页面在各种移动设备上正常显示

## 后续优化方向

1. **个性化推荐**：根据用户历史交互记录，提供个性化的函数推荐
2. **多模态支持**：支持图片识别等多模态输入，触发相应的函数调用
3. **统计分析**：记录函数调用数据，分析用户偏好和使用模式
4. **A/B测试**：对不同的交互方式进行A/B测试，优化用户体验
