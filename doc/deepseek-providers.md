# DeepSeek多云平台支持

## 概述

本文档介绍了景区助手系统对多个云平台提供的DeepSeek大模型的支持。通过工厂模式实现，系统可以灵活切换不同云平台的DeepSeek API，提高系统的可用性和灵活性。

## 支持的云平台

目前系统支持以下云平台的DeepSeek大模型：

1. **DeepSeek官方** - 直接调用DeepSeek官方API
2. **腾讯云DeepSeek** - 使用腾讯云提供的DeepSeek大模型
3. **百度智能云DeepSeek** - 使用百度智能云提供的DeepSeek大模型
4. **阿里云DeepSeek** - 使用阿里云提供的DeepSeek大模型
5. **华为云DeepSeek** - 使用华为云提供的DeepSeek大模型

## 配置方法

在系统后台管理界面，进入"插件设置 > 景区助手 > DeepSeek配置"，可以选择DeepSeek提供商和相关配置：

1. **DeepSeek提供商** - 选择使用哪个云平台的DeepSeek大模型
2. **API密钥配置** - 根据选择的提供商，配置相应的API密钥

### 各提供商配置说明

#### DeepSeek官方

- **DeepSeek API密钥** - 在DeepSeek官网申请的API密钥
- **DeepSeek模型** - 选择使用的DeepSeek模型，如deepseek-chat或deepseek-chat-pro

#### 腾讯云DeepSeek

使用腾讯云API密钥访问腾讯云提供的DeepSeek大模型：

- **腾讯云SecretId** - 腾讯云API密钥ID
- **腾讯云SecretKey** - 腾讯云API密钥
- **腾讯云区域** - 选择腾讯云区域，如广州、上海等

#### 百度智能云DeepSeek

使用百度智能云API密钥访问百度智能云提供的DeepSeek大模型：

- **百度API密钥** - 百度智能云API密钥
- **百度密钥** - 百度智能云密钥

#### 阿里云DeepSeek

使用阿里云API密钥访问阿里云提供的DeepSeek大模型：

- **阿里API密钥** - 阿里云API密钥

#### 华为云DeepSeek

使用华为云API密钥访问华为云提供的DeepSeek大模型：

- **华为云AK** - 华为云API密钥ID
- **华为云SK** - 华为云API密钥
- **华为云项目ID** - 华为云项目ID
- **华为云区域** - 选择华为云区域，如华北-北京四等

## 技术实现

系统使用工厂模式实现对多云平台DeepSeek API的支持：

1. **DeepSeekInterface** - 定义所有DeepSeek实现必须提供的方法
2. **DeepSeekFactory** - 工厂类，根据配置创建不同云平台的DeepSeek实现
3. **具体实现类** - 针对每个云平台的具体实现，如OfficialDeepSeek、TencentDeepSeek等

### 代码示例

```php
// 获取DeepSeek实例
$deepseek = DeepSeekFactory::getInstance();

// 调用DeepSeek API
$response = $deepseek->chatCompletion($messages, $model);
```

## 注意事项

1. 不同云平台的DeepSeek API可能有细微差异，系统已尽量兼容这些差异
2. 请确保配置正确的API密钥和区域信息
3. 如果主要提供商不可用，可以考虑添加备用提供商功能
4. 各云平台的计费标准和限制可能不同，请参考各云平台的官方文档

## 故障排除

如果遇到DeepSeek API调用问题，请检查：

1. API密钥是否正确配置
2. 网络连接是否正常
3. 查看系统日志中的错误信息
4. 确认所选云平台的DeepSeek服务是否可用

## 未来计划

1. 添加更多云平台的支持
2. 实现自动故障转移功能，当主要提供商不可用时自动切换到备用提供商
3. 添加性能监控和统计功能，比较不同提供商的响应时间和质量
