# 使用Function Calling实现聊天消息中的交互元素

本文档描述了如何使用Function Calling功能在AI助理的回复中添加可交互的元素（如链接或卡片），使用户能够点击这些元素打开小程序的其他页面（如地图导航页面）。

## 需求场景

用户在与AI助理对话时，可能会询问关于景点位置、详情等信息。例如：

- 用户：请告诉我"西湖"在哪里？
- 助理：西湖位于杭州市区西面，是中国著名的风景名胜区。[查看地图位置]

当用户点击"[查看地图位置]"链接时，系统会跳转到小程序内的地图页面，显示西湖的位置。

## Function Calling简介

Function Calling是大型语言模型的一个功能，它允许模型识别何时应该调用特定的函数，并生成适当的参数。在对话场景中，这非常适合用来生成结构化的响应，包括导航链接或卡片。

### 工作原理

1. **定义函数**：在后端定义一系列函数，如`showOnMap`、`showScenicSpotDetails`等
2. **模型调用**：当用户询问关于景点位置等信息时，模型会识别需要调用`showOnMap`函数
3. **参数生成**：模型会生成必要的参数，如景点名称、ID、坐标等
4. **前端渲染**：前端接收到这些函数调用信息，将其渲染为可交互的元素

## 实现方案

### 1. 后端定义函数架构

在与大模型（如DeepSeek）交互的API中，定义类似这样的函数架构：

```json
{
  "functions": [
    {
      "name": "showOnMap",
      "description": "显示景点在地图上的位置",
      "parameters": {
        "type": "object",
        "properties": {
          "spotId": {
            "type": "string",
            "description": "景点ID"
          },
          "spotName": {
            "type": "string",
            "description": "景点名称"
          },
          "latitude": {
            "type": "number",
            "description": "纬度"
          },
          "longitude": {
            "type": "number",
            "description": "经度"
          }
        },
        "required": ["spotName"]
      }
    },
    {
      "name": "showScenicSpotDetails",
      "description": "显示景点详细信息",
      "parameters": {
        "type": "object",
        "properties": {
          "spotId": {
            "type": "string",
            "description": "景点ID"
          },
          "spotName": {
            "type": "string",
            "description": "景点名称"
          }
        },
        "required": ["spotId"]
      }
    }
  ]
}
```

### 2. 后端处理模型响应

当模型返回包含函数调用的响应时，后端需要处理这些信息：

```php
// 示例PHP代码
function processModelResponse($modelResponse) {
    $processedResponse = [
        'text' => $modelResponse['text'],
        'functionCalls' => []
    ];
    
    if (isset($modelResponse['function_calls'])) {
        foreach ($modelResponse['function_calls'] as $call) {
            $processedResponse['functionCalls'][] = [
                'name' => $call['name'],
                'parameters' => $call['parameters']
            ];
        }
    }
    
    return $processedResponse;
}
```

### 3. 前端解析和渲染

在前端，需要修改消息处理逻辑，识别并处理函数调用：

```javascript
// 在ChatService.js中添加处理函数调用的逻辑
sendMessage(message, onSuccess, onFail) {
    uni.request({
        url: this.baseUrl + '/api/chat',
        method: 'POST',
        data: {
            question: message,
            user_id: this.userId,
            device_id: this.deviceId,
            session_id: this.sessionId,
            platform: this.platform
        },
        success: (res) => {
            if (res.data.code === 1) {
                // 处理包含函数调用的响应
                const response = {
                    text: res.data.data.answer,
                    functionCalls: res.data.data.functionCalls || []
                };
                onSuccess && onSuccess(response);
            } else {
                onFail && onFail('抱歉，出现了一些问题：' + res.data.msg);
            }
        },
        fail: () => {
            onFail && onFail('抱歉，网络连接出现问题，请稍后再试。');
        }
    });
}
```

### 4. 创建函数调用卡片组件

创建一个新的组件来显示函数调用卡片：

```vue
<!-- components/chat/FunctionCard.vue -->
<template>
  <view class="function-card" @tap="handleCardTap">
    <view class="card-icon">
      <image :src="iconSrc" mode="aspectFit"></image>
    </view>
    <view class="card-content">
      <view class="card-title">{{ title }}</view>
      <view class="card-description">{{ description }}</view>
    </view>
    <view class="card-action">
      <text>{{ actionText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FunctionCard',
  props: {
    functionName: {
      type: String,
      required: true
    },
    parameters: {
      type: Object,
      required: true
    }
  },
  computed: {
    iconSrc() {
      const icons = {
        'showOnMap': '/static/images/map-icon.svg',
        'showScenicSpotDetails': '/static/images/info-icon.svg'
      };
      return icons[this.functionName] || '/static/images/default-icon.svg';
    },
    title() {
      if (this.functionName === 'showOnMap') {
        return `${this.parameters.spotName}的位置`;
      } else if (this.functionName === 'showScenicSpotDetails') {
        return `${this.parameters.spotName}详情`;
      }
      return '查看详情';
    },
    description() {
      if (this.functionName === 'showOnMap') {
        return '点击查看地图位置';
      } else if (this.functionName === 'showScenicSpotDetails') {
        return '点击查看景点详细信息';
      }
      return '点击查看';
    },
    actionText() {
      const actions = {
        'showOnMap': '查看地图',
        'showScenicSpotDetails': '查看详情'
      };
      return actions[this.functionName] || '查看';
    }
  },
  methods: {
    handleCardTap() {
      if (this.functionName === 'showOnMap') {
        uni.navigateTo({
          url: `/pages/map/map?spotId=${this.parameters.spotId || ''}&spotName=${this.parameters.spotName || ''}&latitude=${this.parameters.latitude || ''}&longitude=${this.parameters.longitude || ''}`
        });
      } else if (this.functionName === 'showScenicSpotDetails') {
        uni.navigateTo({
          url: `/pages/scenic/detail?spotId=${this.parameters.spotId || ''}&spotName=${this.parameters.spotName || ''}`
        });
      }
    }
  }
}
</script>
```

### 5. 修改ChatMessage组件

修改ChatMessage组件，使其能够显示函数调用卡片：

```vue
<!-- 修改 components/chat/ChatMessage.vue -->
<template>
  <!-- 现有模板保持不变 -->
  
  <!-- 在助手消息气泡中添加函数调用卡片 -->
  <view class="chat-bubble assistant-bubble" v-else>
    <!-- 如果有解析后的消息，则显示解析后的消息 -->
    <rich-text v-if="parsedMessage" class="markdown-content" :nodes="parsedMessage"></rich-text>
    <!-- 如果没有解析后的消息，则显示原始消息 -->
    <text v-else>{{message}}</text>
    
    <!-- 函数调用卡片 -->
    <view v-if="functionCalls && functionCalls.length > 0" class="function-cards">
      <function-card 
        v-for="(call, index) in functionCalls" 
        :key="index"
        :function-name="call.name"
        :parameters="call.parameters"
      ></function-card>
    </view>
  </view>
</template>

<script>
import FunctionCard from './FunctionCard.vue';

export default {
  // 现有代码保持不变
  components: {
    FunctionCard
  },
  props: {
    // 现有props保持不变
    functionCalls: {
      type: Array,
      default: () => []
    }
  }
}
</script>
```

### 6. 修改chat.vue中的消息处理

最后，修改chat.vue中的消息处理逻辑：

```javascript
// 在chat.vue中修改handleSendMessage方法
handleSendMessage(message) {
  // 现有代码保持不变
  
  // 使用聊天服务发送消息
  this.chatService.sendMessage(
    message,
    // 成功回调
    (response) => {
      // 隐藏正在输入指示器
      this.isTyping = false;

      // 创建一个新的消息对象
      const newMessage = {
        isUser: false,
        message: response.text,
        parsedMessage: this.parseMarkdown(response.text),
        time: this.formatTime(new Date()),
        functionCalls: response.functionCalls || []
      };

      // 添加到聊天列表
      this.chatList.push(newMessage);

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    // 失败回调保持不变
  );
}
```

## 优势与考虑事项

### 优势

1. **结构化响应**：Function Calling提供了结构化的响应格式，便于前端处理和渲染
2. **灵活性**：可以定义多种不同的函数，满足各种交互需求
3. **可扩展性**：随着需求的增长，可以轻松添加新的函数类型
4. **语义清晰**：模型能够准确理解何时应该调用特定函数，生成合适的参数

### 考虑事项

1. **后端支持**：需要确保使用的大模型（如DeepSeek）支持Function Calling功能
2. **参数准确性**：需要处理模型可能生成的不准确参数
3. **回退机制**：当无法获取准确参数时，应提供回退机制（如搜索功能）

## 实施步骤

1. 确认使用的大模型API是否支持Function Calling
2. 在后端实现函数定义和处理逻辑
3. 创建FunctionCard组件
4. 修改ChatMessage组件以支持显示函数调用卡片
5. 更新chat.vue中的消息处理逻辑
6. 测试并优化交互体验

## 扩展应用场景

这个方案不仅可以满足显示景点在地图上的位置的需求，还可以扩展到其他场景：

1. **门票预订**：提供景点门票预订链接
2. **路线规划**：规划从当前位置到景点的路线
3. **营业时间查询**：显示景点的营业时间信息
4. **活动推荐**：推荐景区内的特色活动
5. **美食推荐**：推荐景区周边的特色美食

通过Function Calling，可以为用户提供更加丰富和便捷的交互体验，提升小程序的实用性和用户满意度。
