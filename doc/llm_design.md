# LLM调用设计文档

## 背景

当前的模型调用设计采用了工厂模式和不同的实现类来处理不同服务商的API调用，这种设计相对传统，存在以下问题：

1. **耦合性高**：每个服务商都需要一个独立的实现类，导致代码冗余
2. **扩展性差**：添加新的服务商需要创建新的类并修改工厂类
3. **配置分散**：模型、服务商和API密钥的配置分散在不同的地方
4. **不符合现代趋势**：现代LLM应用通常采用更灵活的配置方式

## 设计目标

设计一个更现代化的LLM调用系统，采用"模型服务商、模型、API密钥"的方式来组织代码，具有以下特点：

1. **解耦合**：将服务商、模型和API密钥解耦，使它们可以独立配置
2. **易扩展**：添加新的服务商或模型只需添加配置，无需修改代码
3. **统一接口**：提供统一的接口，简化调用方式
4. **配置集中**：集中管理所有配置，便于维护
5. **符合现代趋势**：采用类似于OpenAI、Anthropic等现代LLM服务的调用方式

## 系统架构

### 1. LLM配置模型

创建一个集中的配置模型，包含以下内容：

```php
[
    'providers' => [
        'deepseek' => [
            'name' => 'DeepSeek',
            'base_url' => 'https://api.deepseek.com/v1',
            'api_key' => 'your_api_key',
            'models' => [
                'deepseek-reasoner' => [
                    'name' => 'DeepSeek Reasoner',
                    'supports_functions' => true,
                    'max_tokens' => 4096
                ],
                'deepseek-chat' => [
                    'name' => 'DeepSeek Chat',
                    'supports_functions' => false,
                    'max_tokens' => 4096
                ]
            ]
        ],
        'baidu' => [
            'name' => '百度文心',
            'base_url' => 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop',
            'api_key' => 'your_api_key',
            'secret_key' => 'your_secret_key',
            'models' => [
                'ernie-bot-4' => [
                    'name' => '文心一言4.0',
                    'supports_functions' => true,
                    'max_tokens' => 4096
                ],
                'ernie-bot' => [
                    'name' => '文心一言',
                    'supports_functions' => false,
                    'max_tokens' => 2048
                ]
            ]
        ],
        // 其他服务商...
    ],
    'default_provider' => 'deepseek',
    'default_model' => 'deepseek-reasoner'
]
```

### 2. LLM客户端类

创建一个统一的LLM客户端类，负责处理所有服务商的API调用：

```php
class LLMClient
{
    protected $config;
    protected $provider;
    protected $model;
    
    public function __construct($provider = null, $model = null)
    {
        $this->config = $this->loadConfig();
        $this->provider = $provider ?? $this->config['default_provider'];
        $this->model = $model ?? $this->config['default_model'];
    }
    
    public function chatCompletion($messages, $options = [])
    {
        $providerConfig = $this->getProviderConfig();
        $modelConfig = $this->getModelConfig();
        
        // 根据服务商和模型调用相应的API
        $method = 'call' . ucfirst($this->provider) . 'API';
        if (method_exists($this, $method)) {
            return $this->$method($messages, $options);
        }
        
        throw new \Exception("不支持的服务商: {$this->provider}");
    }
    
    // 各服务商的API调用方法...
}
```

### 3. 函数调用支持

为支持函数调用的模型提供统一的接口：

```php
public function chatCompletionWithFunctions($messages, $functions, $options = [])
{
    $modelConfig = $this->getModelConfig();
    if (!$modelConfig['supports_functions']) {
        throw new \Exception("模型 {$this->model} 不支持函数调用");
    }
    
    $options['functions'] = $functions;
    return $this->chatCompletion($messages, $options);
}
```

## 实现步骤

1. 创建LLM配置模型和管理界面
2. 实现LLM客户端类
3. 为各服务商实现API调用方法
4. 修改现有代码，使用新的LLM客户端
5. 添加单元测试

## 配置界面设计

在后台添加LLM配置界面，包含以下内容：

1. 服务商管理：添加、编辑、删除服务商
2. 模型管理：为每个服务商添加、编辑、删除模型
3. API密钥管理：配置各服务商的API密钥
4. 默认设置：设置默认服务商和默认模型

## 迁移计划

1. 创建新的LLM客户端类和配置模型
2. 实现各服务商的API调用方法
3. 在不影响现有功能的情况下，逐步替换现有代码
4. 添加单元测试，确保功能正常
5. 完全迁移后，移除旧的实现类

## 预期效果

1. 代码更加简洁、易于维护
2. 添加新的服务商或模型更加简单
3. 配置更加集中，易于管理
4. 调用方式更加统一，符合现代LLM应用的趋势
