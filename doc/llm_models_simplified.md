# LLM模型管理简化实现方案

## 概述

本文档描述了LLM模型管理的简化实现方案，采用扁平化的模型结构，直接使用模型配置，不需要复杂的组合结构。

## 数据库设计

### 表结构

使用独立的数据库表`fa_ds_llm_models`来管理LLM模型配置，表结构如下：

```sql
CREATE TABLE IF NOT EXISTS `fa_ds_llm_models` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `model_id` varchar(50) NOT NULL COMMENT '模型ID',
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `provider` varchar(50) NOT NULL COMMENT '服务商标识',
  `provider_name` varchar(100) NOT NULL COMMENT '服务商名称',
  `base_url` varchar(255) NOT NULL COMMENT 'API基础URL',
  `api_key` varchar(255) DEFAULT '' COMMENT 'API密钥',
  `supports_functions` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持函数调用',
  `max_tokens` int(11) NOT NULL DEFAULT 4096 COMMENT '最大token数',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认模型',
  `weight` int(10) unsigned DEFAULT 0 COMMENT '排序权重',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `model_id` (`model_id`),
  KEY `provider` (`provider`),
  KEY `enabled` (`enabled`),
  KEY `is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LLM模型配置表';
```

### 默认数据

```sql
INSERT INTO `fa_ds_llm_models` (`model_id`, `name`, `provider`, `provider_name`, `base_url`, `api_key`, `supports_functions`, `max_tokens`, `enabled`, `is_default`, `weight`, `createtime`, `updatetime`) VALUES
('deepseek-reasoner', 'DeepSeek Reasoner', 'deepseek', 'DeepSeek', 'https://api.deepseek.com/v1', '', 1, 4096, 1, 1, 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('deepseek-chat', 'DeepSeek Chat', 'deepseek', 'DeepSeek', 'https://api.deepseek.com/v1', '', 0, 4096, 1, 0, 90, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
```

## 类设计

### LLMModelManager

`LLMModelManager`类负责管理LLM模型配置，提供以下功能：

- 获取所有模型
- 获取指定模型
- 获取默认模型
- 按服务商获取模型
- 添加或更新模型
- 设置默认模型
- 删除模型
- 清除缓存

### LLMClient

`LLMClient`类负责调用LLM API，提供以下功能：

- 聊天补全
- 带函数调用的聊天补全
- 调用DeepSeek API
- 处理DeepSeek函数调用结果

## 简化设计

### 1. 扁平化模型结构

不再使用复杂的嵌套结构（如providers->models），而是直接使用扁平化的模型结构，一条记录就是一个具体的模型。

### 2. 直接使用模型配置

`LLMClient`类直接使用`LLMModelManager`获取模型配置，不需要中间层的转换。

### 3. 移除兼容层

移除`LLMConfig`类，不再需要兼容旧代码。

## API调用流程

### 基本调用流程

1. 创建LLMClient实例
2. 调用chatCompletion方法
3. 处理返回结果

```php
// 创建LLMClient实例
$client = new LLMClient();

// 调用chatCompletion方法
$messages = [
    ['role' => 'system', 'content' => '你是一个助手'],
    ['role' => 'user', 'content' => '你好']
];
$result = $client->chatCompletion($messages);

// 处理返回结果
echo $result;
```

### 带函数调用的调用流程

1. 创建LLMClient实例
2. 准备函数定义
3. 调用chatCompletionWithFunctions方法
4. 处理返回结果

```php
// 创建LLMClient实例
$client = new LLMClient('deepseek-reasoner');

// 准备函数定义
$functions = [
    [
        'name' => 'get_weather',
        'description' => '获取指定城市的天气信息',
        'parameters' => [
            'type' => 'object',
            'properties' => [
                'city' => [
                    'type' => 'string',
                    'description' => '城市名称'
                ]
            ],
            'required' => ['city']
        ]
    ]
];

// 调用chatCompletionWithFunctions方法
$messages = [
    ['role' => 'system', 'content' => '你是一个助手'],
    ['role' => 'user', 'content' => '北京今天天气怎么样？']
];
$result = $client->chatCompletionWithFunctions($messages, $functions);

// 处理返回结果
$content = $result['content'];
$functionCalls = $result['function_calls'];

echo "回答内容: $content\n";
echo "函数调用: " . json_encode($functionCalls, JSON_UNESCAPED_UNICODE) . "\n";
```

## 缓存机制

为了提高性能，LLMModelManager使用缓存机制，缓存时间为3600秒（1小时）。缓存的内容包括：

- 所有模型列表
- 指定模型信息
- 默认模型信息
- 按服务商的模型列表

当模型配置发生变化时，会自动清除相关缓存。

## 代码示例

### LLMClient.php

```php
<?php

namespace addons\dsassistant\library\llm;

use think\Log;

/**
 * LLM客户端类
 *
 * 统一处理各种LLM服务商的API调用
 */
class LLMClient
{
    /**
     * 当前模型信息
     * @var array
     */
    protected $model;

    /**
     * 构造函数
     *
     * @param string|null $modelId 模型ID，如果为null则使用默认模型
     * @throws \Exception 如果模型不存在
     */
    public function __construct($modelId = null)
    {
        // 获取模型信息
        if ($modelId) {
            $this->model = LLMModelManager::getModel($modelId);
            if (!$this->model) {
                throw new \Exception("模型不存在: {$modelId}");
            }
        } else {
            $this->model = LLMModelManager::getDefaultModel();
            if (!$this->model) {
                throw new \Exception("没有可用的默认模型");
            }
        }
    }

    /**
     * 聊天补全
     *
     * @param array $messages 消息数组
     * @param array $options 选项
     * @return string|array 生成的回答
     * @throws \Exception 如果API调用失败
     */
    public function chatCompletion($messages, $options = [])
    {
        // 根据服务商调用相应的API
        $provider = $this->model['provider'];
        $method = 'call' . ucfirst($provider) . 'API';
        if (method_exists($this, $method)) {
            return $this->$method($messages, $options);
        }

        throw new \Exception("不支持的服务商: {$provider}");
    }
}
```

## 注意事项

1. 模型配置的API密钥应该妥善保管，避免泄露
2. 当模型配置发生变化时，会自动清除相关缓存，无需手动清除
3. 使用`LLMClient`类时，只需要指定模型ID，不需要指定服务商
