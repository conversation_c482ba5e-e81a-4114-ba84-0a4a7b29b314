<?php

return [
    // [
    //     //配置唯一标识
    //     'name'    => 'embedding_provider',
    //     //显示的标题
    //     'title'   => '向量嵌入服务提供商',
    //     //类型
    //     'type'    => 'select',
    //     //分组
    //     'group'   => '向量搜索配置',
    //     //动态显示
    //     'visible' => '',
    //     //数据字典
    //     'content' => [
    //         'baidu' => '百度文心大模型',
    //         'xunfei' => '讯飞星火大模型',
    //         'ali' => '阿里云通义千问',
    //         'tencent' => '腾讯云混元大模型',
    //         'huawei' => '华为云盘古大模型'
    //     ],
    //     //值
    //     'value'   => 'tencent',
    //     //验证规则
    //     'rule'    => 'required',
    //     //错误消息
    //     'msg'     => '请选择向量嵌入服务提供商',
    //     //提示消息
    //     'tip'     => '选择用于生成向量嵌入的服务提供商',
    //     //成功消息
    //     'ok'      => '',
    //     //扩展信息
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'embedding_backup_provider',
    //     'title'   => '备用向量嵌入服务提供商',
    //     'type'    => 'select',
    //     'group'   => '向量搜索配置',
    //     'content' => [
    //         'baidu' => '百度文心大模型',
    //         'xunfei' => '讯飞星火大模型',
    //         'ali' => '阿里云通义千问',
    //         'tencent' => '腾讯云混元大模型',
    //         'huawei' => '华为云盘古大模型'
    //     ],
    //     'value'   => 'ali',
    //     'rule'    => 'required',
    //     'msg'     => '请选择备用向量嵌入服务提供商',
    //     'tip'     => '当主要服务提供商不可用时，使用备用服务提供商',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'baidu_api_key',
    //     'title'   => '百度API密钥',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     //'visible' => 'embedding_provider == \'baidu\' || embedding_backup_provider == \'baidu\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写百度API密钥',
    //     'tip'     => '百度文心大模型API密钥',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'baidu_secret_key',
    //     'title'   => '百度密钥',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     //'visible' => 'embedding_provider == \'baidu\' || embedding_backup_provider == \'baidu\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写百度密钥',
    //     'tip'     => '百度文心大模型密钥',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'xunfei_app_id',
    //     'title'   => '讯飞应用ID',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     //'visible' => 'embedding_provider == \'xunfei\' || embedding_backup_provider == \'xunfei\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写讯飞应用ID',
    //     'tip'     => '讯飞星火大模型应用ID',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'xunfei_api_key',
    //     'title'   => '讯飞API密钥',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     //'visible' => 'embedding_provider == \'xunfei\' || embedding_backup_provider == \'xunfei\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写讯飞API密钥',
    //     'tip'     => '讯飞星火大模型API密钥',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'xunfei_api_secret',
    //     'title'   => '讯飞API密钥',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     //'visible' => 'embedding_provider == \'xunfei\' || embedding_backup_provider == \'xunfei\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写讯飞API密钥',
    //     'tip'     => '讯飞星火大模型API密钥',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'ali_api_key',
    //     'title'   => '阿里API密钥',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     //'visible' => 'embedding_provider == \'ali\' || embedding_backup_provider == \'ali\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写阿里API密钥',
    //     'tip'     => '阿里云通义千问API密钥',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'tencent_secret_id',
    //     'title'   => '腾讯云SecretId',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //    // 'visible' => 'embedding_provider == \'tencent\' || embedding_backup_provider == \'tencent\'',
    //     'content' => [],
    //     'value'   => 'AKIDeT7cw75pZ5OUN5as0xjIenlX6SbgYDgx',
    //     'rule'    => '',
    //     'msg'     => '请填写腾讯云SecretId',
    //     'tip'     => '腾讯云API密钥ID',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'tencent_secret_key',
    //     'title'   => '腾讯云SecretKey',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //    // 'visible' => 'embedding_provider == \'tencent\' || embedding_backup_provider == \'tencent\'',
    //     'content' => [],
    //     'value'   => '8mHZhcLtHSRHuO3cAe4TxRnwsc8RWR9f',
    //     'rule'    => '',
    //     'msg'     => '请填写腾讯云SecretKey',
    //     'tip'     => '腾讯云API密钥',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'tencent_region',
    //     'title'   => '腾讯云区域',
    //     'type'    => 'select',
    //     'group'   => '向量搜索配置',
    //     'visible' => 'embedding_provider == \'tencent\' || embedding_backup_provider == \'tencent\'',
    //     'content' => [
    //         'ap-guangzhou' => '广州',
    //         'ap-shanghai' => '上海',
    //         'ap-beijing' => '北京',
    //         'ap-nanjing' => '南京',
    //         'ap-hongkong' => '香港'
    //     ],
    //     'value'   => 'ap-guangzhou',
    //     'rule'    => '',
    //     'msg'     => '请选择腾讯云区域',
    //     'tip'     => '腾讯云API区域',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'huawei_ak',
    //     'title'   => '华为云AK',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     'visible' => 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写华为云AK',
    //     'tip'     => '华为云API密钥ID',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'huawei_sk',
    //     'title'   => '华为云SK',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     'visible' => 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写华为云SK',
    //     'tip'     => '华为云API密钥',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'huawei_project_id',
    //     'title'   => '华为云项目ID',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     'visible' => 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写华为云项目ID',
    //     'tip'     => '华为云项目ID',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'huawei_region',
    //     'title'   => '华为云区域',
    //     'type'    => 'select',
    //     'group'   => '向量搜索配置',
    //     'visible' => 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'',
    //     'content' => [
    //         'cn-north-4' => '华北-北京四',
    //         'cn-north-1' => '华北-北京一',
    //         'cn-east-3' => '华东-上海一',
    //         'cn-south-1' => '华南-广州',
    //         'ap-southeast-1' => '中国-香港'
    //     ],
    //     'value'   => 'cn-north-4',
    //     'rule'    => '',
    //     'msg'     => '请选择华为云区域',
    //     'tip'     => '华为云API区域',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'huawei_embedding_model',
    //     'title'   => '华为云嵌入模型',
    //     'type'    => 'string',
    //     'group'   => '向量搜索配置',
    //     'visible' => 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'',
    //     'content' => [],
    //     'value'   => 'pangu-embedding',
    //     'rule'    => '',
    //     'msg'     => '请填写华为云嵌入模型名称',
    //     'tip'     => '华为云盘古嵌入模型名称',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     //配置唯一标识
    //     'name'    => 'deepseek_provider',
    //     //显示的标题
    //     'title'   => 'DeepSeek提供商',
    //     //类型
    //     'type'    => 'select',
    //     //分组
    //     'group'   => 'DeepSeek配置',
    //     //动态显示
    //     'visible' => '',
    //     //数据字典
    //     'content' => [
    //         'official' => 'DeepSeek官方',
    //         'tencent' => '腾讯云DeepSeek',
    //         'baidu' => '百度智能云DeepSeek',
    //         'ali' => '阿里云DeepSeek',
    //         'huawei' => '华为云DeepSeek'
    //     ],
    //     //值
    //     'value'   => 'official',
    //     //验证规则
    //     'rule'    => 'required',
    //     //错误消息
    //     'msg'     => '请选择DeepSeek提供商',
    //     //提示消息
    //     'tip'     => '选择DeepSeek API的提供商',
    //     //成功消息
    //     'ok'      => '',
    //     //扩展信息
    //     'extend'  => ''
    // ],
    // [
    //     //配置唯一标识
    //     'name'    => 'deepseek_api_key',
    //     //显示的标题
    //     'title'   => 'DeepSeek API密钥',
    //     //类型
    //     'type'    => 'string',
    //     //分组
    //     'group'   => 'DeepSeek配置',
    //     //动态显示
    //     'visible' => 'deepseek_provider == \'official\'',
    //     //数据字典
    //     'content' => [],
    //     //值
    //     'value'   => 'sk-3b220dab9e39449aa305948cb8d2470e',
    //     //验证规则
    //     'rule'    => 'required',
    //     //错误消息
    //     'msg'     => '请填写DeepSeek API密钥',
    //     //提示消息
    //     'tip'     => '请在DeepSeek官网申请API密钥',
    //     //成功消息
    //     'ok'      => '',
    //     //扩展信息
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'deepseek_model',
    //     'title'   => 'DeepSeek模型',
    //     'type'    => 'select',
    //     'group'   => 'DeepSeek配置',
    //     'content' => [
    //         'deepseek-chat' => 'deepseek-chat',
    //         'deepseek-chat-pro' => 'deepseek-chat-pro'
    //     ],
    //     'value'   => 'deepseek-chat',
    //     'rule'    => 'required',
    //     'msg'     => '请选择DeepSeek模型',
    //     'tip'     => '选择使用的DeepSeek模型',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'system_prompt',
    //     'title'   => '系统提示词',
    //     'type'    => 'text',
    //     'group'   => 'DeepSeek配置',
    //     'content' => [],
    //     'value'   => '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。',
    //     'rule'    => 'required',
    //     'msg'     => '请填写系统提示词',
    //     'tip'     => '设置AI助手的角色定位和行为指南',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'cache_time',
    //     'title'   => '缓存时间(秒)',
    //     'type'    => 'number',
    //     'group'   => '性能配置',
    //     'content' => [],
    //     'value'   => '3600',
    //     'rule'    => 'required',
    //     'msg'     => '请填写缓存时间',
    //     'tip'     => '设置问答结果缓存时间，单位：秒',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'confidence_threshold',
    //     'title'   => '置信度阈值',
    //     'type'    => 'number',
    //     'group'   => '性能配置',
    //     'content' => [],
    //     'value'   => '70',
    //     'rule'    => 'required',
    //     'msg'     => '请填写置信度阈值',
    //     'tip'     => '设置AI回答的置信度阈值，低于此值将转人工客服',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'vectordb_type',
    //     'title'   => '向量数据库类型',
    //     'type'    => 'select',
    //     'group'   => '向量数据库配置',
    //     'content' => [
    //         'mysql' => 'MySQL (内置)',
    //         'tencent' => '腾讯云 VectorDB',
    //         'aliyun' => '阿里云 VectorSearch',
    //         'baidu' => '百度智能云 VectorDB',
    //         'huawei' => '华为云 VSS'
    //     ],
    //     'value'   => 'mysql',
    //     'rule'    => 'required',
    //     'msg'     => '请选择向量数据库类型',
    //     'tip'     => '选择用于存储和检索向量的数据库类型',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'vector_search_type',
    //     'title'   => '向量搜索实现',
    //     'type'    => 'select',
    //     'group'   => '向量搜索配置',
    //     'content' => [
    //         'enhanced' => '进阶版 (向量数据库)',
    //         'jieba' => '高级版 (Jieba分词+向量数据库)'
    //     ],
    //     'value'   => 'optimized',
    //     'rule'    => 'required',
    //     'msg'     => '请选择向量搜索实现',
    //     'tip'     => '选择向量搜索的实现方式，高级版需要Jieba分词服务支持',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'wechat_appid',
    //     'title'   => '微信公众号AppID',
    //     'type'    => 'string',
    //     'group'   => '微信配置',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写微信公众号AppID',
    //     'tip'     => '微信公众号的AppID',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'wechat_appsecret',
    //     'title'   => '微信公众号AppSecret',
    //     'type'    => 'string',
    //     'group'   => '微信配置',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写微信公众号AppSecret',
    //     'tip'     => '微信公众号的AppSecret',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'wechat_token',
    //     'title'   => '微信公众号Token',
    //     'type'    => 'string',
    //     'group'   => '微信配置',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写微信公众号Token',
    //     'tip'     => '微信公众号的Token',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'tencent_map_key',
    //     'title'   => '腾讯地图Key',
    //     'type'    => 'string',
    //     'group'   => '地图配置',
    //     'content' => [],
    //     'value'   => '',
    //     'rule'    => '',
    //     'msg'     => '请填写腾讯地图Key',
    //     'tip'     => '腾讯地图开发者Key，用于网页和小程序地图显示',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'customer_service_phone',
    //     'title'   => '客服电话',
    //     'type'    => 'string',
    //     'group'   => '基础配置',
    //     'content' => [],
    //     'value'   => '************',
    //     'rule'    => '',
    //     'msg'     => '请填写客服电话',
    //     'tip'     => '当AI无法回答问题时显示的客服电话',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    // [
    //     'name'    => 'rag_config',
    //     'title'   => 'RAG模式配置',
    //     'type'    => 'array',
    //     'group'   => 'RAG模式设置',
    //     'content' => [],
    //     'value'   => [
    //         'enabled' => 1,                 // 是否启用RAG模式
    //         'top_k' => 3,                   // 从本地知识库检索的结果数量
    //         'min_score' => 0.3,             // 本地知识库结果的最低得分要求
    //         'context_format' => "问题: {question}\n回答: {answer}\n\n", // 上下文格式
    //         'prompt_template' => "以下是关于用户问题的相关信息，请参考这些信息回答问题，但不要直接复制这些内容，而是用自然、流畅的语言回答：\n\n{context}" // 提示模板
    //     ],
    //     'rule'    => '',
    //     'msg'     => '',
    //     'tip'     => '配置RAG模式的行为，包括是否启用、检索结果数量等',
    //     'ok'      => '',
    //     'extend'  => ''
    // ],
    [
        'name' => 'enable_functions',
        'title' => '启用函数调用',
        'type' => 'switch',
        'group' => 'function_calling',
        'content' => [],
        'value' => 0,
        'rule' => '',
        'msg' => '',
        'tip' => '启用后，AI助理可以调用函数，如显示地图位置、景点详情等',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'enabled_functions',
        'title' => '已启用的函数',
        'type' => 'hidden',
        'group' => 'function_calling',
        'content' => [],
        'value' => '{}',
        'rule' => '',
        'msg' => '',
        'tip' => '存储已启用的函数列表，由系统自动管理',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'function_calling_prompt',
        'title' => 'Function Calling提示词',
        'type' => 'textarea',
        'group' => 'function_calling',
        'content' => [],
        'value' => "重要提示：当你需要调用函数时，请同时提供文字说明。即使你调用了函数，也要用自然语言向用户解释你正在做什么或提供相关信息。不要只返回函数调用而不提供任何文字回复。例如：\n- 如果调用景点详情函数，请说明你正在为用户查询该景点的详细信息\n- 如果调用地图函数，请告诉用户你正在为他们显示位置信息\n- 始终保持友好和专业的语气，让用户感受到你的帮助",
        'rule' => '',
        'msg' => '',
        'tip' => '当启用Function Calling时，会将此提示词附加到系统提示词中，确保AI同时提供文本说明和函数调用',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'group',
        'title' => '参数组',
        'type' => 'array',
        'content' => [],
        'value' => [
            'basic' => '基础',
            'contact' => '联系方式',
            'deepseek' => 'DeepSeek配置',
            'vector_search' => '向量搜索配置',
            'vectordb' => '向量数据库配置',
            'rag_model' => 'RAG模式设置',
            'map' => '地图配置',
            'wechat_mp' => '微信公众号',
            'wechat_xcx' => '微信小程序',
            'performance' => '性能配置',
            'function_calling' => '函数调用'
        ],
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
];
