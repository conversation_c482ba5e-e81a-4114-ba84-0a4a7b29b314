<?php

namespace addons\dsassistant\controller;

use addons\dsassistant\library\Wechat as WechatLib;
use app\admin\model\dsassistant\Userwechat;
use app\common\model\DsAssistant;
use think\addons\Controller;
use think\Db;
use think\Log;

/**
 * 微信公众号控制器
 */
class Wechat extends Controller
{
    protected $wechat = null;
    protected $assistant = null;

    public function _initialize()
    {
        parent::_initialize();
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $config = [
            'appid' => $config['wechat_appid'] ?? '',
            'appsecret' => $config['wechat_appsecret'] ?? '',
            'token' => $config['wechat_token'] ?? ''
        ];

        $this->wechat = new WechatLib($config);
        $this->assistant = new DsAssistant();
    }

    /**
     * 微信服务器验证接口
     */
    public function index()
    {
        $echoStr = $this->request->param('echostr');
        if ($this->wechat->checkSignature()) {
            echo $echoStr;
            exit;
        }
    }

    /**
     * 处理微信消息
     */
    public function message()
    {
        if (!$this->wechat->checkSignature()) {
            echo 'Invalid signature';
            exit;
        }

        $postStr = file_get_contents('php://input');
        if (empty($postStr)) {
            echo '';
            exit;
        }

        try {
            $message = $this->wechat->parseMessage($postStr);

            // 处理不同类型的消息
            switch ($message['MsgType']) {
                case 'text':
                    $response = $this->handleTextMessage($message);
                    break;
                case 'event':
                    $response = $this->handleEventMessage($message);
                    break;
                default:
                    $response = $this->wechat->buildTextMessage($message['FromUserName'], '暂不支持此类型消息');
            }

            echo $response;
        } catch (\Exception $e) {
            Log::error('处理微信消息出错: ' . $e->getMessage());
            echo '';
        }
    }

    /**
     * 处理文本消息
     */
    protected function handleTextMessage($message)
    {
        $question = $message['Content'];
        $openid = $message['FromUserName'];
        $sessionId = md5($openid . date('Ymd'));
        $ip = $this->request->ip();

        // 检查是否是菜单选择
        if (preg_match('/^[1-9]\d*$/', $question)) {
            return $this->handleMenuSelection($openid, $question);
        }

        // 检查是否是重置会话的命令
        if ($question === '/reset' || $question === '重置会话') {
            // 重置会话
            \addons\dsassistant\library\SessionManager::closeSession($sessionId);
            return $this->wechat->buildTextMessage($openid, '会话已重置，我们可以开始新的对话了。');
        }

        // 调用DeepSeek API获取回答和函数调用
        $response = $this->assistant->handleQuestion($question, $openid, $sessionId, 'wechat', $ip);

        // 如果返回的是字符串，说明没有启用函数调用
        if (is_string($response)) {
            return $this->wechat->buildTextMessage($openid, $response);
        }

        // 提取文本回答和函数调用
        $answer = $response['answer'];
        $functionCalls = $response['functionCalls'] ?? [];

        // 如果没有函数调用，直接返回文本回答
        if (empty($functionCalls)) {
            return $this->wechat->buildTextMessage($openid, $answer);
        }

        // 如果只有一个函数调用，直接返回图文消息
        if (count($functionCalls) == 1) {
            return $this->generateNewsForFunction($answer, $functionCalls[0], $openid);
        }

        // 如果有多个函数调用，保存到会话中并提供选择菜单
        $this->saveActionsToSession($openid, $functionCalls);

        // 构建回复消息，包含文本回答和选择菜单
        $reply = $answer . "\n\n您可以选择以下操作：\n";
        foreach ($functionCalls as $index => $call) {
            $reply .= ($index + 1) . ". " . $this->getFunctionTitle($call) . "\n";
        }

        return $this->wechat->buildTextMessage($openid, $reply);
    }

    /**
     * 处理事件消息
     */
    protected function handleEventMessage($message)
    {
        $event = strtolower($message['Event']);

        switch ($event) {
            case 'subscribe':
                // 用户关注，保存或更新用户信息
                $openid = $message['FromUserName'];
                $this->saveUserInfo($openid);

                return $this->wechat->buildTextMessage($message['FromUserName'], '欢迎关注景区智能助手！我可以为您提供景区信息、导览服务、预警通知等。请问有什么可以帮助您的？');

            case 'unsubscribe':
                // 用户取消关注，更新用户状态
                $openid = $message['FromUserName'];
                Userwechat::where('openid', $openid)->update([
                    'subscribe' => 0,
                    'updatetime' => time()
                ]);

                // 同时从位置池中移除
                Db::name('ds_user_location_pool')->where('openid', $openid)->delete();

                return '';

            case 'location':
                // 处理位置上报事件
                return $this->handleLocationMessage($message);

            case 'click':
                return $this->handleMenuClick($message);

            default:
                return $this->wechat->buildTextMessage($message['FromUserName'], '感谢您的关注！');
        }
    }

    /**
     * 保存用户信息
     */
    protected function saveUserInfo($openid)
    {
        // 获取用户信息
        try {
            $userInfo = $this->wechat->getUserInfo($openid);

            if ($userInfo) {
                // 查询用户是否存在
                $user = Userwechat::where('openid', $openid)->find();

                $data = [
                    'openid' => $openid,
                    'nickname' => $userInfo['nickname'] ?? '',
                    'subscribe' => 1,
                    'subscribe_time' => time(),
                    'updatetime' => time()
                ];

                if ($user) {
                    // 更新用户信息
                    Userwechat::where('openid', $openid)->update($data);
                } else {
                    // 新增用户信息
                    $data['createtime'] = time();
                    Userwechat::insert($data);
                }
            }
        } catch (\Exception $e) {
            Log::error('保存微信用户信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理用户位置上报
     */
    protected function handleLocationMessage($message)
    {
        $openid = $message['FromUserName'];
        $latitude = $message['Latitude'];
        $longitude = $message['Longitude'];
        $precision = $message['Precision'] ?? 0;

        // 获取用户信息
        $user = Userwechat::where('openid', $openid)->find();

        $nickname = $user ? $user['nickname'] : '';

        // 判断用户是否在景区内
        $inScenic = $this->isUserInScenicArea($latitude, $longitude);

        // 计算过期时间（12小时后）
        $expireTime = time() + 12 * 3600;

        // 更新或插入用户位置信息
        $locationData = [
            'openid' => $openid,
            'nickname' => $nickname,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'in_scenic' => $inScenic ? 1 : 0,
            'last_report_time' => time(),
            'expire_time' => $expireTime,
            'updatetime' => time()
        ];

        $existUser = Db::name('ds_user_location_pool')
            ->where('openid', $openid)
            ->find();

        if ($existUser) {
            // 更新现有记录
            Db::name('ds_user_location_pool')
                ->where('openid', $openid)
                ->update($locationData);
        } else {
            // 插入新记录
            $locationData['createtime'] = time();
            Db::name('ds_user_location_pool')->insert($locationData);
        }

        // 如果用户在景区内，可以返回一些景区信息
        if ($inScenic) {
            return $this->wechat->buildTextMessage($openid, "您好，欢迎来到我们的景区！\n\n您可以通过菜单查看景区导览、预警信息等服务。");
        } else {
            // 用户不在景区，可以不回复或回复其他信息
            return '';
        }
    }

    /**
     * 判断用户是否在景区内
     */
    protected function isUserInScenicArea($latitude, $longitude)
    {
        // 获取所有景区边界
        $boundaries = Db::name('ds_scenic_area_boundary')
            ->where('status', 'normal')
            ->select();

        if (empty($boundaries)) {
            return false;
        }

        foreach ($boundaries as $boundary) {
            switch ($boundary['type']) {
                case 'circle':
                    // 圆形区域判断
                    if ($this->isPointInCircle(
                        $latitude,
                        $longitude,
                        $boundary['center_lat'],
                        $boundary['center_lng'],
                        $boundary['radius']
                    )) {
                        return true;
                    }
                    break;

                case 'polygon':
                    // 多边形区域判断
                    $points = json_decode($boundary['points'], true);
                    if ($points && $this->isPointInPolygon($latitude, $longitude, $points)) {
                        return true;
                    }
                    break;

                case 'rectangle':
                    // 矩形区域判断
                    $points = json_decode($boundary['points'], true);
                    if ($points && count($points) >= 2) {
                        $minLat = min($points[0]['lat'], $points[1]['lat']);
                        $maxLat = max($points[0]['lat'], $points[1]['lat']);
                        $minLng = min($points[0]['lng'], $points[1]['lng']);
                        $maxLng = max($points[0]['lng'], $points[1]['lng']);

                        if ($latitude >= $minLat && $latitude <= $maxLat &&
                            $longitude >= $minLng && $longitude <= $maxLng) {
                            return true;
                        }
                    }
                    break;
            }
        }

        return false;
    }

    /**
     * 判断点是否在圆内
     */
    protected function isPointInCircle($lat, $lng, $centerLat, $centerLng, $radius)
    {
        // 地球半径，单位米
        $earthRadius = 6378137;

        // 将经纬度转换为弧度
        $latRad = deg2rad($lat);
        $lngRad = deg2rad($lng);
        $centerLatRad = deg2rad($centerLat);
        $centerLngRad = deg2rad($centerLng);

        // 计算两点之间的距离
        $distance = $earthRadius * acos(
            sin($latRad) * sin($centerLatRad) +
            cos($latRad) * cos($centerLatRad) * cos($lngRad - $centerLngRad)
        );

        // 判断距离是否小于半径
        return $distance <= $radius;
    }

    /**
     * 判断点是否在多边形内
     */
    protected function isPointInPolygon($lat, $lng, $points)
    {
        $count = count($points);
        if ($count < 3) {
            return false;
        }

        $inside = false;
        for ($i = 0, $j = $count - 1; $i < $count; $j = $i++) {
            $xi = $points[$i]['lng'];
            $yi = $points[$i]['lat'];
            $xj = $points[$j]['lng'];
            $yj = $points[$j]['lat'];

            $intersect = (($yi > $lat) != ($yj > $lat)) &&
                         ($lng < ($xj - $xi) * ($lat - $yi) / ($yj - $yi) + $xi);

            if ($intersect) {
                $inside = !$inside;
            }
        }

        return $inside;
    }

    /**
     * 处理菜单点击事件
     */
    protected function handleMenuClick($message)
    {
        $key = $message['EventKey'];

        switch ($key) {
            case 'MENU_GUIDE':
                return $this->wechat->buildTextMessage($message['FromUserName'], '景区导览功能已启动，您可以直接询问我景点相关信息，例如"介绍一下XX景点"、"XX景点怎么走"等。');

            case 'MENU_TICKET':
                return $this->wechat->buildTextMessage($message['FromUserName'], '门票信息查询已启动，您可以直接询问我门票相关信息，例如"门票多少钱"、"有什么优惠政策"等。');

            case 'MENU_LOST_FOUND':
                return $this->wechat->buildTextMessage($message['FromUserName'], '失物招领功能已启动，您可以直接询问我失物招领相关信息，例如"我丢了XX物品"、"我捡到了XX物品"等。');

            default:
                return $this->wechat->buildTextMessage($message['FromUserName'], '您点击了未知菜单，请重试或直接输入您的问题。');
        }
    }

    /**
     * 处理菜单选择
     */
    protected function handleMenuSelection($openid, $selection)
    {
        // 从会话中获取保存的函数调用
        $functionCalls = $this->getActionsFromSession($openid);

        // 检查选择是否有效
        $index = (int)$selection - 1;
        if (!isset($functionCalls[$index])) {
            return $this->wechat->buildTextMessage($openid, '无效的选择，请重新输入您的问题。');
        }

        // 执行选择的函数
        $functionCall = $functionCalls[$index];
        return $this->executeFunction($openid, $functionCall);
    }

    /**
     * 生成图文消息
     */
    protected function generateNewsForFunction($answer, $functionCall, $openid)
    {
        $article = [
            'title' => $this->getFunctionTitle($functionCall),
            'description' => $answer,
            'picurl' => $this->getFunctionImage($functionCall['name']),
            'url' => $this->generateUrl($functionCall['name'], $functionCall['parameters'])
        ];

        return $this->wechat->buildNewsMessage($openid, [$article]);
    }

    /**
     * 获取函数标题
     */
    protected function getFunctionTitle($functionCall)
    {
        switch ($functionCall['name']) {
            case 'showOnMap':
                return $functionCall['parameters']['spotName'] . '的位置';
            case 'showScenicSpotDetails':
                return $functionCall['parameters']['spotName'] . '详情';
            case 'searchNearby':
                return '搜索附近' . ($functionCall['parameters']['keyword'] ?? '地点');
            case 'bookTicket':
                return '预订' . ($functionCall['parameters']['spotName'] ?? '景点') . '门票';
            case 'getWeather':
                return ($functionCall['parameters']['location'] ?? '当地') . '天气';
            default:
                return $functionCall['name'];
        }
    }

    /**
     * 获取函数图片
     */
    protected function getFunctionImage($functionName)
    {
        $baseUrl = config('site.cdnurl') . '/assets/addons/dsassistant/images/functions/';

        $images = [
            'showOnMap' => 'map.jpg',
            'showScenicSpotDetails' => 'scenic.jpg',
            'searchNearby' => 'search.jpg',
            'bookTicket' => 'ticket.jpg',
            'getWeather' => 'weather.jpg'
        ];

        return $baseUrl . ($images[$functionName] ?? 'default.jpg');
    }

    /**
     * 生成H5页面URL
     */
    protected function generateUrl($functionName, $parameters)
    {
        $baseUrl = request()->domain() . '/addons/dsassistant/wechat/h5/';

        switch ($functionName) {
            case 'showOnMap':
                return $baseUrl . 'map?' . http_build_query([
                    'spotId' => $parameters['spotId'] ?? '',
                    'spotName' => $parameters['spotName'] ?? '',
                    'latitude' => $parameters['latitude'] ?? '',
                    'longitude' => $parameters['longitude'] ?? ''
                ]);
            case 'showScenicSpotDetails':
                return $baseUrl . 'scenic?' . http_build_query([
                    'spotId' => $parameters['spotId'] ?? '',
                    'spotName' => $parameters['spotName'] ?? ''
                ]);
            case 'searchNearby':
                return $baseUrl . 'search?' . http_build_query([
                    'keyword' => $parameters['keyword'] ?? '',
                    'latitude' => $parameters['latitude'] ?? '',
                    'longitude' => $parameters['longitude'] ?? ''
                ]);
            case 'bookTicket':
                return $baseUrl . 'ticket?' . http_build_query([
                    'spotId' => $parameters['spotId'] ?? '',
                    'spotName' => $parameters['spotName'] ?? ''
                ]);
            case 'getWeather':
                return $baseUrl . 'weather?' . http_build_query([
                    'location' => $parameters['location'] ?? ''
                ]);
            default:
                return $baseUrl . 'index';
        }
    }

    /**
     * 保存函数调用到会话
     */
    protected function saveActionsToSession($openid, $functionCalls)
    {
        return \app\admin\model\dsassistant\WechatSession::saveSessionData($openid, 'function_calls', $functionCalls, 1800); // 30分钟有效
    }

    /**
     * 从会话中获取函数调用
     */
    protected function getActionsFromSession($openid)
    {
        return \app\admin\model\dsassistant\WechatSession::getSessionData($openid, 'function_calls') ?: [];
    }

    /**
     * 执行函数
     */
    protected function executeFunction($openid, $functionCall)
    {
        // 根据函数名执行不同操作
        switch ($functionCall['name']) {
            case 'showOnMap':
            case 'showScenicSpotDetails':
            case 'searchNearby':
            case 'bookTicket':
            case 'getWeather':
                // 这些函数都通过图文消息实现
                return $this->generateNewsForFunction('请点击查看详细信息', $functionCall, $openid);

            // 可以添加其他直接在公众号中执行的函数

            default:
                return $this->wechat->buildTextMessage($openid, '暂不支持该操作');
        }
    }
}
