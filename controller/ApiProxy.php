<?php

namespace addons\dsassistant\controller;

use app\common\controller\Api as BaseApi;

/**
 * API代理控制器 - 用于向后兼容
 * 将旧的API调用重定向到新的模块化API
 */
class ApiProxy extends BaseApi
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 聊天接口 - 重定向到Chat模块
     */
    public function chat()
    {
        return $this->proxy('app\api\controller\Chat', 'chat');
    }

    /**
     * 重置会话 - 重定向到Chat模块
     */
    public function resetSession()
    {
        return $this->proxy('app\api\controller\Chat', 'resetSession');
    }

    /**
     * 获取景点列表 - 重定向到Guide模块
     */
    public function getScenicSpots()
    {
        return $this->proxy('app\api\controller\Guide', 'getScenicSpots');
    }

    /**
     * 获取景点类型 - 重定向到Guide模块
     */
    public function getScenicSpotTypes()
    {
        return $this->proxy('app\api\controller\Guide', 'getScenicSpotTypes');
    }

    /**
     * 获取景点详情 - 重定向到Guide模块
     */
    public function getScenicSpotDetail()
    {
        return $this->proxy('app\api\controller\Guide', 'getScenicSpotDetail');
    }

    /**
     * 获取预警信息 - 重定向到Guide模块
     */
    public function getWarnings()
    {
        return $this->proxy('app\api\controller\Guide', 'getWarnings');
    }

    /**
     * 获取失物招领列表 - 重定向到LostFound模块
     */
    public function getLostFound()
    {
        return $this->proxy('app\api\controller\LostFound', 'getList');
    }

    /**
     * 提交失物招领 - 重定向到LostFound模块
     */
    public function submitLostFound()
    {
        return $this->proxy('app\api\controller\LostFound', 'submit');
    }

    /**
     * 获取失物招领详情 - 重定向到LostFound模块
     */
    public function getLostFoundDetail()
    {
        return $this->proxy('app\api\controller\LostFound', 'getDetail');
    }

    /**
     * 获取失物招领分类 - 重定向到LostFound模块
     */
    public function getLostFoundCategories()
    {
        return $this->proxy('app\api\controller\LostFound', 'getCategories');
    }

    /**
     * 提交反馈 - 重定向到Feedback模块
     */
    public function submitFeedback()
    {
        return $this->proxy('app\api\controller\Feedback', 'submit');
    }

    /**
     * 代理方法 - 将请求转发到指定的控制器和方法
     */
    private function proxy($controllerClass, $method)
    {
        try {
            // 实例化目标控制器
            $controller = new $controllerClass();
            
            // 设置请求对象
            $controller->request = $this->request;
            
            // 初始化控制器
            if (method_exists($controller, '_initialize')) {
                $controller->_initialize();
            }
            
            // 调用目标方法
            if (method_exists($controller, $method)) {
                return $controller->$method();
            } else {
                $this->error('方法不存在');
            }
        } catch (\Exception $e) {
            $this->error('代理调用失败: ' . $e->getMessage());
        }
    }
}
