<?php

namespace addons\dsassistant\controller\wechat;

use app\admin\model\dsassistant\Contentconfig;
use think\addons\Controller;
use think\Config;
use think\Db;
use think\Log;

/**
 * H5页面控制器
 */
class H5 extends Controller
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        // 加载公共资源
        $this->loadCommonResources();

        // 注册计算距离的方法
        $this->view->engine->assign('getDistance', function($lat1, $lng1, $lat2, $lng2) {
            return $this->getDistance($lat1, $lng1, $lat2, $lng2);
        });
    }

    /**
     * 计算两点之间的距离（单位：公里）
     */
    protected function getDistance($lat1, $lng1, $lat2, $lng2)
    {
        // 将角度转为弧度
        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        // 地球半径（单位：公里）
        $earthRadius = 6371;

        // 使用Haversine公式计算球面距离
        $dlat = $lat2 - $lat1;
        $dlng = $lng2 - $lng1;
        $a = sin($dlat/2) * sin($dlat/2) + cos($lat1) * cos($lat2) * sin($dlng/2) * sin($dlng/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $distance = $earthRadius * $c;

        return $distance;
    }

    /**
     * 加载公共资源
     */
    protected function loadCommonResources()
    {
        // 加载CSS和JS
        $this->view->assign('site', config('site'));

        // 加载配置信息
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $this->view->assign('tencentMapKey', $config['tencent_map_key'] ?? '');

        // 加载微信JS SDK配置
        $this->loadWechatJsConfig();
    }

    /**
     * 加载微信JS SDK配置
     */
    protected function loadWechatJsConfig()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $appId = $config['wechat_appid'] ?? '';
        $appSecret = $config['wechat_appsecret'] ?? '';

        if (empty($appId) || empty($appSecret)) {
            Log::info('微信公众号配置不完整，无法加载JS SDK');
            return;
        }

        try {
            // 获取当前完整URL
            $currentUrl = request()->url(true);

            // 创建微信实例
            $wechat = new \addons\dsassistant\library\Wechat([
                'appid' => $appId,
                'appsecret' => $appSecret
            ]);

            // 获取JS SDK配置
            $jsConfig = $wechat->getJsConfig($currentUrl);

            // 将配置传递给视图
            if ($jsConfig && isset($jsConfig['appId'])) {
                $this->view->assign('jsConfig', $jsConfig);
                Log::info('微信JS SDK配置加载成功');
            } else {
                Log::error('微信JS SDK配置无效');
            }
        } catch (\Exception $e) {
            Log::error('加载微信JS SDK配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 首页
     */
    public function index()
    {
        return $this->view->fetch();
    }

    /**
     * 地图页面
     */
    public function map()
    {
        $params = $this->request->get();
        $config = Contentconfig::getConfigAll();
        $this->view->assign('spotId', $params['spotId'] ?? '');
        $this->view->assign('spotName', $params['spotName'] ?? '');
        $this->view->assign('latitude', $params['latitude'] ?? '');
        $this->view->assign('longitude', $params['longitude'] ?? '');
        $this->view->assign('tencentMapKey', $config['tencent_map_key'] ?? '');

        return $this->view->fetch();
    }

    /**
     * 景点详情页面
     */
    public function scenic()
    {
        $params = $this->request->get();

        $this->view->assign('spotId', $params['spotId'] ?? '');
        $this->view->assign('spotName', $params['spotName'] ?? '');

        // 获取景点详情数据
        $spotId = $params['spotId'] ?? '';
        if (!empty($spotId)) {
            $spotInfo = Db::name('ds_scenic_spot')
                ->where('id', $spotId)
                ->find();

            if ($spotInfo) {
                $this->view->assign('spotInfo', $spotInfo);
            }
        }

        return $this->view->fetch();
    }

    /**
     * 搜索页面
     */
    public function search()
    {
        $params = $this->request->get();

        $this->view->assign('keyword', $params['keyword'] ?? '');
        $this->view->assign('latitude', $params['latitude'] ?? '');
        $this->view->assign('longitude', $params['longitude'] ?? '');

        // 如果有关键词，执行搜索
        $keyword = $params['keyword'] ?? '';
        if (!empty($keyword)) {
            $results = Db::name('ds_scenic_spot')
                ->where('name', 'like', "%{$keyword}%")
                ->whereOr('description', 'like', "%{$keyword}%")
                ->select();

            $this->view->assign('results', $results);
        }

        return $this->view->fetch();
    }

    /**
     * 订票页面
     */
    public function ticket()
    {
        $params = $this->request->get();

        $this->view->assign('spotId', $params['spotId'] ?? '');
        $this->view->assign('spotName', $params['spotName'] ?? '');

        // 获取门票信息
        $spotId = $params['spotId'] ?? '';
        if (!empty($spotId)) {
            $tickets = Db::name('ds_scenic_ticket')
                ->where('spot_id', $spotId)
                ->select();

            $this->view->assign('tickets', $tickets);
        }

        return $this->view->fetch();
    }

    /**
     * 天气页面
     */
    public function weather()
    {
        $params = $this->request->get();

        $this->view->assign('location', $params['location'] ?? '');

        // 获取天气数据
        $location = $params['location'] ?? '';
        if (!empty($location)) {
            // 这里可以调用天气API获取数据
            // 示例数据
            $weatherData = [
                'location' => $location,
                'temperature' => '25°C',
                'weather' => '晴',
                'wind' => '东北风 3-4级',
                'humidity' => '65%',
                'forecast' => [
                    ['date' => '明天', 'weather' => '多云', 'temperature' => '23°C-28°C'],
                    ['date' => '后天', 'weather' => '小雨', 'temperature' => '20°C-25°C']
                ]
            ];

            $this->view->assign('weatherData', $weatherData);
        }

        return $this->view->fetch();
    }
}
