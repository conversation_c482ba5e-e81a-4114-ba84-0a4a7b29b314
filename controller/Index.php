<?php

namespace addons\dsassistant\controller;

use think\addons\Controller;

/**
 * 景区智能助理首页
 */
class Index extends Controller
{
    public function index()
    {
        return $this->view->fetch();
    }

    /**
     * 聊天页面
     */
    public function chat()
    {
        return $this->view->fetch();
    }

    /**
     * 景点导览
     */
    public function guide()
    {
        return $this->view->fetch();
    }

    /**
     * 失物招领
     */
    public function lostfound()
    {
        return $this->view->fetch();
    }

    /**
     * 预警信息
     */
    public function warning()
    {
        return $this->view->fetch();
    }

    /**
     * 失物招领详情
     */
    public function lostfound_detail()
    {
        return $this->view->fetch();
    }
}
