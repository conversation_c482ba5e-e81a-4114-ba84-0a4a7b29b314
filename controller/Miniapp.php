<?php

namespace addons\dsassistant\controller;

use app\common\controller\Api as BaseApi;
use app\common\model\DsAssistant;
use think\Db;
use think\Exception;
use think\Log;

/**
 * 小程序控制器
 */
class Miniapp extends BaseApi
{
    /**
     * 无需登录的方法
     * @var array
     */
    protected $noNeedLogin = ['index', 'chat', 'feedback', 'getConfig', 'getHistory'];

    /**
     * 无需鉴权的方法
     * @var array
     */
    protected $noNeedRight = ['*'];

    /**
     * AI助手实例
     * @var DsAssistant
     */
    protected $assistant = null;

    /**
     * 初始化
     */
    public function _initialize()
    {
        parent::_initialize();

        // 初始化AI助手
        $this->assistant = new DsAssistant();

        // 设置跨域头
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');

        // 处理OPTIONS请求
        if ($this->request->isOptions()) {
            exit;
        }
    }

    /**
     * 小程序首页
     */
    public function index()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $this->view->assign('config', $config);
        return $this->view->fetch();
    }

    /**
     * 聊天接口
     */
    public function chat()
    {
        $question = $this->request->post('question');
        // 优先使用传入的user_id，如果没有则尝试使用设备ID，最后才使用IP
        $userId = $this->request->post('user_id', '');

        // 如果没有提供user_id，尝试使用设备ID
        if (empty($userId)) {
            $deviceId = $this->request->post('device_id', '');
            if (!empty($deviceId)) {
                $userId = 'device_' . $deviceId;
            } else {
                // 最后才使用IP+UA组合作为标识
                $ip = $this->request->ip();
                $ua = $this->request->header('user-agent', '');
                $userId = 'ip_' . md5($ip . $ua);
            }
        }
        $sessionId = $this->request->post('session_id', md5($userId . date('Ymd')));
        $platform = $this->request->post('platform', 'miniapp');
        $ip = $this->request->ip();

        if (empty($question)) {
            $this->error('问题不能为空');
        }

        $answer = '';
        $errorMsg = '';

        try {
            $answer = $this->assistant->handleQuestion($question, $userId, $sessionId, $platform, $ip);
        } catch (Exception $e) {
            Log::error('小程序聊天接口异常: ' . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success('', [
                'answer' => $answer,
                'session_id' => $sessionId
            ]);
        }
    }

    /**
     * 获取聊天历史记录
     */
    public function getHistory()
    {
        $sessionId = $this->request->param('session_id');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);

        if (empty($sessionId)) {
            $this->error('会话ID不能为空');
        }

        $result = [];
        $total = 0;
        $errorMsg = '';

        try {
            $offset = ($page - 1) * $limit;

            $history = Db::name('ds_chat_log')
                ->where('session_id', $sessionId)
                ->order('id desc')
                ->limit($offset, $limit)
                ->select();

            $total = Db::name('ds_chat_log')
                ->where('session_id', $sessionId)
                ->count();

            // 格式化数据
            $result = [];
            foreach ($history as $item) {
                $result[] = [
                    'id' => $item['id'],
                    'question' => $item['question'],
                    'answer' => $item['answer'],
                    'time' => date('Y-m-d H:i:s', $item['createtime']),
                    'source' => $item['source']
                ];
            }
        } catch (Exception $e) {
            Log::error('获取聊天历史记录异常: ' . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success('', [
                'list' => $result,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        }
    }

    /**
     * 提交反馈
     */
    public function feedback()
    {
        $content = $this->request->post('content');
        $type = $this->request->post('type', 'suggestion');
        $contact = $this->request->post('contact', '');
        // 优先使用传入的user_id，如果没有则尝试使用设备ID，最后才使用IP
        $userId = $this->request->post('user_id', '');

        // 如果没有提供user_id，尝试使用设备ID
        if (empty($userId)) {
            $deviceId = $this->request->post('device_id', '');
            if (!empty($deviceId)) {
                $userId = 'device_' . $deviceId;
            } else {
                // 最后才使用IP+UA组合作为标识
                $ip = $this->request->ip();
                $ua = $this->request->header('user-agent', '');
                $userId = 'ip_' . md5($ip . $ua);
            }
        }

        if (empty($content)) {
            $this->error('反馈内容不能为空');
        }

        $result = false;
        $errorMsg = '';

        try {
            $data = [
                'content' => $content,
                'type' => $type,
                'contact' => $contact,
                'user_id' => $userId,
                'ip' => $this->request->ip(),
                'createtime' => time(),
                'status' => 'pending'
            ];

            $result = Db::name('ds_feedback')->insert($data);
        } catch (Exception $e) {
            Log::error('提交反馈异常: ' . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } elseif ($result) {
            $this->success('反馈提交成功');
        } else {
            $this->error('反馈提交失败');
        }
    }

    /**
     * 获取配置信息
     */
    public function getConfig()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();

        // 过滤敏感配置
        $safeConfig = [
            'site_name' => $config['site_name'] ?? '景区智能助理',
            'site_logo' => $config['site_logo'] ?? '',
            'welcome_message' => $config['welcome_message'] ?? '您好！我是景区智能助理，请问有什么可以帮助您的？',
            'customer_service_phone' => $config['customer_service_phone'] ?? '',
            'business_hours' => $config['business_hours'] ?? '9:00-18:00',
            'miniapp_version' => $config['miniapp_version'] ?? '1.0.0',
            'miniapp_update_time' => $config['miniapp_update_time'] ?? date('Y-m-d'),
            'miniapp_update_log' => $config['miniapp_update_log'] ?? ''
        ];

        $this->success('', $safeConfig);
    }
}
