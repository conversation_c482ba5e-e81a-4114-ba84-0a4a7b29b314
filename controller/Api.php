<?php

namespace addons\dsassistant\controller;

use app\common\controller\Api as BaseApi;

/**
 * 景区助理API - 兼容性代理
 *
 * @deprecated 此文件已废弃，请使用新的模块化API
 * 新的API位于 application/api/controller/ 目录下
 *
 * 对话相关: app\api\controller\Chat
 * 导览相关: app\api\controller\Guide
 * 失物招领: app\api\controller\LostFound
 * 反馈相关: app\api\controller\Feedback
 */
class Api extends BaseApi
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 代理方法 - 将请求转发到指定的控制器和方法
     */
    private function proxy($controllerClass, $method)
    {
        try {
            // 实例化目标控制器
            $controller = new $controllerClass();

            // 设置请求对象
            $controller->request = $this->request;

            // 初始化控制器
            if (method_exists($controller, '_initialize')) {
                $controller->_initialize();
            }

            // 调用目标方法
            if (method_exists($controller, $method)) {
                return $controller->$method();
            } else {
                $this->error('方法不存在');
            }
        } catch (\Exception $e) {
            $this->error('代理调用失败: ' . $e->getMessage());
        }
    }

    /**
     * 重置会话 - 代理到Chat模块
     * @deprecated 请使用 app\api\controller\dsassistant\Chat::resetSession
     */
    public function resetSession()
    {
        return $this->proxy('app\api\controller\dsassistant\Chat', 'resetSession');
    }

    /**
     * 聊天接口 - 代理到Chat模块
     * @deprecated 请使用 app\api\controller\dsassistant\Chat::chat
     */
    public function chat()
    {
        return $this->proxy('app\api\controller\dsassistant\Chat', 'chat');
    }

    /**
     * 获取景点列表 - 代理到Guide模块
     * @deprecated 请使用 app\api\controller\dsassistant\Guide::getScenicSpots
     */
    public function getScenicSpots()
    {
        return $this->proxy('app\api\controller\dsassistant\Guide', 'getScenicSpots');
    }

    /**
     * 获取景点类型 - 代理到Guide模块
     * @deprecated 请使用 app\api\controller\dsassistant\Guide::getScenicSpotTypes
     */
    public function getScenicSpotTypes()
    {
        return $this->proxy('app\api\controller\dsassistant\Guide', 'getScenicSpotTypes');
    }

    /**
     * 获取景点详情 - 代理到Guide模块
     * @deprecated 请使用 app\api\controller\dsassistant\Guide::getScenicSpotDetail
     */
    public function getScenicSpotDetail()
    {
        return $this->proxy('app\api\controller\dsassistant\Guide', 'getScenicSpotDetail');
    }

    /**
     * 获取预警信息 - 代理到Guide模块
     * @deprecated 请使用 app\api\controller\dsassistant\Guide::getWarnings
     */
    public function getWarnings()
    {
        return $this->proxy('app\api\controller\dsassistant\Guide', 'getWarnings');
    }

    /**
     * 获取失物招领列表 - 代理到LostFound模块
     * @deprecated 请使用 app\api\controller\dsassistant\LostFound::getList
     */
    public function getLostFound()
    {
        return $this->proxy('app\api\controller\dsassistant\LostFound', 'getList');
    }

    /**
     * 提交失物招领 - 代理到LostFound模块
     * @deprecated 请使用 app\api\controller\dsassistant\LostFound::submit
     */
    public function submitLostFound()
    {
        return $this->proxy('app\api\controller\dsassistant\LostFound', 'submit');
    }

    /**
     * 获取失物招领详情 - 代理到LostFound模块
     * @deprecated 请使用 app\api\controller\dsassistant\LostFound::getDetail
     */
    public function getLostFoundDetail()
    {
        return $this->proxy('app\api\controller\dsassistant\LostFound', 'getDetail');
    }

    /**
     * 获取失物招领分类 - 代理到LostFound模块
     * @deprecated 请使用 app\api\controller\dsassistant\LostFound::getCategories
     */
    public function getLostFoundCategories()
    {
        return $this->proxy('app\api\controller\dsassistant\LostFound', 'getCategories');
    }

    /**
     * 提交反馈 - 代理到Feedback模块
     * @deprecated 请使用 app\api\controller\dsassistant\Feedback::submit
     */
    public function submitFeedback()
    {
        return $this->proxy('app\api\controller\dsassistant\Feedback', 'submit');
    }
}
