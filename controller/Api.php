<?php

namespace addons\dsassistant\controller;

use app\common\controller\Api as BaseApi;
use app\common\model\DsAssistant;
use think\Db;
use think\Log;
use think\Exception;

/**
 * 景区助理API
 */
class Api extends BaseApi
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    protected $assistant = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->assistant = new DsAssistant();
    }

    /**
     * 重置会话
     */
    public function resetSession()
    {
        $sessionId = $this->request->post('session_id', '');

        if (empty($sessionId)) {
            $this->error('会话ID不能为空');
        }

        $result = \addons\dsassistant\library\SessionManager::closeSession($sessionId);

        if ($result) {
            $this->success('会话已重置');
        } else {
            $this->error('会话重置失败');
        }
    }

    /**
     * 聊天接口
     */
    public function chat()
    {
        $question = $this->request->post('question');
        // 优先使用传入的user_id，如果没有则尝试使用设备ID，最后才使用IP
        $userId = $this->request->post('user_id', '');

        // 如果没有提供user_id，尝试使用设备ID
        if (empty($userId)) {
            $deviceId = $this->request->post('device_id', '');
            if (!empty($deviceId)) {
                $userId = 'device_' . $deviceId;
            } else {
                // 最后才使用IP+UA组合作为标识
                $ip = $this->request->ip();
                $ua = $this->request->header('user-agent', '');
                $userId = 'ip_' . md5($ip . $ua);
            }
        }
        $sessionId = $this->request->post('session_id', md5($userId . date('Ymd')));
        $platform = $this->request->post('platform', 'miniapp');
        $ip = $this->request->ip();

        if (empty($question)) {
            $this->error('问题不能为空');
        }

        // 检查是否是重置会话的命令
        if ($question === '/reset' || $question === '重置会话') {
            // 重置会话
            \addons\dsassistant\library\SessionManager::closeSession($sessionId);
            $this->success('', [
                'answer' => '会话已重置，我们可以开始新的对话了。',
                'session_id' => $sessionId
            ]);
            return;
        }

        $result = '';
        $errorMsg = '';

        try {
            $result = $this->assistant->handleQuestion($question, $userId, $sessionId, $platform, $ip);
        } catch (Exception $e) {
            Log::error('聊天接口异常: ' . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            // 检查返回结果是否包含函数调用
            if (is_array($result) && isset($result['answer']) && isset($result['functionCalls'])) {
                // 包含函数调用的响应
                $this->success('', [
                    'answer' => $result['answer'],
                    'functionCalls' => $result['functionCalls'],
                    'session_id' => $sessionId
                ]);
            } else {
                // 普通文本响应
                $this->success('', [
                    'answer' => $result,
                    'session_id' => $sessionId
                ]);
            }
        }
    }

    /**
     * 获取景点列表
     */
    public function getScenicSpots()
    {
        $page = $this->request->param('page/d', 1);
        $pageSize = $this->request->param('pageSize/d', 10);
        $type = $this->request->param('type', '');
        $keyword = $this->request->param('keyword', '');

        $query = Db::name('ds_scenic_spot')
            ->alias('s')
            ->leftJoin('ds_scenic_spot_type t', 's.type_id = t.id')
            ->field('s.*, t.name as type_name, t.code as type_code, t.color as type_color, t.icon as type_icon')
            ->where('s.status', 'normal');

        // 按类型筛选
        if (!empty($type) && $type !== 'all') {
            $query->where('t.code', $type);
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $query->where('s.name|s.description', 'like', '%' . $keyword . '%');
        }

        $total = $query->count();
        $list = $query->order('s.weight DESC, s.id DESC')
            ->page($page, $pageSize)
            ->select();

        foreach ($list as &$item) {
            if (!empty($item['images'])) {
                $item['images'] = explode(',', $item['images']);
                $item['cover'] = $item['images'][0] ?? '';
            } else {
                $item['images'] = [];
                $item['cover'] = '';
            }

            // 添加类型信息
            $item['type'] = $item['type_code'] ?? '';
            $item['type_info'] = [
                'name' => $item['type_name'] ?? '',
                'code' => $item['type_code'] ?? '',
                'color' => $item['type_color'] ?? '#1890ff',
                'icon' => $item['type_icon'] ?? ''
            ];
        }

        $this->success('', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize
        ]);
    }

    /**
     * 获取景点类型列表
     */
    public function getScenicSpotTypes()
    {
        $list = Db::name('ds_scenic_spot_type')
            ->where('status', 'normal')
            ->order('weight DESC, id ASC')
            ->select();

        // 添加"全部"选项
        array_unshift($list, [
            'id' => 0,
            'name' => '全部',
            'code' => 'all',
            'icon' => 'icon-all',
            'color' => '#666666'
        ]);

        $this->success('', ['list' => $list]);
    }

    /**
     * 获取景点详情
     */
    public function getScenicSpotDetail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->error('参数错误：景点ID不能为空');
        }

        // 先检查景点是否存在，不考虑状态
        $spot = null;
        $errorMsg = '';

        try {
            $spot = Db::name('ds_scenic_spot')
                ->where('id', $id)
                ->find();

            if (!$spot) {
                // 记录错误日志
                \think\Log::error("景点不存在: ID = {$id}");

                // 获取所有景点ID列表，用于调试
                $allSpots = Db::name('ds_scenic_spot')
                    ->field('id, name, status')
                    ->select();
                \think\Log::info("可用景点列表: " . json_encode($allSpots, JSON_UNESCAPED_UNICODE));

                $errorMsg = "景点不存在 (ID: {$id})";
                return;
            }

            // 检查景点状态
            if ($spot['status'] !== 'normal') {
                \think\Log::warning("景点状态异常: ID = {$id}, 状态 = {$spot['status']}");
                $errorMsg = "该景点暂时不可用 (ID: {$id}, 状态: {$spot['status']})";
                return;
            }

            // 处理图片
            if (!empty($spot['images'])) {
                $spot['images'] = explode(',', $spot['images']);
            } else {
                $spot['images'] = [];
            }
        } catch (\Exception $e) {
            \think\Log::error("获取景点详情异常: " . $e->getMessage());
            $errorMsg = '系统错误，请稍后再试';
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success('获取成功', ['info' => $spot]);
        }
    }

    /**
     * 获取预警信息
     */
    public function getWarnings()
    {
        $list = Db::name('ds_warning')
            ->where('status', 'normal')
            ->where('start_time', '<=', time())
            ->where('end_time', '>=', time())
            ->order('level DESC, id DESC')
            ->select();

        $this->success('', ['list' => $list]);
    }

    /**
     * 获取失物招领列表
     */
    public function getLostFound()
    {
        $type = $this->request->param('type', '');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 10);
        $keyword = $this->request->param('keyword', '');
        $categoryId = $this->request->param('category_id', '');
        $excludeId = $this->request->param('exclude_id/d', 0);

        $where = ['status' => ['in', ['pending', 'processed']]];

        // 按类型筛选
        if (in_array($type, ['lost', 'found'])) {
            $where['type'] = $type;
        }

        // 按分类ID筛选
        if (!empty($categoryId)) {
            $where['category_id'] = $categoryId;
        }

        // 排除指定ID（用于相关推荐）
        if ($excludeId > 0) {
            $where['id'] = ['<>', $excludeId];
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $where['title|description|location'] = ['like', "%{$keyword}%"];
        }

        $total = Db::name('ds_lost_found')
            ->where($where)
            ->count();

        $list = Db::name('ds_lost_found')
            ->where($where)
            ->order('id DESC')
            ->page($page, $limit)
            ->select();

        foreach ($list as &$item) {
            // 处理图片
            if (!empty($item['images'])) {
                $item['images'] = explode(',', $item['images']);
                $item['cover'] = $item['images'][0] ?? '';
            } else {
                $item['images'] = [];
                $item['cover'] = '';
            }

            // 获取分类名称
            if (!empty($item['category_id'])) {
                $category = Db::name('ds_lost_found_category')
                    ->where('id', $item['category_id'])
                    ->find();
                $item['category'] = $category ? $category['category'] : '';
            }
        }

        $this->success('', [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 提交失物招领
     */
    public function submitLostFound()
    {
        $data = [
            'title' => $this->request->post('title'),
            'description' => $this->request->post('description'),
            'type' => $this->request->post('type'),
            'category_id' => $this->request->post('category_id/d', 0), // 使用分类ID
            'location' => $this->request->post('location', ''),
            'contact_name' => $this->request->post('contact_name', ''),
            'contact_phone' => $this->request->post('contact_phone', ''),
            'images' => $this->request->post('images', ''),
            'status' => 'pending',
            'createtime' => time(),
            'updatetime' => time()
        ];

        // 兼容旧版本，如果提供了category字段但没有category_id
        if (empty($data['category_id'])) {
            $category = $this->request->post('category', '');
            if (!empty($category)) {
                // 尝试根据分类名称查找分类ID
                $categoryInfo = Db::name('ds_lost_found_category')
                    ->where('category', $category)
                    ->where('status', 'normal')
                    ->find();

                if ($categoryInfo) {
                    $data['category_id'] = $categoryInfo['id'];
                }
            }
        }

        if (empty($data['title']) || empty($data['description']) || !in_array($data['type'], ['lost', 'found'])) {
            $this->error('参数错误');
        }

        if (is_array($data['images'])) {
            $data['images'] = implode(',', $data['images']);
        }

        $id = 0;
        $errorMsg = '';

        try {
            $id = Db::name('ds_lost_found')->insertGetId($data);

            // 记录日志
            \think\Log::info("提交失物招领成功: ID = {$id}, 标题 = {$data['title']}, 分类ID = {$data['category_id']}");

        } catch (Exception $e) {
            \think\Log::error("提交失物招领异常: " . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } elseif ($id) {
            $this->success('提交成功', ['id' => $id]);
        } else {
            $this->error('提交失败，请稍后再试');
        }
    }

    /**
     * 获取失物招领详情
     */
    public function getLostFoundDetail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->error('参数错误：ID不能为空');
        }

        // 查询失物招领信息
        $info = null;
        $errorMsg = '';

        try {
            $info = Db::name('ds_lost_found')
                ->where('id', $id)
                ->where('status', 'in', ['pending', 'processed'])
                ->find();

            if (!$info) {
                // 记录错误日志
                \think\Log::error("失物招领信息不存在: ID = {$id}");

                // 获取所有失物招领ID列表，用于调试
                $allItems = Db::name('ds_lost_found')
                    ->field('id, title, status')
                    ->limit(10)
                    ->select();
                \think\Log::info("最近10条失物招领列表: " . json_encode($allItems, JSON_UNESCAPED_UNICODE));

                $errorMsg = "失物招领信息不存在 (ID: {$id})";
                return;
            }

            // 处理图片
            if (!empty($info['images'])) {
                $info['images'] = explode(',', $info['images']);
            } else {
                $info['images'] = [];
            }

            // 获取分类信息
            if (!empty($info['category_id'])) {
                $category = Db::name('ds_lost_found_category')
                    ->where('id', $info['category_id'])
                    ->find();
                if ($category) {
                    $info['category'] = $category['category'];
                }
            }

            // 格式化时间
            $info['create_time'] = date('Y-m-d H:i:s', $info['createtime']);
            if (!empty($info['updatetime'])) {
                $info['update_time'] = date('Y-m-d H:i:s', $info['updatetime']);
            }
        } catch (\Exception $e) {
            \think\Log::error("获取失物招领详情异常: " . $e->getMessage());
            $errorMsg = '系统错误，请稍后再试';
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success('获取成功', ['info' => $info]);
        }
    }

    /**
     * 获取失物招领分类列表
     */
    public function getLostFoundCategories()
    {
        $where = ['status' => 'normal'];

        // 可选参数：是否包含隐藏分类
        $includeHidden = $this->request->param('include_hidden/b', false);
        if ($includeHidden) {
            $where = [];
        }

        $categories = null;
        $errorMsg = '';

        try {
            $categories = Db::name('ds_lost_found_category')
                ->where($where)
                ->order('weight DESC')
                ->select();

            // 记录日志
            \think\Log::info("获取失物招领分类列表: " . count($categories) . " 条记录");

        } catch (\Exception $e) {
            \think\Log::error("获取失物招领分类列表异常: " . $e->getMessage());
            $errorMsg = '系统错误，请稍后再试';
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success('获取成功', $categories);
        }
    }

    /**
     * 提交反馈
     */
    public function submitFeedback()
    {
        $content = $this->request->post('content');
        $type = $this->request->post('type', 'suggestion');
        $contact = $this->request->post('contact', '');

        // 优先使用传入的user_id，如果没有则尝试使用设备ID，最后才使用IP
        $userId = $this->request->post('user_id', '');

        // 如果没有提供user_id，尝试使用设备ID
        if (empty($userId)) {
            $deviceId = $this->request->post('device_id', '');
            if (!empty($deviceId)) {
                $userId = 'device_' . $deviceId;
            } else {
                // 最后才使用IP+UA组合作为标识
                $ip = $this->request->ip();
                $ua = $this->request->header('user-agent', '');
                $userId = 'ip_' . md5($ip . $ua);
            }
        }

        if (empty($content)) {
            $this->error('反馈内容不能为空');
        }

        $result = false;
        $errorMsg = '';

        try {
            $data = [
                'content' => $content,
                'type' => $type,
                'contact' => $contact,
                'user_id' => $userId,
                'ip' => $this->request->ip(),
                'createtime' => time(),
                'status' => 'pending'
            ];

            $result = Db::name('ds_feedback')->insert($data);
        } catch (Exception $e) {
            \think\Log::error("提交反馈异常: " . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } elseif ($result) {
            $this->success('反馈提交成功');
        } else {
            $this->error('反馈提交失败');
        }
    }
}
