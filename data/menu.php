<?php

return [
    [
        'name'    => 'dsassistant',
        'title'   => '景区智能助理',
        'icon'    => 'fa fa-robot',
        'sublist' => [
            [
                'name'    => 'dsassistant/content',
                'title'   => '内容管理',
                'icon'    => 'fa fa-file-text',
                'sublist' => [
                    ['name' => 'dsassistant/content/index', 'title' => '知识库管理'],
                    ['name' => 'dsassistant/content/add', 'title' => '添加知识'],
                    ['name' => 'dsassistant/content/edit', 'title' => '编辑知识', 'remark' => 'Edit content'],
                    ['name' => 'dsassistant/content/del', 'title' => '删除知识', 'remark' => 'Delete content'],
                    ['name' => 'dsassistant/content/multi', 'title' => '批量更新', 'remark' => 'Multi update'],
                    ['name' => 'dsassistant/content/import', 'title' => '导入知识', 'remark' => 'Import content'],
                    ['name' => 'dsassistant/content/export', 'title' => '导出知识', 'remark' => 'Export content'],
                ]
            ],
            [
                'name'    => 'dsassistant/chatlog',
                'title'   => '聊天记录',
                'icon'    => 'fa fa-comments',
                'sublist' => [
                    ['name' => 'dsassistant/chatlog/index', 'title' => '聊天记录列表'],
                    ['name' => 'dsassistant/chatlog/detail', 'title' => '聊天详情', 'remark' => 'Chat detail'],
                    ['name' => 'dsassistant/chatlog/del', 'title' => '删除记录', 'remark' => 'Delete chat log'],
                    ['name' => 'dsassistant/chatlog/multi', 'title' => '批量操作', 'remark' => 'Multi update'],
                ]
            ],
            [
                'name'    => 'dsassistant/contentconfig',
                'title'   => '系统配置',
                'icon'    => 'fa fa-cogs',
                'sublist' => [
                    ['name' => 'dsassistant/contentconfig/index', 'title' => '配置管理'],
                    ['name' => 'dsassistant/contentconfig/add', 'title' => '添加配置', 'remark' => 'Add config'],
                    ['name' => 'dsassistant/contentconfig/edit', 'title' => '编辑配置', 'remark' => 'Edit config'],
                    ['name' => 'dsassistant/contentconfig/del', 'title' => '删除配置', 'remark' => 'Delete config'],
                    ['name' => 'dsassistant/contentconfig/multi', 'title' => '批量更新', 'remark' => 'Multi update'],
                ]
            ],
            [
                'name'    => 'dsassistant/function',
                'title'   => '函数管理',
                'icon'    => 'fa fa-code',
                'sublist' => [
                    ['name' => 'dsassistant/function/index', 'title' => '函数列表'],
                    ['name' => 'dsassistant/function/edit', 'title' => '编辑函数', 'remark' => 'Edit function'],
                    ['name' => 'dsassistant/function/enable', 'title' => '启用函数', 'remark' => 'Enable function'],
                    ['name' => 'dsassistant/function/disable', 'title' => '禁用函数', 'remark' => 'Disable function'],
                ]
            ],
            [
                'name'    => 'dsassistant/llm',
                'title'   => 'LLM配置',
                'icon'    => 'fa fa-brain',
                'sublist' => [
                    ['name' => 'dsassistant/llm/index', 'title' => 'LLM配置管理'],
                    ['name' => 'dsassistant/llm/save', 'title' => '保存配置', 'remark' => 'Save LLM config'],
                    ['name' => 'dsassistant/llm/addProvider', 'title' => '添加服务商', 'remark' => 'Add provider'],
                    ['name' => 'dsassistant/llm/editProvider', 'title' => '编辑服务商', 'remark' => 'Edit provider'],
                    ['name' => 'dsassistant/llm/deleteProvider', 'title' => '删除服务商', 'remark' => 'Delete provider'],
                    ['name' => 'dsassistant/llm/addModel', 'title' => '添加模型', 'remark' => 'Add model'],
                    ['name' => 'dsassistant/llm/editModel', 'title' => '编辑模型', 'remark' => 'Edit model'],
                    ['name' => 'dsassistant/llm/deleteModel', 'title' => '删除模型', 'remark' => 'Delete model'],
                    ['name' => 'dsassistant/llm/setDefault', 'title' => '设置默认', 'remark' => 'Set default'],
                    ['name' => 'dsassistant/llm/testConnection', 'title' => '测试连接', 'remark' => 'Test connection'],
                ]
            ],
        ]
    ]
];
