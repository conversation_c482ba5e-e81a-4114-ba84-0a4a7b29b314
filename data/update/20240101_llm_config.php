<?php

use think\Db;
use think\Exception;
use think\Log;

/**
 * 添加LLM配置字段
 */
try {
    // 检查llm_config字段是否已存在
    $exists = Db::query("SHOW COLUMNS FROM `__PREFIX__dsassistant_contentconfig` LIKE 'llm_config'");
    if (!$exists) {
        // 添加llm_config字段
        Db::execute("ALTER TABLE `__PREFIX__dsassistant_contentconfig` ADD COLUMN `llm_config` TEXT COMMENT 'LLM配置' AFTER `deepseek_api_key`");
        
        // 获取现有配置
        $config = Db::name('dsassistant_contentconfig')->find();
        
        // 构建默认LLM配置
        $llmConfig = [
            'providers' => [
                'deepseek' => [
                    'name' => 'DeepSeek',
                    'base_url' => 'https://api.deepseek.com/v1',
                    'api_key' => $config['deepseek_api_key'] ?? '',
                    'models' => [
                        'deepseek-reasoner' => [
                            'name' => 'DeepSeek Reasoner',
                            'supports_functions' => true,
                            'max_tokens' => 4096
                        ],
                        'deepseek-chat' => [
                            'name' => 'DeepSeek Chat',
                            'supports_functions' => false,
                            'max_tokens' => 4096
                        ]
                    ],
                    'default_model' => 'deepseek-reasoner'
                ]
            ],
            'default_provider' => 'deepseek',
            'default_model' => 'deepseek-reasoner'
        ];
        
        // 保存LLM配置
        Db::name('dsassistant_contentconfig')->update([
            'id' => $config['id'],
            'llm_config' => json_encode($llmConfig, JSON_UNESCAPED_UNICODE)
        ]);
        
        Log::record('添加LLM配置字段成功', 'info');
    } else {
        Log::record('LLM配置字段已存在', 'info');
    }
} catch (Exception $e) {
    Log::record('添加LLM配置字段失败: ' . $e->getMessage(), 'error');
}
