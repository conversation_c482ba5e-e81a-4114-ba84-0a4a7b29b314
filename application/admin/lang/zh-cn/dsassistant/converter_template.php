<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

return [
    'Category_id'       => '主分类ID',
    'Name'             => '模板名称',
    'Source_table'     => '数据源表名',
    'Target_table'     => '目标表名',
    'Source_type'      => '数据类型',
    'Content_type'     => '内容类型',
    'Template_content' => '模板内容(JSON)',
    'Sync_mode'        => '同步模式',
    'Sync_field'       => '增量同步字段',
    'Last_sync_time'   => '上次同步时间',
    'Last_sync_id'     => '上次同步ID',
    'Where_condition'  => '查询条件',
    'Order_field'      => '排序字段',
    'Order_direction'  => '排序方向',
    'Batch_size'       => '每批处理数量',
    'Status'           => '状态',
    'Createtime'       => '创建时间',
    'Updatetime'       => '更新时间',
    'Full'             => '全量',
    'Increment'        => '增量',
    'Asc'              => '升序',
    'Desc'             => '降序',
    'Normal'           => '正常',
    'Hidden'           => '隐藏',
    'Execute'          => '执行',
    'Batch execution completed'                                => '批量执行完成',
    'Batch execution completed with errors'                    => '批量执行完成(有错误)',
    'Initialize completed'                                     => '初始化完成',
    'Initialize completed with errors'                         => '初始化完成(有错误)',
    'Parameter %s can not be empty'                            => '参数 %s 不能为空',
    'No Results were found'                                    => '未找到结果'
];