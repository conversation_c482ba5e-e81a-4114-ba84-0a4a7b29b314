<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\model\dsassistant;

use think\Model;


class ConverterTemplate extends Model
{

    

    

    // 表名
    protected $name = 'ds_converter_template';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'sync_mode_text',
        'last_sync_time_text',
        'order_direction_text',
        'status_text'
    ];
    

    
    public function getSyncModeList()
    {
        return ['full' => __('Full'), 'increment' => __('Increment')];
    }

    public function getOrderDirectionList()
    {
        return ['asc' => __('Asc'), 'desc' => __('Desc')];
    }

    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }


    public function getSyncModeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['sync_mode']) ? $data['sync_mode'] : '');
        $list = $this->getSyncModeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getLastSyncTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['last_sync_time']) ? $data['last_sync_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getOrderDirectionTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['order_direction']) ? $data['order_direction'] : '');
        $list = $this->getOrderDirectionList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function setLastSyncTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function category() 
    { 
        return $this->belongsTo('Categoryrule', 'category_id', 'id', [], 'LEFT')->setEagerlyType(0); 
    } 
}