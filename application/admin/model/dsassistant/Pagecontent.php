<?php

namespace app\admin\model\dsassistant;

use think\Model;
use addons\dsassistant\library\CacheManager;

class Pagecontent extends Model
{
    // 表名
    protected $name = 'ds_page_content';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
    ];

    /**
     * 获取页面内容
     *
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @param string $key 内容键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getContent($page, $section, $key, $default = '')
    {
        return CacheManager::getPageContent($page, $section, $key, $default);
    }

    /**
     * 设置页面内容
     *
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @param string $key 内容键名
     * @param mixed $value 内容值
     * @param string $description 描述
     * @return boolean
     */
    public static function setContent($page, $section, $key, $value, $description = '')
    {
        return CacheManager::setPageContent($page, $section, $key, $value, $description);
    }

    /**
     * 获取页面所有内容
     *
     * @param string $page 页面标识
     * @return array
     */
    public static function getPagecontents($page)
    {
        return CacheManager::getPageContents($page);
    }

    /**
     * 获取页面区块内容
     *
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @return array
     */
    public static function getSectionContents($page, $section)
    {
        return CacheManager::getSectionContents($page, $section);
    }

    /**
     * 清除缓存
     *
     * @param string $page 页面标识，为空则清除所有页面缓存
     * @return boolean
     */
    public static function clearCache($page = '')
    {
        return CacheManager::clearPageContentCache($page);
    }

    /**
     * 刷新缓存
     *
     * @param string $page 页面标识，为空则刷新所有页面缓存
     * @return boolean
     */
    public static function refreshCache($page = '')
    {
        return CacheManager::refreshPageContentCache($page);
    }
}
