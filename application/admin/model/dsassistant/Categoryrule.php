<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\model\dsassistant;

use think\Model;
use addons\dsassistant\library\CacheManager;
use think\Log;

class Categoryrule extends Model
{





    // 表名
    protected $name = 'ds_category_rule';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];

    protected static function init() 
    { 
        self::afterInsert(function ($row) { 
            $pk = $row->getPk(); 
            $row->getQuery()->where($pk, $row[$pk])->update(['weight' => $row[$pk]]); 
        }); 
    } 

    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 写入后刷新缓存
     *
     * @param array $data 数据
     * @return bool
     */
    public static function onAfterWrite($data)
    {
        try {
            // 刷新分类规则缓存
            CacheManager::refreshCategoryRuleCache();
            Log::info('分类规则更新，已刷新缓存');
            return true;
        } catch (\Exception $e) {
            Log::error('刷新分类规则缓存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除后刷新缓存
     *
     * @param array $data 数据
     * @return bool
     */
    public static function onAfterDelete($data)
    {
        try {
            // 刷新分类规则缓存
            CacheManager::refreshCategoryRuleCache();
            Log::info('分类规则删除，已刷新缓存');
            return true;
        } catch (\Exception $e) {
            Log::error('刷新分类规则缓存失败: ' . $e->getMessage());
            return false;
        }
    }
}