<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\model\dsassistant;

use think\Model;


class WechatSession extends Model
{

    

    

    // 表名
    protected $name = 'ds_wechat_session';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'expire_time_text',
        'create_time_text',
        'update_time_text'
    ];
    

    



    public function getExpireTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['expire_time']) ? $data['expire_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['create_time']) ? $data['create_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUpdateTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['update_time']) ? $data['update_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function setExpireTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function setCreateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function setUpdateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

   
    /**
     * 保存用户会话数据
     * 
     * @param string $openid 用户OpenID
     * @param string $key 会话键名
     * @param mixed $data 会话数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function saveData($openid, $key, $data, $expire = 1800)
    {
        $session = self::where('openid', $openid)->where('key', $key)->find();
        
        if (!$session) {
            $session = new self();
            $session->openid = $openid;
            $session->key = $key;
        }
        
        $session->data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $session->expire_time = time() + $expire;
        $session->save();
        
        return true;
    }
    
    /**
     * 保存用户会话数据
     *
     * @param string $openid 用户OpenID
     * @param string $key 会话键名
     * @param mixed $data 会话数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function saveSessionData($openid, $key, $data, $expire = 1800)
    {
        $session = self::where('openid', $openid)->where('key', $key)->find();

        if (!$session) {
            $session = new self();
            $session->openid = $openid;
            $session->key = $key;
        }

        $session->session_data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $session->expire_time = time() + $expire;
        $session->save();

        return true;
    }

    /**
     * 获取用户会话数据
     *
     * @param string $openid 用户OpenID
     * @param string $key 会话键名
     * @return mixed|null
     */
    public static function getSessionData($openid, $key)
    {
        $session = self::where('openid', $openid)
            ->where('key', $key)
            ->where('expire_time', '>', time())
            ->find();

        if (!$session) {
            return null;
        }

        return json_decode($session->session_data, true);
    }

    /**
     * 清理过期会话数据
     *
     * @return int 清理的记录数
     */
    public static function cleanExpired()
    {
        return self::where('expire_time', '<', time())->delete();
    }

    /**
     * 删除用户会话数据
     *
     * @param string $openid 用户OpenID
     * @param string $key 会话键名
     * @return bool
     */
    public static function removeSessionData($openid, $key)
    {
        return self::where('openid', $openid)->where('key', $key)->delete();
    }

    /**
     * 清理指定用户的所有会话数据
     *
     * @param string $openid 用户OpenID
     * @return int 清理的记录数
     */
    public static function cleanUserSessionData($openid)
    {
        return self::where('openid', $openid)->delete();
    }
}