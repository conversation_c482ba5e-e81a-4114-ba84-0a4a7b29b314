<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\model\dsassistant;

use think\Model;
use think\Cache;

class Contentconfig extends Model
{

    

    

    // 表名
    protected $name = 'ds_content_config';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'extend_html'
    ];
    protected $type = [
        'setting' => 'json',
    ];


    public function getExtendHtmlAttr($value, $data)
    {
        $result = preg_replace_callback("/\{([a-zA-Z]+)\}/", function ($matches) use ($data) {
            if (isset($data[$matches[1]])) {
                return $data[$matches[1]];
            }
        }, $data['extend']);
        return $result;
    }

    
    /**
     * 获取配置值
     * 
     * @param string $name 配置名称
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getConfig($name, $default = '')
    {
        // 尝试从缓存获取
        $value = Cache::get('content_config_' . $name);
        if ($value !== false) {
            return $value;
        }
        
        // 从数据库获取
        $config = self::where('name', $name)->find();
        if (!$config) {
            return $default;
        }
        
        // 写入缓存
        Cache::set('content_config_' . $name, $config['value'], 3600);
        
        return $config['value'];
    }
    
    /**
     * 设置配置值
     * 
     * @param string $name 配置名称
     * @param mixed $value 配置值
     * @return boolean
     */
    public static function setConfig($name, $value)
    {
        $config = self::where('name', $name)->find();
        if ($config) {
            $config->value = $value;
            $result = $config->save();
        } else {
            $result = self::create([
                'name' => $name,
                'value' => $value,
                'group' => 'basic',
                'type' => 'string',
                'title' => '自动创建的配置'
            ]);
        }
        
        // 更新缓存
        if ($result) {
            Cache::set('content_config_' . $name, $value, 3600);
        }
        
        return $result !== false;
    }
    
    /**
     * 获取分组配置
     * 
     * @param string $group 分组名称
     * @return array
     */
    public static function getGroupConfig($group)
    {
        // 尝试从缓存获取
        $configs = Cache::get('content_config_group_' . $group);
        if ($configs !== false) {
            return $configs;
        }
        
        // 从数据库获取
        $configs = self::where('group', $group)->select();
        if (!$configs) {
            return [];
        }
        
        $result = [];
        foreach ($configs as $config) {
            $result[$config['name']] = $config['value'];
        }
        
        // 写入缓存
        Cache::set('content_config_group_' . $group, $result, 3600);
        
        return $result;
    }

    public static function getConfigAll($cache=true){
        if($cache){
            // 尝试从缓存获取
            $configs = Cache::get('content_config_all');
            if ($configs !== false) {
                return $configs;
            }
        }
        
        // 从数据库获取
        $configs = self::column('name,value');
        
        // 写入缓存
        Cache::set('content_config_all', $configs, 3600);
        
        return $configs;
    }
    
    /**
     * 清除缓存
     * 
     * @return boolean
     */
    public static function clearCache()
    {
        $configs = self::select();
        foreach ($configs as $config) {
            Cache::rm('content_config_' . $config['name']);
        }
        
        $groups = self::group('group')->column('group');
        foreach ($groups as $group) {
            Cache::rm('content_config_group_' . $group);
        }
        
        Cache::rm('content_config_all');
        return true;
    }





}