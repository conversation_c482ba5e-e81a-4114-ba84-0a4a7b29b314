<?php

namespace app\admin\model\dsassistant;

use think\Model;

class Chatlog extends Model
{
    // 表名
    protected $name = 'ds_chat_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;

    // 追加属性
    protected $append = [
        'source_text',
        'platform_text'
    ];
    
    public function getSourceList()
    {
        return ['local' => __('Local knowledge base'), 'ai' => __('AI generated')];
    }
    
    public function getSourceTextAttr($value, $data)
    {
        $value = $value ? $value : ($data['source'] ?? '');
        $list = $this->getSourceList();
        return $list[$value] ?? '';
    }
    
    public function getPlatformList()
    {
        return ['wechat' => __('WeChat'), 'miniapp' => __('Mini Program'), 'web' => __('Website')];
    }
    
    public function getPlatformTextAttr($value, $data)
    {
        $value = $value ? $value : ($data['platform'] ?? '');
        $list = $this->getPlatformList();
        return $list[$value] ?? '';
    }
}
