<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\model\dsassistant;

use think\Model;


class Userwechat extends Model
{

    

    

    // 表名
    protected $name = 'ds_user_wechat';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'subscribe_time_text'
    ];
    

    



    public function getSubscribeTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['subscribe_time']) ? $data['subscribe_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function setSubscribeTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}