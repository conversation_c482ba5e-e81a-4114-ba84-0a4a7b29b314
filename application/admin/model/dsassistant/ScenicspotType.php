<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\model\dsassistant;

use think\Model;

class ScenicspotType extends Model
{
    // 表名
    protected $name = 'ds_scenic_spot_type';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];
    
    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weight' => $row[$pk]]);
        });
    }

    public function getStatusList()
    {
        return ['normal' => __('Status normal'), 'hidden' => __('Status hidden')];
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取所有启用的景点类型
     * @return array
     */
    public static function getEnabledTypes()
    {
        return self::where('status', 'normal')
            ->order('weight DESC, id ASC')
            ->column('name,code,icon,color', 'id');
    }

    /**
     * 根据代码获取类型信息
     * @param string $code
     * @return array|null
     */
    public static function getByCode($code)
    {
        return self::where('code', $code)
            ->where('status', 'normal')
            ->find();
    }

    /**
     * 获取类型选项列表（用于下拉选择）
     * @return array
     */
    public static function getOptions()
    {
        return self::where('status', 'normal')
            ->order('weight DESC, id ASC')
            ->column('name', 'id');
    }
}
