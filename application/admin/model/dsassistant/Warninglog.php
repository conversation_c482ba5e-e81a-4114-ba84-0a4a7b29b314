<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\model\dsassistant;

use think\Model;


class Warninglog extends Model
{

    

    

    // 表名
    protected $name = 'ds_warning_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_text',
        'level_text'
    ];
    

    
    public function getTypeList()
    {
        return ['crowd' => __('Crowd'), 'weather' => __('Weather'), 'traffic' => __('Traffic'), 'other' => __('Other')];
    }

    public function getLevelList()
    {
        return ['info' => __('Info'), 'warning' => __('Warning'), 'danger' => __('Danger')];
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getLevelTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['level']) ? $data['level'] : '');
        $list = $this->getLevelList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function warning()
    {
        return $this->belongsTo('Warning', 'warning_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}