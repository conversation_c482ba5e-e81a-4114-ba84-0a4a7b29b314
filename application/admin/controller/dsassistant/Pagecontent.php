<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\controller\dsassistant;

use app\common\controller\Backend;

/**
 * 页面内容
 *
 * @icon fa fa-circle-o
 */
class Pagecontent extends Backend
{

    /**
     * Pagecontent模型对象
     * @var \app\admin\model\dsassistant\Pagecontent
     */
    protected $model = null;

    protected $noNeedRight = ['pages'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\dsassistant\Pagecontent;

    }


    public function pages()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model->where($where)
                ->field('page, count(*) as num')
                ->order($sort, $order)
                ->group('page')
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch('index');
    }

}