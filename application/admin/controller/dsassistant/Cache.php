<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\controller\dsassistant;

use app\common\controller\Backend;
use think\Exception;
use think\Log;
use addons\dsassistant\library\CacheManager;

/**
 * 缓存管理
 *
 * @icon fa fa-refresh
 */
class Cache extends Backend
{
    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = ['index'];

    /**
     * 查看缓存
     */
    public function index()
    {
        // 获取缓存统计信息
        $cacheStats = CacheManager::getCacheStats();

        // 准备视图数据
        $cacheData = [
            'keyword_map' => [
                'status' => $cacheStats['keyword_map']['exists'] ? 'success' : 'danger',
                'status_text' => $cacheStats['keyword_map']['exists'] ? '已加载' : '未加载',
                'count' => $cacheStats['keyword_map']['count'],
            ],
            'category_rule' => [
                'status' => $cacheStats['category_rule']['exists'] ? 'success' : 'danger',
                'status_text' => $cacheStats['category_rule']['exists'] ? '已加载' : '未加载',
                'count' => $cacheStats['category_rule']['count'],
            ],
            'page_content' => [
                'status' => $cacheStats['page_content']['exists'] ? 'success' : 'danger',
                'status_text' => $cacheStats['page_content']['exists'] ? '已加载' : '未加载',
                'count' => $cacheStats['page_content']['count'],
            ]
        ];

        // 传递数据到视图
        $this->view->assign('cacheData', $cacheData);
        $this->view->assign('cacheVersion', $cacheStats['version']['value'] ?: '未知');

        return $this->view->fetch();
    }

    /**
     * 刷新所有缓存
     */
    public function refresh()
    {
        if ($this->request->isPost()) {
            $errorMsg = '';

            try {
                CacheManager::refreshAllCache();
            } catch (Exception $e) {
                Log::error('缓存刷新失败: ' . $e->getMessage());
                $errorMsg = $e->getMessage();
            }

            // try-catch块外部进行响应
            if (!empty($errorMsg)) {
                $this->error('缓存刷新失败: ' . $errorMsg);
            } else {
                $this->success('缓存刷新成功');
            }
        }

        return $this->view->fetch();
    }

    /**
     * 刷新关键词映射缓存
     */
    public function refreshKeywordMap()
    {
        if ($this->request->isPost()) {
            $data = [];
            $errorMsg = '';

            try {
                $data = CacheManager::refreshKeywordMapCache();
            } catch (Exception $e) {
                Log::error('关键词映射缓存刷新失败: ' . $e->getMessage());
                $errorMsg = $e->getMessage();
            }

            // try-catch块外部进行响应
            if (!empty($errorMsg)) {
                $this->error('关键词映射缓存刷新失败: ' . $errorMsg);
            } else {
                $this->success('关键词映射缓存刷新成功，共 ' . count($data) . ' 条记录');
            }
        }

        return $this->view->fetch();
    }

    /**
     * 刷新分类规则缓存
     */
    public function refreshCategoryRule()
    {
        if ($this->request->isPost()) {
            $data = [];
            $errorMsg = '';

            try {
                $data = CacheManager::refreshCategoryRuleCache();
            } catch (Exception $e) {
                Log::error('分类规则缓存刷新失败: ' . $e->getMessage());
                $errorMsg = $e->getMessage();
            }

            // try-catch块外部进行响应
            if (!empty($errorMsg)) {
                $this->error('分类规则缓存刷新失败: ' . $errorMsg);
            } else {
                $this->success('分类规则缓存刷新成功，共 ' . count($data) . ' 条记录');
            }
        }

        return $this->view->fetch();
    }

    /**
     * 刷新页面内容缓存
     */
    public function refresh_page_content()
    {
        if ($this->request->isPost()) {
            $errorMsg = '';

            try {
                CacheManager::refreshPageContentCache();
                $pageCount = \think\Db::name('ds_page_content')->group('page')->count();
                $contentCount = \think\Db::name('ds_page_content')->count();
            } catch (\think\Exception $e) {
                \think\Log::error('页面内容缓存刷新失败: ' . $e->getMessage());
                $errorMsg = $e->getMessage();
            }

            // try-catch块外部进行响应
            if (!empty($errorMsg)) {
                $this->error('页面内容缓存刷新失败: ' . $errorMsg);
            } else {
                $this->success('页面内容缓存刷新成功，共 ' . $pageCount . ' 个页面，' . $contentCount . ' 条内容');
            }
        }

        return $this->view->fetch();
    }

    /**
     * 清除所有缓存
     */
    public function clear()
    {
        if ($this->request->isPost()) {
            $errorMsg = '';

            try {
                CacheManager::clearAllCache();
            } catch (\think\Exception $e) {
                \think\Log::error('缓存清除失败: ' . $e->getMessage());
                $errorMsg = $e->getMessage();
            }

            // try-catch块外部进行响应
            if (!empty($errorMsg)) {
                $this->error('缓存清除失败: ' . $errorMsg);
            } else {
                $this->success('缓存清除成功');
            }
        }

        return $this->view->fetch();
    }
}
