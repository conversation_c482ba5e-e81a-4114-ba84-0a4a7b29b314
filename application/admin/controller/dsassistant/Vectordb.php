<?php

namespace app\admin\controller\dsassistant;

use app\common\controller\Backend;
use think\Exception;
use addons\dsassistant\library\vectordb\VectorDBFactory;
use addons\dsassistant\library\VectorSearchFactory;
use app\admin\model\dsassistant\Contentconfig;

/**
 * 向量数据库管理
 *
 * @icon fa fa-database
 */
class Vectordb extends Backend
{
    /**
     * 向量搜索实例
     */
    protected $vectorSearch = null;

    /**
     * 向量数据库实例
     */
    protected $vectorDB = null;

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->vectorSearch =  VectorSearchFactory::getInstance();
        $this->vectorDB = VectorDBFactory::getInstance();
    }

    /**
     * 查看
     */
    public function index()
    {
        $this->view->assign('title', '向量数据库管理');

        // 获取向量数据库类型
        $addonConfig = Contentconfig::getGroupConfig('vectordb')  ;
        $dbType = $addonConfig['vectordb_type'] ?? 'mysql';

        // 获取固定集合信息
        try {
            // 固定集合名称为 ds_knowledge_vectors
            $collectionName = 'ds_knowledge_vectors';
            $collectionInfo = [];

            // 获取集合信息
            $info = $this->vectorDB->getCollectionInfo($collectionName);
            $collectionInfo[] = $info;

            $this->view->assign('dbType', $dbType);
            $this->view->assign('collections', $collectionInfo);
        } catch (Exception $e) {
            \think\Log::error('获取向量数据库信息失败: ' . $e->getMessage());
            // 如果获取失败，提供一个基本信息
            $this->view->assign('dbType', $dbType);
            $this->view->assign('collections', [
                [
                    'name' => 'ds_knowledge_vectors',
                    'count' => '未知',
                    'engine' => $dbType,
                    'dimension' => '未知'
                ]
            ]);
        }

        return $this->view->fetch();
    }

    /**
     * 重建索引
     */
    public function rebuild()
    {
        if ($this->request->isPost()) {
            $count = $this->vectorSearch->indexKnowledgeBase();
            $this->success("成功索引 " . $count . " 条知识");
        }
        return $this->view->fetch();
    }

    /**
     * 测试向量搜索
     */
    public function test()
    {
        if ($this->request->isPost()) {
            $question = $this->request->post('question');

            $results = $this->vectorSearch->search($question, 5);

            $this->success('', '', $results);
        }

        return $this->view->fetch();
    }

    /**
     * 性能测试
     */
    public function benchmark()
    {
        if ($this->request->isPost()) {
            $testQuestions = [
                '景区门票多少钱？',
                '景区开放时间是几点到几点？',
                '景区有哪些著名景点？',
                '如何前往景区？',
                '景区内有餐厅吗？',
                '青山瀑布在哪里？',
                '翠谷幽林有什么特色？',
                '天空栈道需要另外收费吗？',
                '古镇风情街有什么好吃的？',
                '百花园什么时候花最多？'
            ];

            $results = [];
            $totalTime = 0;

            foreach ($testQuestions as $question) {
                $startTime = microtime(true);
                $searchResults = $this->vectorSearch->search($question, 3);
                $endTime = microtime(true);
                $duration = round(($endTime - $startTime) * 1000, 2); // 毫秒
                $totalTime += $duration;

                $bestMatch = !empty($searchResults) ? $searchResults[0]['question'] : '无匹配';
                $score = !empty($searchResults) ? $searchResults[0]['score'] : 0;

                $results[] = [
                    'question' => $question,
                    'best_match' => $bestMatch,
                    'score' => $score,
                    'time' => $duration . 'ms'
                ];
            }

            $avgTime = round($totalTime / count($testQuestions), 2);

            $this->success('平均查询时间: ' . $avgTime . 'ms', '', [
                'avg_time' => $avgTime,
                'results' => $results
            ]);
        }

        return $this->view->fetch();
    }

    /**
     * 配置向量数据库
     */
    public function config()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();

            $config = Contentconfig::getGroupConfig('vectordb')  ;;

            // 更新向量数据库配置
            $config['vectordb_type'] = $params['type'];

            switch ($params['type']) {
                case 'mysql':
                    // MySQL不需要额外配置
                    $config['vectordb_config'] = [];
                    break;

                case 'tencent':
                    $config['vectordb_config'] = [
                        'secretId' => $params['tencent_secret_id'],
                        'secretKey' => $params['tencent_secret_key'],
                        'region' => $params['tencent_region'],
                        'databaseId' => $params['tencent_database_id']
                    ];
                    break;

                case 'aliyun':
                    $config['vectordb_config'] = [
                        'accessKeyId' => $params['aliyun_access_key_id'],
                        'accessKeySecret' => $params['aliyun_access_key_secret'],
                        'endpoint' => $params['aliyun_endpoint'],
                        'instanceId' => $params['aliyun_instance_id']
                    ];
                    break;

                case 'baidu':
                    $config['vectordb_config'] = [
                        'apiKey' => $params['baidu_api_key'],
                        'secretKey' => $params['baidu_secret_key'],
                        'endpoint' => $params['baidu_endpoint']
                    ];
                    break;

                case 'huawei':
                    $config['vectordb_config'] = [
                        'ak' => $params['huawei_ak'],
                        'sk' => $params['huawei_sk'],
                        'projectId' => $params['huawei_project_id'],
                        'endpoint' => $params['huawei_endpoint']
                    ];
                    break;
            }

            // 保存配置
            set_addon_config('dsassistant', $config);

            // 重置向量数据库实例
            VectorDBFactory::resetInstance();

            $this->success('配置已保存');
        }

        $config = Contentconfig::getGroupConfig('vectordb')  ;
        $this->view->assign('config', $config);

        return $this->view->fetch();
    }
}
