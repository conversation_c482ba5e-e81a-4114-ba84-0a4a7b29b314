<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\controller\dsassistant;

use app\common\controller\Backend;
use addons\dsassistant\library\ConfigHelper;
use think\Exception;
/**
 * 内容配置
 *
 * @icon fa fa-cogs
 */
class Contentconfig extends Backend
{

    /**
     * Contentconfig模型对象
     * @var \app\admin\model\dsassistant\Contentconfig
     */
    protected $model = null;

    protected $noNeedRight = ['check', 'rulelist', 'selectpage'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\dsassistant\Contentconfig;
    }



    /**
     * 查看
     */
    public function index()
    {
        $argList = [];
        $groupList = get_addon_config('dsassistant')['group'];
        foreach ($groupList as $k => $v) {
            $argList[$k]['name'] = $k;
            $argList[$k]['title'] = $v;
            $argList[$k]['list'] = [];
        }
        $dataList = $this->model->all();
        foreach ($dataList as $k => $v) {
            if (!isset($argList[$v['group']])) {
                continue;
            }
            $value = $v->toArray();
            $value['title'] = __($value['title']);
            if (in_array($value['type'], ['select', 'selects', 'checkbox', 'radio'])) {
                $value['value'] = explode(',', $value['value']);
            }
            $value['content'] = json_decode($value['content'], true);
            if (in_array($value['name'], ['categorytype', 'configgroup', 'attachmentcategory'])) {
                $dictValue = (array)json_decode($value['value'], true);
                foreach ($dictValue as $index => &$item) {
                    $item = __($item);
                }
                unset($item);
                $value['value'] = json_encode($dictValue, JSON_UNESCAPED_UNICODE);
            }
            $value['tip'] = htmlspecialchars($value['tip']);
            if ($value['name'] == 'cdnurl') {
                //cdnurl不支持在线修改
                continue;
            }
            $argList[$v['group']]['list'][] = $value;
        }
        $index = 0;
        foreach ($argList as $k => &$v) {
            $v['active'] = !$index ? true : false;
            $index++;
        }
        $this->view->assign('argList', $argList);
        $this->view->assign('typeList', ConfigHelper::getTypeList());
        $this->view->assign('ruleList', ConfigHelper::getRegexList());
        $this->view->assign('groupList', $groupList);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if (!config('app_debug')) {
            $this->error(__('Only work at development environment'));
        }
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a", [], 'trim');
            if ($params) {
                foreach ($params as $k => &$v) {
                    $v = is_array($v) && $k !== 'setting' ? implode(',', $v) : $v;
                }
                if (in_array($params['type'], ['select', 'selects', 'checkbox', 'radio', 'array'])) {
                    $params['content'] = json_encode(ConfigHelper::decode($params['content']), JSON_UNESCAPED_UNICODE);
                } else {
                    $params['content'] = '';
                }
                try {
                    $result = $this->model->create($params);
                } catch (Exception $e) {
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    try {
                        //ConfigHelper::config_recache();
                    } catch (Exception $e) {
                        $this->error($e->getMessage());
                    }
                    $this->success();
                } else {
                    $this->error($this->model->getError());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     * @param null $ids
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
            $row = $this->request->post("row/a", [], 'trim');
            if ($row) {
                $configList = [];
                foreach ($this->model->all() as $v) {
                    if (isset($row[$v['name']])) {
                        $value = $row[$v['name']];
                        if (is_array($value) && isset($value['field'])) {
                            $value = json_encode(ConfigHelper::getArrayData($value), JSON_UNESCAPED_UNICODE);
                        } else {
                            $value = is_array($value) ? implode(',', $value) : $value;
                        }
                        $v['value'] = $value;
                        $configList[] = $v->toArray();
                    }
                }
                try {
                    $this->model->allowField(true)->saveAll($configList);
                } catch (Exception $e) {
                    $this->error($e->getMessage());
                }
                // try {
                //     //ConfigHelper::config_recache();
                // } catch (Exception $e) {
                //     $this->error($e->getMessage());
                // }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
    }

    /**
     * 删除
     * @param string $ids
     */
    public function del($ids = "")
    {
        if (!config('app_debug')) {
            $this->error(__('Only work at development environment'));
        }
        $name = $this->request->post('name');
        $config = $this->model->get(['name' => $name]);
        if ($name && $config) {
            try {
                $config->delete();
                //ConfigHelper::config_recache();
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
            $this->success();
        } else {
            $this->error(__('Invalid parameters'));
        }
    }

    /**
     * 检测配置项是否存在
     * @internal
     */
    public function check()
    {
        $params = $this->request->post("row/a");
        if ($params) {
            $config = $this->model->get($params);
            if (!$config) {
                $this->success();
            } else {
                $this->error(__('Name already exist'));
            }
        } else {
            $this->error(__('Invalid parameters'));
        }
    }

    /**
     * 规则列表
     * @internal
     */
    public function rulelist()
    {
        //主键
        $primarykey = $this->request->request("keyField");
        //主键值
        $keyValue = $this->request->request("keyValue", "");

        $keyValueArr = array_filter(explode(',', $keyValue));
        $regexList = \app\common\model\Config::getRegexList();
        $list = [];
        foreach ($regexList as $k => $v) {
            if ($keyValueArr) {
                if (in_array($k, $keyValueArr)) {
                    $list[] = ['id' => $k, 'name' => $v];
                }
            } else {
                $list[] = ['id' => $k, 'name' => $v];
            }
        }
        return json(['list' => $list]);
    }


    public function selectpage()
    {
        $id = $this->request->get("id/d");
        $config = \app\admin\model\dsassistant\Contentconfig::get($id);
        if (!$config) {
            $this->error(__('Invalid parameters'));
        }
        $setting = $config['setting'];
        //自定义条件
        $custom = isset($setting['conditions']) ? (array)json_decode($setting['conditions'], true) : [];
        $custom = array_filter($custom);

        $this->request->request(['showField' => $setting['field'], 'keyField' => $setting['primarykey'], 'custom' => $custom, 'searchField' => [$setting['field'], $setting['primarykey']]]);
        $this->model = \think\Db::connect()->setTable($setting['table']);
        return parent::selectpage();
    }

}