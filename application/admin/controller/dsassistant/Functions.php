<?php

namespace app\admin\controller\dsassistant;

use app\admin\model\dsassistant\Contentconfig;
use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\Log;
use addons\dsassistant\library\FunctionLoader;

/**
 * 函数管理
 *
 * @icon fa fa-code
 */
class Functions extends Backend
{
    /**
     * 函数加载器
     * @var FunctionLoader
     */
    protected $functionLoader = null;

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $this->functionLoader = new FunctionLoader();
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $functions = $this->functionLoader->getFunctionsList(true);
            $total = count($functions);
            $result = ['total' => $total, 'rows' => $functions];
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 启用/禁用函数
     */
    public function toggle()
    {
        $name = $this->request->post('name');
        $enabled = (bool)$this->request->post('enabled');

        if (empty($name)) {
            $this->error('函数名称不能为空');
        }

        try {
            $result = $this->functionLoader->updateFunctionStatus($name, $enabled);
            if ($result) {
                $this->success('操作成功');
            } else {
                $this->error('操作失败');
            }
        } catch (Exception $e) {
            Log::error('函数状态更新失败: ' . $e->getMessage());
            $this->error($e->getMessage());
        }
    }

    /**
     * 查看函数详情
     */
    public function detail()
    {
        $name = $this->request->get('name');
        
        if (empty($name)) {
            $this->error('函数名称不能为空');
        }
        
        // 获取所有函数
        $functions = $this->functionLoader->getAllFunctions(true);
        
        if (!isset($functions[$name])) {
            $this->error('函数不存在');
        }
        
        $function = $functions[$name];
        
        // 确定函数来源
        $source = 'core';
        $files = glob(ADDON_PATH . 'dsassistant/functions/custom/*.php');
        foreach ($files as $file) {
            $func = include $file;
            if (is_array($func) && isset($func['name']) && $func['name'] === $name) {
                $source = 'custom';
                break;
            }
        }
        
        $function['source'] = $source;
        
        $this->view->assign('function', $function);
        return $this->view->fetch();
    }
}
