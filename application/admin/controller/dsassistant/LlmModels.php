<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\controller\dsassistant;

use addons\dsassistant\library\service\LlmModelsService;
use app\common\controller\Backend;

/**
 * LLM模型配置管理
 *
 * @icon fa fa-circle-o
 */
class LlmModels extends Backend
{

    /**
     * LlmModels模型对象
     * @var \app\admin\model\dsassistant\LlmModels
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\dsassistant\LlmModels;
        LlmModelsService::registDefaultChanged();
    }

}