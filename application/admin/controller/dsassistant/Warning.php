<?php

namespace app\admin\controller\dsassistant;

use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\Log;

// 定义项目根路径常量（如果未定义）
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(dirname(dirname(dirname(__FILE__))))) . '/');
}

/**
 * 预警管理
 *
 * @icon fa fa-exclamation-triangle
 */
class Warning extends Backend
{
    /**
     * Warning模型对象
     * @var \app\admin\model\dsassistant\Warning
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\dsassistant\Warning;
        $this->view->assign("typeList", $this->model->getTypeList());
        $this->view->assign("levelList", $this->model->getLevelList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }


    /**
     * 推送预警信息
     */
    public function push()
    {
        $ids = $this->request->param('ids/a');
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
            return;
        }

        $list = $this->model->where('id', 'in', $ids)->select();
        if (empty($list)) {
            $this->error(__('No results were found'));
            return;
        }

        $errorMsg = '';
        $totalUsers = 0;

        try {
            foreach ($list as $item) {
                // 记录推送日志
                Db::name('ds_warning_log')->insert([
                    'warning_id' => $item->id,
                    'title' => $item->title,
                    'content' => $item->content,
                    'type' => $item->type,
                    'level' => $item->level,
                    'createtime' => time()
                ]);

                // 获取当前在景区内的用户数量
                $totalUsers = Db::name('ds_user_location_pool')
                    ->where('in_scenic', 1)
                    ->where('expire_time', '>', time())
                    ->count();

                // 触发异步推送任务
                $this->triggerPushTask($item->id);
            }
        } catch (Exception $e) {
            Log::error("推送预警信息异常: " . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success(__('Push task created successfully') . "，目标用户数：{$totalUsers}");
        }
    }

    /**
     * 触发推送任务
     */
    protected function triggerPushTask($warningId)
    {
        // 使用后台任务执行命令
        $command = "php " . ROOT_PATH . "think dsassistant:warning-push --task=push_warning --warning_id={$warningId} > /dev/null 2>&1 &";

        // 执行命令
        if (function_exists('exec')) {
            exec($command);
        } elseif (function_exists('shell_exec')) {
            shell_exec($command);
        } elseif (function_exists('popen')) {
            $handle = popen($command, 'r');
            if ($handle) {
                pclose($handle);
            }
        } else {
            Log::error('无法执行后台命令，请检查PHP配置');
        }

        return true;
    }
}
