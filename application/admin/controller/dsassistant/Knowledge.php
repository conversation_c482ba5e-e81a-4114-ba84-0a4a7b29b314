<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\controller\dsassistant;

use addons\dsassistant\library\service\CategoryruleService;
use addons\dsassistant\library\service\DictService;
use addons\dsassistant\library\service\KnowledgeService;
use app\common\controller\Backend;

/**
 * 知识库
 *
 * @icon fa fa-circle-o
 */
class Knowledge extends Backend
{

    /**
     * Knowledge模型对象
     * @var \app\admin\model\dsassistant\Knowledge
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\dsassistant\Knowledge;
        $this->assign("statusList", $this->model->getStatusList());
        $contentTypeList = DictService::dicts('content_type');
        $sourceTypeList = DictService::dicts('source_type');
        $this->assign("contentTypeList", $contentTypeList);
        $this->assign("sourceTypeList", $sourceTypeList);
        $this->assignconfig("contentTypeList", $contentTypeList);
        $this->assignconfig("sourceTypeList", $sourceTypeList);
        $this->assignconfig("categoryruleList", CategoryruleService::getCategoryruleList());
    }

    public function import()
    {
        parent::import();
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['category'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('category')->visible(['category']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    public function add()
    {
        if($this->request->isPost()){
            KnowledgeService::registerEventAfterInsert();
        }
        return parent::add();
    }

    public function edit($ids = "")
    {
        if($this->request->isPost()){
            KnowledgeService::registerEventAfterUpdate();
        }
        return parent::edit($ids);
    }

    public function del($ids = "")
    {
        if($this->request->isPost()){
            KnowledgeService::registerEventAfterDelete();
        }
        return parent::del($ids);
    }
}