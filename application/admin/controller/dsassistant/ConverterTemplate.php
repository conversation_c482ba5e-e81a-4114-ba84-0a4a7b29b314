<?php
// +----------------------------------------------------------------------
// | DSASSISTANT  [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.addondev.cn All rights reserved.
// +----------------------------------------------------------------------
// | Author: dungang
// +----------------------------------------------------------------------

namespace app\admin\controller\dsassistant;

use addons\dsassistant\library\service\CategoryruleService;
use addons\dsassistant\library\service\DictService;
use app\common\controller\Backend;
use think\Db;

/**
 * 转换模板
 *
 * @icon fa fa-circle-o
 */
class ConverterTemplate extends Backend
{

    /**
     * DsConverterTemplate模型对象
     * @var \app\admin\model\dsassistant\DsConverterTemplate
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\dsassistant\ConverterTemplate;
        $this->assign("syncModeList", $this->model->getSyncModeList());
        $this->assign("orderDirectionList", $this->model->getOrderDirectionList());
        $this->assign("statusList", $this->model->getStatusList());
        $contentTypeList = DictService::dicts('content_type');
        $sourceTypeList = DictService::dicts('source_type');
        $this->assign("contentTypeList", $contentTypeList);
        $this->assign("sourceTypeList", $sourceTypeList);
        $this->assignconfig("contentTypeList", $contentTypeList);
        $this->assignconfig("sourceTypeList", $sourceTypeList);
        $this->assignconfig("categoryruleList", CategoryruleService::getCategoryruleList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 执行转换
     */
    public function execute($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $force = $this->request->param('force/b', false);

        // 执行转换
        $result = \addons\dsassistant\library\TemplateConverter::convert($ids, $force);

        if ($result['code']) {
            $this->success($result['msg'], null, $result['data']);
        } else {
            $this->error($result['msg']);
        }
    }

    /**
     * 批量执行转换
     */
    public function executeAll()
    {
        $ids = $this->request->param('ids');
        $force = $this->request->param('force/b', false);

        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $ids = explode(',', $ids);

        // 批量执行转换
        $results = \addons\dsassistant\library\TemplateConverter::batchConvert($ids, $force);

        $success = 0;
        $error = 0;
        $messages = [];

        foreach ($results as $id => $result) {
            if ($result['code']) {
                $success++;
                $messages[] = "模板 {$id}: " . $result['msg'];
            } else {
                $error++;
                $messages[] = "模板 {$id}: " . $result['msg'];
            }
        }

        if ($error == 0) {
            $this->success(sprintf(__('Batch execution completed: %s success, %s error'), $success, $error), null, ['messages' => $messages]);
        } else {
            $this->error(sprintf(__('Batch execution completed with errors: %s success, %s error'), $success, $error), null, ['messages' => $messages]);
        }
    }

    /**
     * 初始化模板
     */
    public function initialize()
    {
        if ($this->request->isPost()) {
            $templates = $this->getInitialTemplates();

            $success = 0;
            $error = 0;

            foreach ($templates as $template) {
                // 检查是否已存在相同名称的模板
                $exists = $this->model->where('name', $template['name'])
                    ->where('source_type', $template['source_type'])
                    ->where('content_type', $template['content_type'])
                    ->find();

                if ($exists) {
                    // 更新模板
                    $result = $exists->save($template);
                } else {
                    // 创建模板
                    $template = new \app\admin\model\dsassistant\ConverterTemplate($template);
                    $result = $template->save();
                }

                if ($result !== false) {
                    $success++;
                } else {
                    $error++;
                }
            }

            if ($error == 0) {
                $this->success(sprintf(__('Initialize completed: %s templates created/updated'), $success));
            } else {
                $this->error(sprintf(__('Initialize completed with errors: %s success, %s error'), $success, $error));
            }
        }

        return $this->view->fetch();
    }

    /**
     * 获取初始化模板数据
     * @return array 初始模板数据
     */
    protected function getInitialTemplates()
    {
        return [
            // 景区基本描述模板
            [
                'name' => '景区基本描述',
                'source_table' => 'ds_scenic',
                'target_table' => 'ds_knowledge',
                'source_type' => 'scenic',
                'content_type' => 'description',
                'template_content' => json_encode([
                    'title' => '{name}',
                    'content' => "# {name}\n\n{description}\n\n" .
                                 "## 区域类型\n" .
                                 "{type}\n\n",
                    'tags' => '{name},景区,描述',
                    'weight' => 100,
                    'status' => 'normal',
                    'has_vector' => 0
                ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
                'sync_mode' => 'increment',
                'sync_field' => 'updatetime',
                'where_condition' => 'status="normal"',
                'order_field' => 'id',
                'order_direction' => 'asc',
                'batch_size' => 100,
                'status' => 'normal'
            ],

            // 景区开放信息模板
            [
                'name' => '景区开放信息',
                'source_table' => 'ds_scenic',
                'target_table' => 'ds_knowledge',
                'source_type' => 'scenic',
                'content_type' => 'info',
                'template_content' => json_encode([
                    'title' => '{name}开放信息',
                    'content' => "# {name}开放信息\n\n" .
                                 "## 开放时间\n" .
                                 "景区全天开放，建议游玩时间：上午9:00至下午5:00\n\n" .
                                 "## 门票信息\n" .
                                 "请咨询景区官方渠道获取最新门票信息。\n\n" .
                                 "## 注意事项\n" .
                                 "- 请遵守景区规定，爱护景区环境\n" .
                                 "- 请勿在景区内吸烟或丢弃垃圾\n" .
                                 "- 请注意自身安全，看管好随身物品\n",
                    'tags' => '{name},景区,开放时间,门票,注意事项',
                    'weight' => 90,
                    'status' => 'normal',
                    'has_vector' => 0,
                    'structured_data' => [
                        'opening_hours' => [
                            'weekday' => '全天开放',
                            'weekend' => '全天开放',
                            'holiday' => '全天开放'
                        ],
                        'ticket_info' => [
                            'note' => '请咨询景区官方渠道获取最新门票信息'
                        ]
                    ]
                ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
                'sync_mode' => 'increment',
                'sync_field' => 'updatetime',
                'where_condition' => 'status="normal"',
                'order_field' => 'id',
                'order_direction' => 'asc',
                'batch_size' => 100,
                'status' => 'normal'
            ],

            // 景区位置交通模板
            [
                'name' => '景区位置交通',
                'source_table' => 'ds_scenic',
                'target_table' => 'ds_knowledge',
                'source_type' => 'scenic',
                'content_type' => 'location',
                'template_content' => json_encode([
                    'title' => '{name}位置与交通',
                    'content' => "# {name}位置与交通\n\n" .
                                 "## 地理位置\n" .
                                 "- 纬度：{center_lat}\n" .
                                 "- 经度：{center_lng}\n\n" .
                                 "## 交通方式\n" .
                                 "### 公共交通\n" .
                                 "可乘坐公交车前往景区，具体线路请咨询当地交通部门。\n\n" .
                                 "### 自驾路线\n" .
                                 "可导航至\"{name}\"，景区周边设有停车场。\n\n",
                    'tags' => '{name},景区,位置,交通,地图',
                    'weight' => 80,
                    'status' => 'normal',
                    'has_vector' => 0,
                    'structured_data' => [
                        'location' => [
                            'latitude' => '{center_lat}',
                            'longitude' => '{center_lng}'
                        ],
                        'transportation' => [
                            'public' => '可乘坐公交车前往景区',
                            'driving' => '可导航至"{name}"'
                        ]
                    ]
                ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
                'sync_mode' => 'increment',
                'sync_field' => 'updatetime',
                'where_condition' => 'status="normal"',
                'order_field' => 'id',
                'order_direction' => 'asc',
                'batch_size' => 100,
                'status' => 'normal'
            ],

            // 景点基本描述模板
            [
                'name' => '景点基本描述',
                'source_table' => 'ds_scenic_spot',
                'target_table' => 'ds_knowledge',
                'source_type' => 'scenicspot',
                'content_type' => 'description',
                'template_content' => json_encode([
                    'title' => '{name}',
                    'content' => "# {name}\n\n" .
                                 "{description}\n\n" .
                                 "## 位置\n" .
                                 "{address}\n\n" .
                                 "## 地理坐标\n" .
                                 "- 经度：{longitude}\n" .
                                 "- 纬度：{latitude}\n\n",
                    'tags' => '{name},景点,描述',
                    'weight' => 100,
                    'status' => 'normal',
                    'has_vector' => 0
                ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
                'sync_mode' => 'increment',
                'sync_field' => 'updatetime',
                'where_condition' => 'status="normal"',
                'order_field' => 'id',
                'order_direction' => 'asc',
                'batch_size' => 100,
                'status' => 'normal'
            ],

            // 景点游玩提示模板
            [
                'name' => '景点游玩提示',
                'source_table' => 'ds_scenic_spot',
                'target_table' => 'ds_knowledge',
                'source_type' => 'scenicspot',
                'content_type' => 'guide',
                'template_content' => json_encode([
                    'title' => '{name}游玩提示',
                    'content' => "# {name}游玩提示\n\n" .
                                 "{tips}\n\n" .
                                 "## 通用提示\n" .
                                 "- 请保管好个人财物\n" .
                                 "- 爱护景区环境，不要乱扔垃圾\n" .
                                 "- 遵守景区规定，文明游览\n",
                    'tags' => '{name},景点,游玩提示,注意事项,指南',
                    'weight' => 90,
                    'status' => 'normal',
                    'has_vector' => 0
                ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
                'sync_mode' => 'increment',
                'sync_field' => 'updatetime',
                'where_condition' => 'status="normal" AND tips!=""',
                'order_field' => 'id',
                'order_direction' => 'asc',
                'batch_size' => 100,
                'status' => 'normal'
            ],

            // 景点票价信息模板
            [
                'name' => '景点票价信息',
                'source_table' => 'ds_scenic_spot',
                'target_table' => 'ds_knowledge',
                'source_type' => 'scenicspot',
                'content_type' => 'data',
                'template_content' => json_encode([
                    'title' => '{name}票价信息',
                    'content' => "# {name}票价信息\n\n" .
                                 "## 票价详情\n" .
                                 "{ticket_price}\n\n" .
                                 "## 购票提示\n" .
                                 "- 建议提前购票，避免排队\n" .
                                 "- 部分人群可能享有优惠票价，请携带相关证件\n" .
                                 "- 具体票价可能会有变动，请以景区现场公示为准\n",
                    'tags' => '{name},景点,票价,门票,价格',
                    'weight' => 85,
                    'status' => 'normal',
                    'has_vector' => 0
                ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
                'sync_mode' => 'increment',
                'sync_field' => 'updatetime',
                'where_condition' => 'status="normal" AND ticket_price!=""',
                'order_field' => 'id',
                'order_direction' => 'asc',
                'batch_size' => 100,
                'status' => 'normal'
            ],

            // 景点开放时间模板
            [
                'name' => '景点开放时间',
                'source_table' => 'ds_scenic_spot',
                'target_table' => 'ds_knowledge',
                'source_type' => 'scenicspot',
                'content_type' => 'data',
                'template_content' => json_encode([
                    'title' => '{name}开放时间',
                    'content' => "# {name}开放时间\n\n" .
                                 "## 开放时间详情\n" .
                                 "{opening_hours}\n\n" .
                                 "## 参观建议\n" .
                                 "- 建议提前规划行程，合理安排参观时间\n" .
                                 "- 避开高峰时段，提升游览体验\n" .
                                 "- 具体开放时间可能会有调整，请以景区现场公示为准\n",
                    'tags' => '{name},景点,开放时间,营业时间,参观时间',
                    'weight' => 80,
                    'status' => 'normal',
                    'has_vector' => 0
                ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
                'sync_mode' => 'increment',
                'sync_field' => 'updatetime',
                'where_condition' => 'status="normal" AND opening_hours!=""',
                'order_field' => 'id',
                'order_direction' => 'asc',
                'batch_size' => 100,
                'status' => 'normal'
            ]
        ];
    }


}