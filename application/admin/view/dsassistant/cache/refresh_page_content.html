<form id="refresh-pagecontent-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">操作说明:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="alert alert-info">
                刷新页面内容缓存将重新从数据库加载所有页面内容数据，并更新缓存。<br>
                这个操作通常在修改页面内容数据后执行，以确保缓存与数据库同步。<br>
                页面内容包括网站文案、标题、描述等静态内容，刷新后将立即生效。
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed">刷新页面内容缓存</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
