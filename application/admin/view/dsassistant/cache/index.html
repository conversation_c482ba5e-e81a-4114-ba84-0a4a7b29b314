<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>缓存管理</em>用于管理系统中的各种缓存数据</div>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h4>缓存管理说明</h4>
                            <p>本系统使用缓存来提高性能，主要包括以下几种缓存：</p>
                            <ul>
                                <li><strong>关键词映射缓存</strong>：存储关键词与分类的映射关系，用于问答匹配</li>
                                <li><strong>分类规则缓存</strong>：存储分类规则数据，用于问题分类</li>
                                <li><strong>页面内容缓存</strong>：存储网站文案内容，提高页面加载速度</li>
                            </ul>
                            <p>您可以通过以下操作管理系统缓存：</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 刷新所有缓存 -->
                    <div class="col-md-6 col-lg-3">
                        <div class="panel panel-warning">
                            <div class="panel-heading">
                                <h3 class="panel-title">刷新所有缓存</h3>
                            </div>
                            <div class="panel-body">
                                <p>重新从数据库加载所有缓存数据，包括关键词映射和分类规则。</p>
                                <a href="{:url('dsassistant/cache/refresh')}" class="btn btn-success btn-block btn-dialog" title="刷新所有缓存">
                                    <i class="fa fa-refresh"></i> 刷新所有缓存
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 刷新关键词缓存 -->
                    <div class="col-md-6 col-lg-3">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">刷新关键词缓存</h3>
                            </div>
                            <div class="panel-body">
                                <p>仅重新加载关键词映射数据，适用于修改关键词后使用。</p>
                                <a href="{:url('dsassistant/cache/refreshkeywordmap')}" class="btn btn-primary btn-block btn-dialog" title="刷新关键词缓存">
                                    <i class="fa fa-key"></i> 刷新关键词缓存
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 刷新分类规则缓存 -->
                    <div class="col-md-6 col-lg-3">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">刷新分类规则缓存</h3>
                            </div>
                            <div class="panel-body">
                                <p>仅重新加载分类规则数据，适用于修改分类规则后使用。</p>
                                <a href="{:url('dsassistant/cache/refreshcategoryrule')}" class="btn btn-primary btn-block btn-dialog" title="刷新分类规则缓存">
                                    <i class="fa fa-sitemap"></i> 刷新分类规则缓存
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 刷新页面内容缓存 -->
                    <div class="col-md-6 col-lg-3">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">刷新页面内容缓存</h3>
                            </div>
                            <div class="panel-body">
                                <p>仅重新加载页面内容数据，适用于修改网站文案后使用。</p>
                                <a href="{:url('dsassistant/cache/refresh_page_content')}" class="btn btn-primary btn-block btn-dialog" title="刷新页面内容缓存">
                                    <i class="fa fa-file-text"></i> 刷新页面内容缓存
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 清除所有缓存 -->
                    <div class="col-md-6 col-lg-3">
                        <div class="panel panel-warning">
                            <div class="panel-heading">
                                <h3 class="panel-title">清除所有缓存</h3>
                            </div>
                            <div class="panel-body">
                                <p>清除系统中的所有缓存数据，下次访问时将重新加载。</p>
                                <a href="{:url('dsassistant/cache/clear')}" class="btn btn-danger btn-block btn-dialog" title="清除所有缓存">
                                    <i class="fa fa-trash"></i> 清除所有缓存
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <h4>注意事项</h4>
                            <ul>
                                <li>缓存刷新操作会从数据库重新加载数据，可能会短暂影响系统性能</li>
                                <li>清除缓存后，系统会在下次访问时自动重建缓存</li>
                                <li>建议在修改数据库内容后手动刷新相应的缓存，以确保数据一致性</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">缓存状态</h3>
                            </div>
                            <div class="panel-body">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>缓存类型</th>
                                            <th>状态</th>
                                            <th>记录数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>关键词映射缓存</td>
                                            <td><span class="label label-{$cacheData.keyword_map.status}">{$cacheData.keyword_map.status_text}</span></td>
                                            <td>{$cacheData.keyword_map.count}</td>
                                        </tr>
                                        <tr>
                                            <td>分类规则缓存</td>
                                            <td><span class="label label-{$cacheData.category_rule.status}">{$cacheData.category_rule.status_text}</span></td>
                                            <td>{$cacheData.category_rule.count}</td>
                                        </tr>
                                        <tr>
                                            <td>页面内容缓存</td>
                                            <td><span class="label label-{$cacheData.page_content.status}">{$cacheData.page_content.status_text}</span></td>
                                            <td>{$cacheData.page_content.count}</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="text-muted">
                                    <p>注：缓存状态为实时展示，可通过刷新操作更新缓存。</p>
                                    <p>当前缓存版本：<code>{$cacheVersion}</code></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>