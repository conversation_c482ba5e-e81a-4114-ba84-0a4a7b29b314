<form id="test-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">测试问题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-question" class="form-control" name="question" type="text" value="" placeholder="请输入要测试的问题">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed">搜索</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<div id="results" class="hidden">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">搜索结果</h3>
        </div>
        <div class="panel-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>问题</th>
                        <th>得分</th>
                        <th>相似度</th>
                        <th>权重因子</th>
                        <th>分类加分</th>
                        <th>关键词加分</th>
                    </tr>
                </thead>
                <tbody id="results-body">
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">最佳匹配答案</h3>
        </div>
        <div class="panel-body" id="best-answer">
        </div>
    </div>
</div>

<script>
    $(function () {
        $('#test-form').on('submit', function (e) {
            e.preventDefault();
            
            var question = $('#c-question').val();
            if (!question) {
                Layer.alert('请输入测试问题');
                return false;
            }
            
            Fast.api.ajax({
                url: 'dsassistant/optimizedvector/test',
                data: {question: question}
            }, function (data) {
                // 清空结果
                $('#results-body').empty();
                $('#best-answer').empty();
                
                if (data.length > 0) {
                    // 显示结果表格
                    $.each(data, function (index, item) {
                        var row = '<tr>' +
                            '<td>' + item.id + '</td>' +
                            '<td>' + item.question + '</td>' +
                            '<td>' + item.score.toFixed(4) + '</td>' +
                            '<td>' + item.similarity.toFixed(4) + '</td>' +
                            '<td>' + item.weight_factor.toFixed(4) + '</td>' +
                            '<td>' + (item.category_boost || 0).toFixed(4) + '</td>' +
                            '<td>' + (item.keyword_boost || 0).toFixed(4) + '</td>' +
                            '</tr>';
                        $('#results-body').append(row);
                    });
                    
                    // 显示最佳匹配答案
                    $('#best-answer').html(data[0].answer.replace(/\n/g, '<br>'));
                    
                    // 显示结果区域
                    $('#results').removeClass('hidden');
                } else {
                    Layer.alert('未找到匹配结果');
                }
                
                return false;
            });
            
            return false;
        });
    });
</script>
