<form id="benchmark-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Warning')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="alert alert-info">
                性能测试将使用预设的10个问题进行搜索，测试向量搜索的性能。<br>
                测试过程中请勿关闭页面。
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed">开始测试</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<div id="benchmark-results" class="hidden">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">性能测试结果 - 平均查询时间: <span id="avg-time">0</span>ms</h3>
        </div>
        <div class="panel-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>测试问题</th>
                        <th>最佳匹配</th>
                        <th>得分</th>
                        <th>查询时间</th>
                    </tr>
                </thead>
                <tbody id="benchmark-body">
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    $(function () {
        $('#benchmark-form').on('submit', function (e) {
            e.preventDefault();
            
            var loadIndex = Layer.load(1, {shade: [0.1, '#fff']});
            
            Fast.api.ajax({
                url: 'dsassistant/optimizedvector/benchmark',
                data: {}
            }, function (data) {
                Layer.close(loadIndex);
                
                // 清空结果
                $('#benchmark-body').empty();
                
                // 显示平均时间
                $('#avg-time').text(data.avg_time);
                
                // 显示结果表格
                $.each(data.results, function (index, item) {
                    var row = '<tr>' +
                        '<td>' + item.question + '</td>' +
                        '<td>' + item.best_match + '</td>' +
                        '<td>' + item.score.toFixed(4) + '</td>' +
                        '<td>' + item.time + '</td>' +
                        '</tr>';
                    $('#benchmark-body').append(row);
                });
                
                // 显示结果区域
                $('#benchmark-results').removeClass('hidden');
                
                return false;
            });
            
            return false;
        });
    });
</script>
