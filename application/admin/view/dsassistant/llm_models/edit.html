<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Model_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-model_id" data-rule="required"  class="form-control" name="row[model_id]" type="text" value="{$row.model_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Provider')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-provider" data-rule="required" class="form-control" name="row[provider]" type="text" value="{$row.provider|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Provider_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-provider_name" data-rule="required" class="form-control" name="row[provider_name]" type="text" value="{$row.provider_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Base_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-base_url" data-rule="required" class="form-control" name="row[base_url]" type="text" value="{$row.base_url|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Api_key')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-api_key" class="form-control" name="row[api_key]" type="text" value="{$row.api_key|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Supports_functions')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <input  id="c-supports_functions" name="row[supports_functions]" type="hidden" value="{$row.supports_functions}">
            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-supports_functions" data-yes="1" data-no="0" >
                <i class="fa fa-toggle-on text-success {eq name="$row.supports_functions" value="0"}fa-flip-horizontal text-gray{/eq} fa-2x"></i>
            </a>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Max_tokens')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-max_tokens" data-rule="required" class="form-control" name="row[max_tokens]" type="number" value="{$row.max_tokens|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enabled')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <input  id="c-enabled" name="row[enabled]" type="hidden" value="{$row.enabled}">
            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-enabled" data-yes="1" data-no="0" >
                <i class="fa fa-toggle-on text-success {eq name="$row.enabled" value="0"}fa-flip-horizontal text-gray{/eq} fa-2x"></i>
            </a>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_default')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <input  id="c-is_default" name="row[is_default]" type="hidden" value="{$row.is_default}">
            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_default" data-yes="1" data-no="0" >
                <i class="fa fa-toggle-on text-success {eq name="$row.is_default" value="0"}fa-flip-horizontal text-gray{/eq} fa-2x"></i>
            </a>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" min="0" class="form-control" name="row[weight]" type="number" value="{$row.weight|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>