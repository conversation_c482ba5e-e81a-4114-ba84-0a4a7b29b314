<div class="row">
    <div class="col-xs-12 col-sm-6 col-md-5 col-lg-4">
        <div class="panel panel-default panel-intro">
            <div class="panel-body">
                <div id="myTabContent-left" class="tab-content">
                    <div class="tab-pane fade active in" id="one">
                        <div class="widget-body no-padding">
                            <div id="toolbar-left" class="toolbar">
                                <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>

                            </div>
                            <table id="table-left" class="table table-striped table-bordered table-hover" 
                            data-operate-edit="false"
                            data-operate-del="false"
                            width="100%">

                            </table>


                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <div class="col-xs-12 col-sm-6 col-md-7 col-lg-8">
        <div class="panel panel-default panel-intro">
            {:build_heading()}
        
            <div class="panel-body">
                <div id="myTabContent" class="tab-content">
                    <div class="tab-pane fade active in" id="one">
                        <div class="widget-body no-padding">
                            <div id="toolbar" class="toolbar">
                                <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                                <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('dsassistant/pagecontent/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                                <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('dsassistant/pagecontent/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                                <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('dsassistant/pagecontent/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                            </div>
                            <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                                   data-operate-edit="{:$auth->check('dsassistant/pagecontent/edit')}"
                                   data-operate-del="{:$auth->check('dsassistant/pagecontent/del')}"
                                   width="100%">
                            </table>
                        </div>
                    </div>
        
                </div>
            </div>
        </div>
    </div>
</div>