<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Category_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-category_id" min="0" data-source="dsassistant/categoryrule/index" data-field="category" class="form-control selectpage" name="row[category_id]" type="text" value="{$row.category_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-content_type" data-source="dsassistant/dict/index" data-field="title" data-params='{"custom[parent]":"content_type"}' data-key-field="name" class="form-control selectpage"  name="row[content_type]" type="text" value="{$row.content_type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" data-rule="required" class="form-control mdeditor" rows="5" name="row[content]" cols="50">{$row.content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tags')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tags" class="form-control" data-role="tagsinput" name="row[tags]" type="text" value="{$row.tags|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Source_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-source_type" data-source="dsassistant/dict/index" data-field="title" data-params='{"custom[parent]":"source_type"}' data-key-field="name"class="form-control selectpage" name="row[source_type]" type="text" value="{$row.source_type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Source_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-source_id"  class="form-control"  name="row[source_id]" type="text" value="{$row.source_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Has_vector')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <input  id="c-has_vector" name="row[has_vector]" type="hidden" value="{$row.has_vector}">
            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-has_vector" data-yes="1" data-no="0" >
                <i class="fa fa-toggle-on text-success {eq name="$row.has_vector" value="0"}fa-flip-horizontal text-gray{/eq} fa-2x"></i>
            </a>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vector_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vector_time" min="0" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[vector_time]" type="text" value="{:$row.vector_time?datetime($row.vector_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Valid_from')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-valid_from" min="0" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[valid_from]" type="text" value="{:$row.valid_from?datetime($row.valid_from):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Valid_until')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-valid_until" min="0" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[valid_until]" type="text" value="{:$row.valid_until?datetime($row.valid_until):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" min="0" class="form-control" name="row[weight]" type="number" value="{$row.weight|htmlentities}">
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>