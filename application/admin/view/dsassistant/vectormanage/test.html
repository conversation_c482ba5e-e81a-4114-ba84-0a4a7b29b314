<form id="test-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">测试问题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="question" class="form-control" name="question" type="text" value="" data-rule="required" placeholder="请输入要测试的问题">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">搜索结果:</label>
        <div class="col-xs-12 col-sm-8">
            <div id="result" class="well" style="min-height: 300px; max-height: 500px; overflow-y: auto;">
                <p class="text-muted">搜索结果将显示在这里</p>
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed">开始搜索</button>
            <button type="reset" class="btn btn-default btn-embossed">重置</button>
        </div>
    </div>
</form>

<script>
    $(function () {
        $('#test-form').on('submit', function (e) {
            e.preventDefault();
            var question = $('#question').val();
            if (!question) {
                Layer.alert('请输入要测试的问题');
                return false;
            }
            
            $('#result').html('<p class="text-muted">正在搜索，请稍候...</p>');
            
            $.ajax({
                url: '{:url("test")}',
                type: 'post',
                dataType: 'json',
                data: {question: question},
                success: function (data) {
                    if (data.code == 1) {
                        var html = '';
                        if (data.data.length > 0) {
                            html += '<table class="table table-striped">';
                            html += '<thead><tr><th>问题</th><th>得分</th><th>相似度</th><th>权重因子</th></tr></thead>';
                            html += '<tbody>';
                            $.each(data.data, function (i, item) {
                                html += '<tr>';
                                html += '<td>' + item.question + '</td>';
                                html += '<td>' + item.score.toFixed(4) + '</td>';
                                html += '<td>' + item.similarity.toFixed(4) + '</td>';
                                html += '<td>' + item.weight_factor.toFixed(4) + '</td>';
                                html += '</tr>';
                                html += '<tr>';
                                html += '<td colspan="4"><strong>答案:</strong><br>' + item.answer.replace(/\n/g, '<br>') + '</td>';
                                html += '</tr>';
                            });
                            html += '</tbody></table>';
                        } else {
                            html = '<p class="text-danger">未找到匹配结果</p>';
                        }
                        $('#result').html(html);
                    } else {
                        $('#result').html('<p class="text-danger">' + data.msg + '</p>');
                    }
                },
                error: function () {
                    $('#result').html('<p class="text-danger">搜索失败，请重试</p>');
                }
            });
            
            return false;
        });
    });
</script>
