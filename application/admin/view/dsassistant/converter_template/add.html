<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group"> 
        <label class="control-label col-xs-12 col-sm-2">{:__('Category_id')}:</label> 
        <div class="col-xs-12 col-sm-8"> 
            <input id="c-category_id" min="0" data-source="dsassistant/categoryrule/index" data-field="category" class="form-control selectpage" name="row[category_id]" type="text" value=""> 
        </div> 
    </div> 
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Source_table')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-source_table" data-rule="required" class="form-control" name="row[source_table]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Target_table')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-target_table" data-rule="required" class="form-control" name="row[target_table]" type="text" value="fa_ds_knowledge">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Source_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-source_type" data-rule="required" class="form-control" name="row[source_type]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-content_type" data-rule="required" class="form-control" name="row[content_type]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Template_content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div id="jsoneditor" style="height: 400px;"></div>
            <textarea id="c-template_content" data-rule="required" class="hidden" rows="5" name="row[template_content]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sync_mode')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-sync_mode" data-rule="required" class="form-control selectpicker" name="row[sync_mode]">
                {foreach name="syncModeList" item="vo"}
                    <option value="{$key}" {in name="key" value="full"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sync_field')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sync_field" class="form-control" name="row[sync_field]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Last_sync_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-last_sync_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[last_sync_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Last_sync_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-last_sync_id" data-source="dsassistant/last/sync/index" class="form-control selectpage" name="row[last_sync_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Where_condition')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-where_condition" class="form-control" name="row[where_condition]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_field')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_field" class="form-control" name="row[order_field]" type="text" value="id">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_direction')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-order_direction" class="form-control selectpicker" name="row[order_direction]">
                {foreach name="orderDirectionList" item="vo"}
                    <option value="{$key}" {in name="key" value="asc"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Batch_size')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-batch_size" class="form-control" name="row[batch_size]" type="number" value="100">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="normal"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>