<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Session_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-session_id" data-source="dsassistant/session/index" class="form-control selectpage" name="row[session_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Question')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-question" class="form-control " rows="5" name="row[question]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Answer')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-answer" class="form-control " rows="5" name="row[answer]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Source')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-source" class="form-control selectpicker" name="row[source]">
                {foreach name="sourceList" item="vo"}
                    <option value="{$key}" {in name="key" value="local"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Score')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-score" class="form-control" step="0.01" name="row[score]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-platform" class="form-control selectpicker" name="row[platform]">
                {foreach name="platformList" item="vo"}
                    <option value="{$key}" {in name="key" value="wechat"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ip')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ip" class="form-control" name="row[ip]" type="text" value="">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>