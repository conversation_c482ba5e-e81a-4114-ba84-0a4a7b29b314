<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>函数详情</em></div>
        <ul class="nav nav-tabs">
            <li class="active"><a href="#basic" data-toggle="tab">基本信息</a></li>
            <li><a href="#parameters" data-toggle="tab">参数定义</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="basic">
                <div class="row">
                    <div class="col-xs-12 col-sm-6">
                        <div class="form-group">
                            <label class="control-label">函数名称:</label>
                            <div class="controls">
                                <div class="value">{$function.name}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-6">
                        <div class="form-group">
                            <label class="control-label">来源:</label>
                            <div class="controls">
                                <div class="value">
                                    {if $function.source == 'core'}
                                    <span class="label label-primary">核心</span>
                                    {else}
                                    <span class="label label-success">自定义</span>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="control-label">描述:</label>
                            <div class="controls">
                                <div class="value">{$function.description}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-sm-6">
                        <div class="form-group">
                            <label class="control-label">状态:</label>
                            <div class="controls">
                                <div class="value">
                                    {if $function.enabled}
                                    <span class="label label-success">启用</span>
                                    {else}
                                    <span class="label label-default">禁用</span>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-6">
                        <div class="form-group">
                            <label class="control-label">优先级:</label>
                            <div class="controls">
                                <div class="value">{$function.priority}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="parameters">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="control-label">参数类型:</label>
                            <div class="controls">
                                <div class="value">{$function.parameters.type}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="control-label">必填参数:</label>
                            <div class="controls">
                                <div class="value">
                                    {foreach name="function.parameters.required" item="param"}
                                    <span class="label label-info">{$param}</span>
                                    {/foreach}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="control-label">参数列表:</label>
                            <div class="controls">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>参数名</th>
                                            <th>类型</th>
                                            <th>描述</th>
                                            <th>是否必填</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {foreach name="function.parameters.properties" item="prop" key="propName"}
                                        <tr>
                                            <td>{$propName}</td>
                                            <td>{$prop.type}</td>
                                            <td>{$prop.description}</td>
                                            <td>
                                                {if in_array($propName, $function.parameters.required)}
                                                <span class="label label-success">是</span>
                                                {else}
                                                <span class="label label-default">否</span>
                                                {/if}
                                            </td>
                                        </tr>
                                        {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    document.title = "函数详情 - " + "{$function.name}";
</script>
