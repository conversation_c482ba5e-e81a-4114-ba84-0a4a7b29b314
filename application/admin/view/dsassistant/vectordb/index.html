<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>向量数据库管理</em></div>
        <ul class="nav nav-tabs">
            <li class="active"><a href="#overview" data-toggle="tab">概览</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="overview">
                <div class="widget-body no-padding">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">向量数据库信息</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>当前向量数据库类型</label>
                                        <div>
                                            <span class="label label-success">{$dbType}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>知识库向量集合信息</label>
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>集合名称</th>
                                                        <th>向量数量</th>
                                                        <th>引擎</th>
                                                        <th>其他信息</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {foreach $collections as $collection}
                                                    <tr>
                                                        <td>{$collection.name}</td>
                                                        <td>{$collection.count}</td>
                                                        <td>{$collection.engine}</td>
                                                        <td>
                                                            {if isset($collection.dimension)}
                                                            <span class="label label-info">维度: {$collection.dimension}</span>
                                                            {/if}
                                                            {if isset($collection.indexType)}
                                                            <span class="label label-primary">索引类型: {$collection.indexType}</span>
                                                            {/if}
                                                            {if isset($collection.metricType)}
                                                            <span class="label label-warning">度量类型: {$collection.metricType}</span>
                                                            {/if}
                                                        </td>
                                                    </tr>
                                                    {/foreach}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <p><i class="fa fa-info-circle"></i> 本系统使用固定的知识库向量集合 <strong>ds_knowledge_vectors</strong> 存储所有向量数据。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">操作</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <a href="{:url('dsassistant.vectordb/config')}" class="btn btn-primary btn-config btn-dialog" title="配置向量数据库">
                                        <i class="fa fa-cog"></i> 配置向量数据库
                                    </a>
                                    <a href="{:url('dsassistant.vectordb/rebuild')}" class="btn btn-success btn-rebuild btn-dialog" title="重建索引">
                                        <i class="fa fa-refresh"></i> 重建索引
                                    </a>
                                    <a href="{:url('dsassistant.vectordb/test')}" class="btn btn-info btn-test btn-dialog" title="测试搜索">
                                        <i class="fa fa-search"></i> 测试搜索
                                    </a>
                                    <a href="{:url('dsassistant.vectordb/benchmark')}" class="btn btn-warning btn-benchmark btn-dialog" title="性能测试">
                                        <i class="fa fa-tachometer"></i> 性能测试
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

