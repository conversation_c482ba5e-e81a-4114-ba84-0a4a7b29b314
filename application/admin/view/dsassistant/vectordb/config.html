<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>配置向量数据库</em></div>
    </div>
    <div class="panel-body">
        <form id="config-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('dsassistant.vectordb/config')}">
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">向量数据库类型:</label>
                <div class="col-xs-12 col-sm-8">
                    <select id="c-type" class="form-control" name="type">
                        <option value="mysql" {if $config.vectordb_type == 'mysql'}selected{/if}>MySQL (内置)</option>
                        <option value="tencent" {if $config.vectordb_type == 'tencent'}selected{/if}>腾讯云 VectorDB</option>
                        <option value="aliyun" {if $config.vectordb_type == 'aliyun'}selected{/if}>阿里云 VectorSearch</option>
                        <option value="baidu" {if $config.vectordb_type == 'baidu'}selected{/if}>百度智能云 VectorDB</option>
                        <option value="huawei" {if $config.vectordb_type == 'huawei'}selected{/if}>华为云 VSS</option>
                    </select>
                </div>
            </div>
            
            <!-- 腾讯云配置 -->
            <div id="tencent-config" class="db-config" style="display: none;">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">SecretId:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-tencent-secret-id" class="form-control" name="tencent_secret_id" type="text" value="{$config.vectordb_config.secretId|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">SecretKey:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-tencent-secret-key" class="form-control" name="tencent_secret_key" type="password" value="{$config.vectordb_config.secretKey|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">地区:</label>
                    <div class="col-xs-12 col-sm-8">
                        <select id="c-tencent-region" class="form-control" name="tencent_region">
                            <option value="ap-guangzhou" {if $config.vectordb_config.region == 'ap-guangzhou'}selected{/if}>广州</option>
                            <option value="ap-shanghai" {if $config.vectordb_config.region == 'ap-shanghai'}selected{/if}>上海</option>
                            <option value="ap-beijing" {if $config.vectordb_config.region == 'ap-beijing'}selected{/if}>北京</option>
                            <option value="ap-nanjing" {if $config.vectordb_config.region == 'ap-nanjing'}selected{/if}>南京</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">数据库ID:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-tencent-database-id" class="form-control" name="tencent_database_id" type="text" value="{$config.vectordb_config.databaseId|default=''}">
                    </div>
                </div>
            </div>
            
            <!-- 阿里云配置 -->
            <div id="aliyun-config" class="db-config" style="display: none;">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">AccessKey ID:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-aliyun-access-key-id" class="form-control" name="aliyun_access_key_id" type="text" value="{$config.vectordb_config.accessKeyId|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">AccessKey Secret:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-aliyun-access-key-secret" class="form-control" name="aliyun_access_key_secret" type="password" value="{$config.vectordb_config.accessKeySecret|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">Endpoint:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-aliyun-endpoint" class="form-control" name="aliyun_endpoint" type="text" value="{$config.vectordb_config.endpoint|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">实例ID:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-aliyun-instance-id" class="form-control" name="aliyun_instance_id" type="text" value="{$config.vectordb_config.instanceId|default=''}">
                    </div>
                </div>
            </div>
            
            <!-- 百度智能云配置 -->
            <div id="baidu-config" class="db-config" style="display: none;">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">API Key:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-baidu-api-key" class="form-control" name="baidu_api_key" type="text" value="{$config.vectordb_config.apiKey|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">Secret Key:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-baidu-secret-key" class="form-control" name="baidu_secret_key" type="password" value="{$config.vectordb_config.secretKey|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">Endpoint:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-baidu-endpoint" class="form-control" name="baidu_endpoint" type="text" value="{$config.vectordb_config.endpoint|default=''}">
                    </div>
                </div>
            </div>
            
            <!-- 华为云配置 -->
            <div id="huawei-config" class="db-config" style="display: none;">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">AK:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-huawei-ak" class="form-control" name="huawei_ak" type="text" value="{$config.vectordb_config.ak|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">SK:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-huawei-sk" class="form-control" name="huawei_sk" type="password" value="{$config.vectordb_config.sk|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">Project ID:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-huawei-project-id" class="form-control" name="huawei_project_id" type="text" value="{$config.vectordb_config.projectId|default=''}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">Endpoint:</label>
                    <div class="col-xs-12 col-sm-8">
                        <input id="c-huawei-endpoint" class="form-control" name="huawei_endpoint" type="text" value="{$config.vectordb_config.endpoint|default=''}">
                    </div>
                </div>
            </div>
            
            <div class="form-group layer-footer">
                <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-8">
                    <button type="submit" class="btn btn-success btn-embossed">保存</button>
                    <button type="reset" class="btn btn-default btn-embossed">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
