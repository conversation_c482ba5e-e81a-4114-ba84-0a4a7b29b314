<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>失物招领详情 - 景区智能助理</title>
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/font-awesome/5.15.3/css/all.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/modern.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/lostfound_detail.css">
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <a href="{:url('index/dsassistant.index/lostfound')}" class="btn btn-outline-secondary btn-back">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div id="itemDetail">
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载详情...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="__CDN__/assets/addons/dsassistant/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script>
        $(function() {
            // 获取URL参数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]); return null;
            }
            
            // 获取失物招领详情
            function loadItemDetail() {
                const id = getUrlParam('id');
                if (!id) {
                    $('#itemDetail').html('<div class="alert alert-danger">参数错误：缺少ID</div>');
                    return;
                }
                
                $.ajax({
                    url: '{:url("api/dsassistant.lost_found/getDetail")}',
                    type: 'GET',
                    data: { id: id },
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            const item = response.data.info;
                            renderItemDetail(item);
                        } else {
                            $('#itemDetail').html(`<div class="alert alert-danger">${response.msg || '获取详情失败'}</div>`);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.log('Response:', xhr.responseText);
                        
                        let errorMsg = '网络错误，请稍后再试';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response && response.msg) {
                                errorMsg = response.msg;
                            }
                        } catch (e) {
                            console.error('解析错误响应失败:', e);
                        }
                        
                        $('#itemDetail').html(`<div class="alert alert-danger">获取详情失败: ${errorMsg}</div>`);
                    }
                });
            }
            
            // 渲染失物招领详情
            function renderItemDetail(item) {
                // 构建图片轮播
                let carouselItems = '';
                if (item.images && item.images.length > 0) {
                    item.images.forEach((img, index) => {
                        carouselItems += `
                            <div class="carousel-item ${index === 0 ? 'active' : ''}">
                                <img src="${img}" class="d-block w-100" alt="${item.title}">
                            </div>
                        `;
                    });
                } else {
                    carouselItems = `
                        <div class="carousel-item active">
                            <img src="https://via.placeholder.com/800x400?text=No+Image" class="d-block w-100" alt="${item.title}">
                        </div>
                    `;
                }
                
                // 构建详情HTML
                const typeText = item.type === 'lost' ? '寻物启事' : '招领启事';
                const typeBadge = item.type === 'lost' ? 'badge-lost' : 'badge-found';
                
                const detailHtml = `
                    <div>
                        <div class="item-title">
                            ${item.title}
                            <span class="badge ${typeBadge}">${typeText}</span>
                        </div>
                        
                        <div class="item-meta">
                            <span><i class="far fa-calendar-alt"></i> ${item.create_time}</span>
                            <span class="ml-3"><i class="fas fa-map-marker-alt"></i> ${item.location || '未知地点'}</span>
                            <span class="ml-3"><i class="fas fa-tag"></i> ${item.category || '未分类'}</span>
                        </div>
                        
                        <div id="itemCarousel" class="carousel slide mb-4" data-ride="carousel">
                            <div class="carousel-inner">
                                ${carouselItems}
                            </div>
                            <a class="carousel-control-prev" href="#itemCarousel" role="button" data-slide="prev">
                                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                <span class="sr-only">Previous</span>
                            </a>
                            <a class="carousel-control-next" href="#itemCarousel" role="button" data-slide="next">
                                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                <span class="sr-only">Next</span>
                            </a>
                        </div>
                        
                        <h5>详细描述</h5>
                        <div class="item-description">
                            ${item.description || '暂无描述'}
                        </div>
                        
                        <h5>联系方式</h5>
                        <div class="contact-info">
                            <p><i class="fas fa-user"></i> 联系人：${item.contact_name || '未提供'}</p>
                            <p><i class="fas fa-phone"></i> 联系电话：${item.contact_phone || '未提供'}</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="{:url('index/dsassistant.index/lostfound')}" class="btn btn-primary">
                                返回列表
                            </a>
                        </div>
                    </div>
                `;
                
                $('#itemDetail').html(detailHtml);
            }
            
            // 加载详情
            loadItemDetail();
        });
    </script>
</body>
</html>
