<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>景点导览 - 景区智能助理</title>
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/modern.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/guide.css">
</head>
<body>
    {include file="/dsassistant/common/nav" /}
    <!-- 页面标题区域 -->
    <div class="page-header mb-4">
        <div class="ai-pattern"></div>
        <div class="container text-center py-3">
            <h1 class="display-4 mb-2 font-weight-bold">景点导览</h1>
            <p class="lead mb-0">探索景区的精彩景点，规划您的完美旅程</p>
        </div>
    </div>

    <div class="container py-2 py-md-3 py-lg-4">
        <!-- 地图区域 -->
        <div class="map-container">
            <div class="map-overlay">
                <h4><i class="fas fa-map-marker-alt mr-2" style="color: var(--primary-color);"></i> 景区地图</h4>
                <p>点击地图上的标记查看景点详情，规划您的游览路线</p>
            </div>
            <div id="map"></div>
            <!-- 滚动提示，仅在移动端显示 -->
            <div class="scroll-hint">
                向下滑动查看热门景点 <i class="fas fa-chevron-down"></i>
            </div>
        </div>

        <!-- 景点列表 -->
        <h3 class="mb-4">热门景点</h3>
        <div class="row" id="spotList">
            <!-- 景点列表将通过AJAX加载 -->
            <div class="col-12">
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在加载景点信息...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 景点详情模态框 -->
    <div class="modal fade" id="spotDetailModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">景点详情</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 景点详情将通过AJAX加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="btnNavigate">
                        <i class="fas fa-directions mr-2"></i> 导航
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化页脚 -->
    <footer>
        <div class="ai-pattern"></div>
        <div class="container">
            <div class="row">
                <div class="col-md-6 mb-4 mb-md-0">
                    <h4 class="mb-4"><i class="fas fa-robot mr-2"></i> 景区智能助理</h4>
                    <p class="mb-3">基于国内大模型技术，为游客提供全方位智能服务。包括智能问答、景点导览、失物招领和预警信息等功能。</p>
                    <div class="d-flex mt-4">
                        <a href="#" class="text-white mr-3"><i class="fab fa-weixin fa-lg"></i></a>
                        <a href="#" class="text-white mr-3"><i class="fab fa-weibo fa-lg"></i></a>
                        <a href="#" class="text-white"><i class="fas fa-envelope fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-md-3 mb-4 mb-md-0">
                    <h5 class="mb-4">快速链接</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{:url('index/dsassistant.index/index')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>首页</a></li>
                        <li class="mb-2"><a href="{:url('index/dsassistant.index/chat')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>智能问答</a></li>
                        <li class="mb-2"><a href="{:url('index/dsassistant.index/guide')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>景点导览</a></li>
                        <li class="mb-2"><a href="{:url('index/dsassistant.index/lostfound')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>失物招领</a></li>
                        <li><a href="{:url('index/dsassistant.index/warning')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>预警信息</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5 class="mb-4">联系我们</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-map-marker-alt mr-2"></i> 景区管理中心</li>
                        <li class="mb-2"><i class="fas fa-phone mr-2"></i> ************</li>
                        <li><i class="fas fa-envelope mr-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class="mt-4 mb-4" style="border-color: rgba(255,255,255,0.1);">
            <div class="text-center">
                <p class="mb-0">© 2024 景区智能助理 - 基于国内大模型技术</p>
            </div>
        </div>
    </footer>

    <script src="__CDN__/assets/addons/dsassistant/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script src="https://map.qq.com/api/gljs?v=1.exp&key={:config('dsassistant.tencent_map_key')}"></script>
    <script>
        $(function() {
            // 当前选中的景点
            let currentSpot = null;

            // 初始化地图
            function initMap() {
                // 创建腾讯地图实例
                const center = new TMap.LatLng(39.90923, 116.397428); // 默认中心点，将根据景点数据调整
                const map = new TMap.Map('map', {
                    zoom: 15,
                    center: center
                });

                // 加载景点数据
                $.ajax({
                    url: '{:url("api/dsassistant.guide/getScenicSpots")}',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1 && response.data.list.length > 0) {
                            const spots = response.data.list;

                            // 渲染景点列表
                            renderSpotList(spots);

                            // 在地图上添加标记
                            addMapMarkers(map, spots);

                            // 调整地图中心点
                            if (spots.length > 0) {
                                const newCenter = new TMap.LatLng(spots[0].latitude, spots[0].longitude);
                                map.setCenter(newCenter);
                            }
                        } else {
                            $('#spotList').html('<div class="col-12 text-center py-5"><p>暂无景点信息</p></div>');
                        }
                    },
                    error: function() {
                        $('#spotList').html('<div class="col-12 text-center py-5"><p>加载景点信息失败，请刷新页面重试</p></div>');
                    }
                });

                return map;
            }

            // 在地图上添加标记
            function addMapMarkers(map, spots) {
                // 创建标记点数组
                const markers = spots.map(spot => {
                    return {
                        id: spot.id.toString(),
                        position: new TMap.LatLng(spot.latitude, spot.longitude),
                        properties: {
                            title: spot.name
                        }
                    };
                });

                // 创建标记图层
                const markerLayer = new TMap.MultiMarker({
                    map: map,
                    styles: {
                        'marker': new TMap.MarkerStyle({
                            width: 32,
                            height: 32,
                            anchor: { x: 16, y: 32 },
                            src: '/assets/addons/dsassistant/img/marker.png'
                        })
                    },
                    geometries: markers.map(marker => {
                        return {
                            ...marker,
                            styleId: 'marker'
                        };
                    })
                });

                // 创建信息窗口
                const infoWindow = new TMap.InfoWindow({
                    map: map,
                    position: new TMap.LatLng(0, 0),
                    offset: { x: 0, y: -32 },
                    content: '',
                    closeButton: true
                });
                infoWindow.close();

                // 点击标记显示信息窗口
                markerLayer.on('click', function(evt) {
                    const spotId = evt.geometry.id;
                    const spot = spots.find(s => s.id.toString() === spotId);

                    if (spot) {
                        // 设置信息窗口内容
                        const content = `
                            <div style="width: 250px; padding: 10px;">
                                <h5>${spot.name}</h5>
                                <p style="overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 3; line-clamp: 3; -webkit-box-orient: vertical; max-height: 4.5em; line-height: 1.5;">${spot.description || '暂无描述'}</p>
                                <button class="btn btn-primary btn-sm" onclick="window.showSpotDetail(${spot.id})">
                                    查看详情
                                </button>
                            </div>
                        `;

                        infoWindow.setContent(content);
                        infoWindow.setPosition(evt.geometry.position);
                        infoWindow.open();
                    }
                });
            }

            // 渲染景点列表
            function renderSpotList(spots) {
                let html = '';

                spots.forEach(spot => {
                    // 使用真实图片替代占位符
                    let image = spot.cover;
                    if (!image) {
                        // 随机选择一张景点图片
                        const defaultImages = [
                            '/assets/addons/dsassistant/img/scenic1.jpg',
                            '/assets/addons/dsassistant/img/scenic2.jpg',
                            '/assets/addons/dsassistant/img/scenic3.jpg'
                        ];
                        image = defaultImages[Math.floor(Math.random() * defaultImages.length)];
                    }

                    html += `
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="spot-card glow-effect">
                                <div class="position-relative">
                                    <img src="${image}" class="spot-img w-100" alt="${spot.name}">
                                    <span class="badge badge-primary position-absolute" style="top: 10px; right: 10px;">
                                        <i class="fas fa-star mr-1"></i> 热门景点
                                    </span>
                                </div>
                                <div class="spot-info">
                                    <div class="spot-title">${spot.name}</div>
                                    <div class="spot-desc">${spot.description || '暂无描述'}</div>
                                    <div class="spot-meta">
                                        <div><i class="far fa-clock"></i> ${spot.opening_hours || '暂无开放时间信息'}</div>
                                        <div><i class="fas fa-ticket-alt"></i> ${spot.ticket_price || '暂无票价信息'}</div>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <button class="btn btn-primary" onclick="window.showSpotDetail(${spot.id})">
                                            <i class="fas fa-info-circle mr-1"></i> <span class="hide-sm">查看详情</span><span class="show-sm">详情</span>
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="window.location.href='https://apis.map.qq.com/uri/v1/routeplan?type=drive&to=${encodeURIComponent(spot.name)}&tocoord=${spot.latitude},${spot.longitude}&policy=0&referer=myapp'">
                                            <i class="fas fa-directions"></i> <span class="hide-sm">导航</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                $('#spotList').html(html);
            }

            // 显示景点详情
            window.showSpotDetail = function(id) {
                // 检查ID是否有效
                if (!id || isNaN(id) || id <= 0) {
                    console.error('无效的景点ID:', id);
                    alert('无效的景点ID');
                    return;
                }
                $.ajax({
                    url: '{:url("api/dsassistant.guide/getScenicSpotDetail")}',
                    type: 'GET',
                    data: { id: id },
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            const spot = response.data.info;
                            currentSpot = spot;

                            // 构建图片轮播
                            let carouselItems = '';
                            if (spot.images && spot.images.length > 0) {
                                spot.images.forEach((img, index) => {
                                    carouselItems += `
                                        <div class="carousel-item ${index === 0 ? 'active' : ''}">
                                            <img src="${img}" class="d-block w-100" alt="${spot.name}">
                                        </div>
                                    `;
                                });
                            } else {
                                // 使用真实图片替代占位符
                                const defaultImages = [
                                    '/assets/addons/dsassistant/img/scenic1.jpg',
                                    '/assets/addons/dsassistant/img/scenic2.jpg',
                                    '/assets/addons/dsassistant/img/scenic3.jpg'
                                ];
                                carouselItems = `
                                    <div class="carousel-item active">
                                        <img src="${defaultImages[0]}" class="d-block w-100" alt="${spot.name}">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="${defaultImages[1]}" class="d-block w-100" alt="${spot.name}">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="${defaultImages[2]}" class="d-block w-100" alt="${spot.name}">
                                    </div>
                                `;
                            }

                            // 构建详情HTML
                            const detailHtml = `
                                <div class="position-relative mb-4">
                                    <div id="spotCarousel" class="carousel slide" data-ride="carousel">
                                        <div class="carousel-inner" style="border-radius: var(--border-radius);">
                                            ${carouselItems}
                                        </div>
                                        <a class="carousel-control-prev" href="#spotCarousel" role="button" data-slide="prev">
                                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                            <span class="sr-only">Previous</span>
                                        </a>
                                        <a class="carousel-control-next" href="#spotCarousel" role="button" data-slide="next">
                                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                            <span class="sr-only">Next</span>
                                        </a>
                                    </div>
                                    <span class="badge badge-primary position-absolute" style="top: 15px; right: 15px; z-index: 10;">
                                        <i class="fas fa-star mr-1"></i> 热门景点
                                    </span>
                                </div>

                                <h3 class="mb-3 font-weight-bold">${spot.name}</h3>
                                <p class="mb-4" style="color: var(--gray-color); line-height: 1.7;">${spot.description || '暂无描述'}</p>

                                <div class="row mt-4">
                                    <div class="col-md-6 mb-4 mb-md-0">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header bg-white border-0 pt-4 pb-0">
                                                <h5 class="mb-0"><i class="fas fa-info-circle mr-2" style="color: var(--primary-color);"></i>基本信息</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-map-marker-alt" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">地址</div>
                                                        <div>${spot.address || '暂无地址信息'}</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="far fa-clock" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">开放时间</div>
                                                        <div>${spot.opening_hours || '暂无开放时间信息'}</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-ticket-alt" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">票价</div>
                                                        <div>${spot.ticket_price || '暂无票价信息'}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header bg-white border-0 pt-4 pb-0">
                                                <h5 class="mb-0"><i class="fas fa-lightbulb mr-2" style="color: var(--primary-color);"></i>游玩提示</h5>
                                            </div>
                                            <div class="card-body">
                                                <p style="line-height: 1.7; color: var(--gray-color);">${spot.tips || '暂无游玩提示'}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <a href="https://apis.map.qq.com/uri/v1/routeplan?type=drive&to=${encodeURIComponent(spot.name)}&tocoord=${spot.latitude},${spot.longitude}&policy=0&referer=myapp" class="btn btn-outline-primary" target="_blank">
                                        <i class="fas fa-map-marked-alt mr-2"></i> 查看地图
                                    </a>
                                </div>
                            `;

                            // 添加图标圆圈样式
                            if (!document.getElementById('icon-circle-style')) {
                                const style = document.createElement('style');
                                style.id = 'icon-circle-style';
                                style.textContent = `
                                    .icon-circle {
                                        width: 40px;
                                        height: 40px;
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        flex-shrink: 0;
                                    }
                                `;
                                document.head.appendChild(style);
                            }

                            // 更新模态框内容
                            $('#modalTitle').text(spot.name);
                            $('#modalBody').html(detailHtml);

                            // 显示模态框
                            $('#spotDetailModal').modal('show');
                        } else {
                            alert('获取景点详情失败：' + response.msg);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.log('Response:', xhr.responseText);

                        let errorMsg = '网络错误，请稍后再试';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response && response.msg) {
                                errorMsg = response.msg;
                            }
                        } catch (e) {
                            console.error('解析错误响应失败:', e);
                        }

                        alert('获取景点详情失败: ' + errorMsg);
                    }
                });
            };

            // 导航按钮点击事件
            $('#btnNavigate').click(function() {
                if (currentSpot) {
                    // 使用腾讯地图导航
                    const url = `https://apis.map.qq.com/uri/v1/routeplan?type=drive&to=${currentSpot.name}&tocoord=${currentSpot.latitude},${currentSpot.longitude}&policy=0&referer=myapp`;
                    window.open(url, '_blank');
                }
            });

            // 初始化地图
            const map = initMap();
        });
    </script>
</body>
</html>
