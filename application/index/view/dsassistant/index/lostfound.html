<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>失物招领 - 景区智能助理</title>
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/modern.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/lostfound.css">
</head>
<body>
    {include file="/dsassistant/common/nav" /}
    <!-- 现代化页面标题区域 -->
    <div class="page-header mb-4">
        <div class="ai-pattern"></div>
            <div class="container text-center py-3">
                <h1 class="display-4 mb-2 font-weight-bold">失物招领</h1>
                <p class="lead mb-3">丢失或拾获物品？在这里登记或查询</p>
                <div class="mt-3">
                    <button class="btn btn-light mr-3 glow-effect" data-toggle="modal" data-target="#submitModal">
                        <i class="fas fa-plus-circle mr-2"></i> 登记物品
                    </button>
                    <button class="btn btn-outline-light" data-toggle="modal" data-target="#searchModal">
                        <i class="fas fa-search mr-2"></i> 搜索物品
                    </button>
                </div>
            </div>
        </div>
        <div class="container">
        <!-- 分类标签 -->
        <ul class="nav nav-tabs mb-4">
            <li class="nav-item">
                <a class="nav-link active" id="all-tab" data-toggle="tab" href="#all" role="tab">全部</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="lost-tab" data-toggle="tab" href="#lost" role="tab">寻物启事</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="found-tab" data-toggle="tab" href="#found" role="tab">招领启事</a>
            </li>
        </ul>

        <!-- 物品列表 -->
        <div class="tab-content">
            <div class="tab-pane fade show active" id="all" role="tabpanel">
                <div class="row" id="allItems">
                    <!-- 物品列表将通过AJAX加载 -->
                    <div class="col-12">
                        <div class="loading-container">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">正在加载物品信息...</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="lost" role="tabpanel">
                <div class="row" id="lostItems">
                    <!-- 寻物启事将通过AJAX加载 -->
                </div>
            </div>
            <div class="tab-pane fade" id="found" role="tabpanel">
                <div class="row" id="foundItems">
                    <!-- 招领启事将通过AJAX加载 -->
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页将通过JS生成 -->
            </ul>
        </nav>
    </div>

    <!-- 登记物品模态框 -->
    <div class="modal fade" id="submitModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">登记物品</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="submitForm">
                        <div class="form-group">
                            <label>物品类型</label>
                            <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                <label class="btn btn-outline-danger active">
                                    <input type="radio" name="type" value="lost" checked> 寻物启事
                                </label>
                                <label class="btn btn-outline-success">
                                    <input type="radio" name="type" value="found"> 招领启事
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="title">标题</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="description">描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="category">物品类别</label>
                            <select class="form-control" id="category" name="category">
                                <option value="证件">证件</option>
                                <option value="钱包">钱包</option>
                                <option value="手机">手机</option>
                                <option value="背包">背包</option>
                                <option value="钥匙">钥匙</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="location">地点</label>
                            <input type="text" class="form-control" id="location" name="location">
                        </div>
                        <div class="form-group">
                            <label for="contact_name">联系人</label>
                            <input type="text" class="form-control" id="contact_name" name="contact_name" required>
                        </div>
                        <div class="form-group">
                            <label for="contact_phone">联系电话</label>
                            <input type="tel" class="form-control" id="contact_phone" name="contact_phone" required>
                        </div>
                        <div class="form-group">
                            <label>上传图片</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="images" name="images" multiple accept="image/*">
                                <label class="custom-file-label" for="images">选择图片</label>
                            </div>
                            <div class="mt-2" id="imagePreview"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSubmit">提交</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索物品模态框 -->
    <div class="modal fade" id="searchModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">搜索物品</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="searchForm">
                        <div class="form-group">
                            <label for="searchKeyword">关键词</label>
                            <input type="text" class="form-control" id="searchKeyword" placeholder="输入物品名称、描述等关键词">
                        </div>
                        <div class="form-group">
                            <label>物品类型</label>
                            <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                <label class="btn btn-outline-secondary active">
                                    <input type="radio" name="searchType" value="" checked> 全部
                                </label>
                                <label class="btn btn-outline-danger">
                                    <input type="radio" name="searchType" value="lost"> 寻物启事
                                </label>
                                <label class="btn btn-outline-success">
                                    <input type="radio" name="searchType" value="found"> 招领启事
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="searchCategory">物品类别</label>
                            <select class="form-control" id="searchCategory">
                                <option value="">全部类别</option>
                                <option value="证件">证件</option>
                                <option value="钱包">钱包</option>
                                <option value="手机">手机</option>
                                <option value="背包">背包</option>
                                <option value="钥匙">钥匙</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSearch">搜索</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 物品详情模态框 -->
    <div class="modal fade" id="itemDetailModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="itemModalTitle">物品详情</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="itemModalBody">
                    <!-- 物品详情将通过JS填充 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化页脚 -->
    <footer>
        <div class="ai-pattern"></div>
        <div class="container">
            <div class="row">
                <div class="col-md-6 mb-4 mb-md-0">
                    <h4 class="mb-4"><i class="fas fa-robot mr-2"></i> 景区智能助理</h4>
                    <p class="mb-3">基于国内大模型技术，为游客提供全方位智能服务。包括智能问答、景点导览、失物招领和预警信息等功能。</p>
                    <div class="d-flex mt-4">
                        <a href="#" class="text-white mr-3"><i class="fab fa-weixin fa-lg"></i></a>
                        <a href="#" class="text-white mr-3"><i class="fab fa-weibo fa-lg"></i></a>
                        <a href="#" class="text-white"><i class="fas fa-envelope fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-md-3 mb-4 mb-md-0">
                    <h5 class="mb-4">快速链接</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{:url('index/dsassistant.index/index')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>首页</a></li>
                        <li class="mb-2"><a href="{:url('index/dsassistant.index/chat')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>智能问答</a></li>
                        <li class="mb-2"><a href="{:url('index/dsassistant.index/guide')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>景点导览</a></li>
                        <li class="mb-2"><a href="{:url('index/dsassistant.index/lostfound')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>失物招领</a></li>
                        <li><a href="{:url('index/dsassistant.index/warning')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>预警信息</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5 class="mb-4">联系我们</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-map-marker-alt mr-2"></i> 景区管理中心</li>
                        <li class="mb-2"><i class="fas fa-phone mr-2"></i> ************</li>
                        <li><i class="fas fa-envelope mr-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class="mt-4 mb-4" style="border-color: rgba(255,255,255,0.1);">
            <div class="text-center">
                <p class="mb-0">© 2024 景区智能助理 - 基于国内大模型技术</p>
            </div>
        </div>
    </footer>

    <script src="__CDN__/assets/addons/dsassistant/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script>
        $(function() {
            let currentPage = 1;
            const pageSize = 9;
            let totalItems = 0;
            let currentType = '';

            // 加载物品列表
            function loadItems(page = 1, type = '', keyword = '', category = '') {
                currentPage = page;
                currentType = type;

                $.ajax({
                    url: '{:url("api/dsassistant.lost_found/getList")}',
                    type: 'GET',
                    data: {
                        page: page,
                        limit: pageSize,
                        type: type,
                        keyword: keyword,
                        category: category
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            const items = response.data.list;
                            totalItems = response.data.total;

                            // 渲染物品列表
                            renderItems(items, type);

                            // 生成分页
                            renderPagination(page, Math.ceil(totalItems / pageSize));
                        } else {
                            showError('加载失败：' + response.msg);
                        }
                    },
                    error: function() {
                        showError('网络错误，请稍后再试');
                    }
                });
            }

            // 渲染物品列表
            function renderItems(items, type) {
                if (items.length === 0) {
                    const noDataHtml = '<div class="col-12 text-center py-5"><p>暂无数据</p></div>';

                    if (type === '') {
                        $('#allItems').html(noDataHtml);
                    } else if (type === 'lost') {
                        $('#lostItems').html(noDataHtml);
                    } else if (type === 'found') {
                        $('#foundItems').html(noDataHtml);
                    }
                    return;
                }

                let html = '';

                items.forEach(item => {
                    // 使用真实图片替代占位符
                    let image = item.cover;
                    if (!image) {
                        // 随机选择一张图片
                        const defaultImages = [
                            '/assets/addons/dsassistant/img/scenic1.jpg',
                            '/assets/addons/dsassistant/img/scenic2.jpg',
                            '/assets/addons/dsassistant/img/scenic3.jpg'
                        ];
                        image = defaultImages[Math.floor(Math.random() * defaultImages.length)];
                    }

                    const badgeClass = item.type === 'lost' ? 'badge-lost' : 'badge-found';
                    const badgeText = item.type === 'lost' ? '寻物启事' : '招领启事';
                    const iconClass = item.type === 'lost' ? 'fa-search' : 'fa-hand-holding';

                    html += `
                        <div class="col-md-4 mb-4">
                            <div class="item-card glow-effect">
                                <div class="position-relative">
                                    <img src="${image}" class="item-img w-100" alt="${item.title}">
                                    <span class="badge ${badgeClass} position-absolute" style="top: 15px; right: 15px;">
                                        <i class="fas ${iconClass} mr-1"></i> ${badgeText}
                                    </span>
                                </div>
                                <div class="item-info">
                                    <div class="item-title">${item.title}</div>
                                    <div class="item-desc">${item.description || '暂无描述'}</div>
                                    <div class="item-meta">
                                        <div><i class="fas fa-tag"></i> ${item.category || '未分类'}</div>
                                        <div><i class="fas fa-map-marker-alt"></i> ${item.location || '未知地点'}</div>
                                        <div><i class="far fa-clock"></i> ${formatDate(item.createtime)}</div>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <a href="{:url('index/dsassistant.index/lostfound_detail')}?id=${item.id}" class="btn btn-primary">
                                            <i class="fas fa-info-circle mr-1"></i> 查看详情
                                        </a>
                                        <button class="btn btn-outline-primary" onclick="showItemDetail(${item.id})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                if (type === '') {
                    $('#allItems').html(html);
                } else if (type === 'lost') {
                    $('#lostItems').html(html);
                } else if (type === 'found') {
                    $('#foundItems').html(html);
                }
            }

            // 生成分页
            function renderPagination(currentPage, totalPages) {
                if (totalPages <= 1) {
                    $('#pagination').html('');
                    return;
                }

                let html = '';

                // 上一页
                html += `
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="javascript:void(0);" onclick="changePage(${currentPage - 1})">上一页</a>
                    </li>
                `;

                // 页码
                const startPage = Math.max(1, currentPage - 2);
                const endPage = Math.min(totalPages, startPage + 4);

                for (let i = startPage; i <= endPage; i++) {
                    html += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="javascript:void(0);" onclick="changePage(${i})">${i}</a>
                        </li>
                    `;
                }

                // 下一页
                html += `
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="javascript:void(0);" onclick="changePage(${currentPage + 1})">下一页</a>
                    </li>
                `;

                $('#pagination').html(html);
            }

            // 切换页码
            window.changePage = function(page) {
                if (page < 1 || page > Math.ceil(totalItems / pageSize)) {
                    return;
                }

                loadItems(page, currentType);

                // 滚动到顶部
                $('html, body').animate({ scrollTop: 0 }, 'slow');
            };

            // 显示物品详情
            window.showItemDetail = function(id) {
                $.ajax({
                    url: '{:url("api/dsassistant.lost_found/getDetail")}',
                    type: 'GET',
                    data: { id: id },
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            const item = response.data.info;

                            // 构建图片轮播
                            let carouselItems = '';
                            if (item.images && item.images.length > 0) {
                                item.images.forEach((img, index) => {
                                    carouselItems += `
                                        <div class="carousel-item ${index === 0 ? 'active' : ''}">
                                            <img src="${img}" class="d-block w-100" alt="${item.title}">
                                        </div>
                                    `;
                                });
                            } else {
                                // 使用真实图片替代占位符
                                const defaultImages = [
                                    '/assets/addons/dsassistant/img/scenic1.jpg',
                                    '/assets/addons/dsassistant/img/scenic2.jpg',
                                    '/assets/addons/dsassistant/img/scenic3.jpg'
                                ];
                                carouselItems = `
                                    <div class="carousel-item active">
                                        <img src="${defaultImages[0]}" class="d-block w-100" alt="${item.title}">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="${defaultImages[1]}" class="d-block w-100" alt="${item.title}">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="${defaultImages[2]}" class="d-block w-100" alt="${item.title}">
                                    </div>
                                `;
                            }

                            const badgeClass = item.type === 'lost' ? 'badge-lost' : 'badge-found';
                            const badgeText = item.type === 'lost' ? '寻物启事' : '招领启事';

                            const iconClass = item.type === 'lost' ? 'fa-search' : 'fa-hand-holding';

                            // 构建详情HTML
                            const detailHtml = `
                                <div class="position-relative mb-4">
                                    <div id="itemCarousel" class="carousel slide" data-ride="carousel">
                                        <div class="carousel-inner" style="border-radius: var(--border-radius);">
                                            ${carouselItems}
                                        </div>
                                        <a class="carousel-control-prev" href="#itemCarousel" role="button" data-slide="prev">
                                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                            <span class="sr-only">Previous</span>
                                        </a>
                                        <a class="carousel-control-next" href="#itemCarousel" role="button" data-slide="next">
                                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                            <span class="sr-only">Next</span>
                                        </a>
                                    </div>
                                    <span class="badge ${badgeClass} position-absolute" style="top: 15px; right: 15px; z-index: 10;">
                                        <i class="fas ${iconClass} mr-1"></i> ${badgeText}
                                    </span>
                                </div>

                                <h3 class="mb-3 font-weight-bold">${item.title}</h3>
                                <p class="mb-4" style="color: var(--gray-color); line-height: 1.7;">${item.description || '暂无描述'}</p>

                                <div class="row mt-4">
                                    <div class="col-md-6 mb-4 mb-md-0">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header bg-white border-0 pt-4 pb-0">
                                                <h5 class="mb-0"><i class="fas fa-info-circle mr-2" style="color: var(--primary-color);"></i>物品信息</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-map-marker-alt" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">地点</div>
                                                        <div>${item.location || '未知地点'}</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-tag" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">类别</div>
                                                        <div>${item.category || '未分类'}</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="far fa-clock" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">发布时间</div>
                                                        <div>${formatDate(item.createtime)}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header bg-white border-0 pt-4 pb-0">
                                                <h5 class="mb-0"><i class="fas fa-user mr-2" style="color: var(--primary-color);"></i>联系方式</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-user" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">联系人</div>
                                                        <div>${item.contact_name || '未提供'}</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-phone" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">联系电话</div>
                                                        <div>${item.contact_phone || '未提供'}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <a href="{:url('index/dsassistant.index/lostfound_detail')}?id=${item.id}" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt mr-2"></i> 查看完整详情
                                    </a>
                                </div>
                            `;

                            // 添加图标圆圈样式
                            if (!document.getElementById('icon-circle-style')) {
                                const style = document.createElement('style');
                                style.id = 'icon-circle-style';
                                style.textContent = `
                                    .icon-circle {
                                        width: 40px;
                                        height: 40px;
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        flex-shrink: 0;
                                    }
                                `;
                                document.head.appendChild(style);
                            }

                            // 更新模态框内容
                            $('#itemModalTitle').text(item.title);
                            $('#itemModalBody').html(detailHtml);

                            // 显示模态框
                            $('#itemDetailModal').modal('show');
                        } else {
                            alert('获取物品详情失败：' + response.msg);
                        }
                    },
                    error: function() {
                        alert('网络错误，请稍后再试');
                    }
                });
            };

            // 格式化日期
            function formatDate(timestamp) {
                if (!timestamp) return '未知时间';

                const date = new Date(timestamp * 1000);
                return date.getFullYear() + '-' +
                       padZero(date.getMonth() + 1) + '-' +
                       padZero(date.getDate()) + ' ' +
                       padZero(date.getHours()) + ':' +
                       padZero(date.getMinutes());
            }

            // 补零
            function padZero(num) {
                return num < 10 ? '0' + num : num;
            }

            // 显示错误信息
            function showError(message) {
                const errorHtml = `<div class="col-12 text-center py-5"><p class="text-danger">${message}</p></div>`;
                $('#allItems, #lostItems, #foundItems').html(errorHtml);
            }

            // 提交表单
            $('#btnSubmit').click(function() {
                // 表单验证
                const form = $('#submitForm')[0];
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // 收集表单数据
                const formData = new FormData();
                formData.append('title', $('#title').val());
                formData.append('description', $('#description').val());
                formData.append('type', $('input[name="type"]:checked').val());
                formData.append('category', $('#category').val());
                formData.append('location', $('#location').val());
                formData.append('contact_name', $('#contact_name').val());
                formData.append('contact_phone', $('#contact_phone').val());

                // 处理图片
                const fileInput = $('#images')[0];
                if (fileInput.files.length > 0) {
                    for (let i = 0; i < fileInput.files.length; i++) {
                        formData.append('images[]', fileInput.files[i]);
                    }
                }

                // 提交数据
                $.ajax({
                    url: '{:url("api/dsassistant.lost_found/submit")}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.code === 1) {
                            alert('提交成功！');
                            $('#submitModal').modal('hide');
                            $('#submitForm')[0].reset();
                            $('#imagePreview').html('');

                            // 重新加载物品列表
                            loadItems();
                        } else {
                            alert('提交失败：' + response.msg);
                        }
                    },
                    error: function() {
                        alert('网络错误，请稍后再试');
                    }
                });
            });

            // 搜索按钮点击事件
            $('#btnSearch').click(function() {
                const keyword = $('#searchKeyword').val();
                const type = $('input[name="searchType"]:checked').val();
                const category = $('#searchCategory').val();

                loadItems(1, type, keyword, category);
                $('#searchModal').modal('hide');

                // 切换到对应的标签页
                if (type === '') {
                    $('#all-tab').tab('show');
                } else if (type === 'lost') {
                    $('#lost-tab').tab('show');
                } else if (type === 'found') {
                    $('#found-tab').tab('show');
                }
            });

            // 标签页切换事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
                const target = $(e.target).attr('id');

                if (target === 'all-tab') {
                    loadItems(1, '');
                } else if (target === 'lost-tab') {
                    loadItems(1, 'lost');
                } else if (target === 'found-tab') {
                    loadItems(1, 'found');
                }
            });

            // 图片预览
            $('#images').change(function() {
                const fileInput = this;
                const imagePreview = $('#imagePreview');
                imagePreview.html('');

                if (fileInput.files.length > 0) {
                    for (let i = 0; i < fileInput.files.length; i++) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            imagePreview.append(`
                                <img src="${e.target.result}" class="img-thumbnail mr-2 mb-2" style="height: 100px;">
                            `);
                        };
                        reader.readAsDataURL(fileInput.files[i]);
                    }
                }
            });

            // 初始加载物品列表
            loadItems();
        });
    </script>
</body>
</html>
