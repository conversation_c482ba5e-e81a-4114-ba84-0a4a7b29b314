<?php

namespace app\common\model;

use think\Model;
use think\Cache;
use think\Db;
use think\Log;
use addons\dsassistant\library\VectorSearchFactory;
use addons\dsassistant\library\SessionManager;
use addons\dsassistant\library\strategy\StrategyFactory;
use addons\dsassistant\library\llm\LLMClient;
use app\admin\model\dsassistant\Contentconfig;

/**
 * 景区DeepSeek智能助理模型
 *
 * 负责处理用户问题，通过向量搜索和AI生成提供回答
 *
 * @package app\common\model
 */

class DsAssistant extends Model
{
    /**
     * 向量搜索实例
     * @var \addons\dsassistant\library\EnhancedVectorSearch|\addons\dsassistant\library\JiebaVectorSearch
     */
    protected $vectorSearch = null;
    /**
     * 处理用户问题
     *
     * @param string $question 用户问题
     * @param string $userId 用户ID
     * @param string $sessionId 会话ID
     * @param string $platform 平台
     * @param string $ip IP地址
     * @return array|string 回答内容，如果启用函数调用则返回数组
     */
    public function handleQuestion($question, $userId = '', $sessionId = '', $platform = 'wechat', $ip = '')
    {
        $config = Contentconfig::getConfigAll();

        // 检查是否启用函数调用
        $enableFunctions = isset($config['enable_functions']) ? (bool)$config['enable_functions'] : false;

        // 0. 检查会话状态，获取上下文
        $sessionState = SessionManager::getSessionState($sessionId);
        $hasContext = false;

        // 检查是否是上下文相关的问题
        if ($sessionState && !empty($sessionState['context'])) {
            // 检查是否是简短回复，可能是对上一个问题的补充
            $isShortReply = mb_strlen($question, 'UTF-8') <= 10 && !preg_match('/[?？。！!]$/', $question);

            // 检查是否包含上下文相关词语
            $contextKeywords = ['这个', '那个', '它', '他们', '她们', '怎么样', '多少', '什么时候', '在哪里', '为什么'];
            $hasContextKeyword = false;
            foreach ($contextKeywords as $keyword) {
                if (mb_stripos($question, $keyword) !== false) {
                    $hasContextKeyword = true;
                    break;
                }
            }

            // 如果是简短回复或包含上下文相关词语，则认为是上下文相关的问题
            if ($isShortReply || $hasContextKeyword) {
                $hasContext = true;
                Log::record("检测到上下文相关问题: {$question}", 'info');
            }
        }

        // 如果启用了函数调用，则不使用缓存，直接调用LLM API
        if ($enableFunctions) {
            Log::record("启用函数调用，跳过缓存", 'info');

            try {
                // 检查是否有支持函数调用的模型
                $supportsFunctions = false;

                // 首先尝试使用配置中指定的模型
                $model = $config['deepseek_model'] ?: 'deepseek-chat';

                // // 如果配置的模型不支持函数调用，尝试使用deepseek-reasoner
                // if ($model !== 'deepseek-reasoner') {
                //     Log::record("模型 {$model} 可能不支持函数调用，尝试使用 deepseek-reasoner", 'info');
                //     $model = 'deepseek-reasoner';
                // }

                // 直接调用LLM API，启用函数调用
                $result = $this->callDeepSeek($question, $sessionId, true, $model);

                $answer = $result['content'];
                $functionCalls = $result['function_calls'] ?? [];
                $source = 'ai';
                $score = 0.9; // 固定分数

                // 记录聊天日志
                $extra = json_encode([
                    'strategy' => 'function_calling',
                    'function_calls' => $functionCalls
                ]);
                $this->saveChatLog($question, $answer, $source, $score, $userId, $sessionId, $platform, $ip, $extra);

                // 更新会话状态
                SessionManager::updateSessionState($sessionId, $userId, $question, $answer, $platform);

                // 返回包含函数调用的结果
                return [
                    'answer' => $answer,
                    'functionCalls' => $functionCalls
                ];
            } catch (\Exception $e) {
                Log::error('函数调用失败: ' . $e->getMessage());
                // 如果函数调用失败，回退到普通模式
                $enableFunctions = false;
            }
        }

        // 如果没有启用函数调用或函数调用失败，使用普通模式

        // 1. 检查缓存（仅对非上下文相关的问题）
        if (!$hasContext) {
            $cacheKey = 'dsassistant_question_' . md5($question);
            $cacheTime = intval($config['cache_time'] ?: 3600);
            $cachedAnswer = Cache::get($cacheKey);

            if ($cachedAnswer) {
                // 记录聊天日志
                $this->saveChatLog($question, $cachedAnswer['answer'], $cachedAnswer['source'], $cachedAnswer['score'], $userId, $sessionId, $platform, $ip, $cachedAnswer['extra'] ?? null);

                // 更新会话状态
                SessionManager::updateSessionState($sessionId, $userId, $question, $cachedAnswer['answer'], $platform);

                return $cachedAnswer['answer'];
            }
        }

        // 2. 使用策略模式生成回答
        // 获取适当的策略实例
        $strategy = StrategyFactory::getStrategy(null, $config);

        // 使用策略生成回答
        $result = $strategy->generateAnswer($question, $sessionId, $config);

        $answer = $result['answer'];
        $source = $result['source'];
        $score = $result['score'];
        $extra = $result['extra'] ?? null;

        // 缓存结果（仅对非上下文相关的问题）
        if (!$hasContext) {
            Cache::set($cacheKey, [
                'answer' => $answer,
                'source' => $source,
                'score' => $score,
                'extra' => $extra
            ], $cacheTime);
        }

        // 记录聊天日志
        $this->saveChatLog($question, $answer, $source, $score, $userId, $sessionId, $platform, $ip, $extra);

        // 更新会话状态
        SessionManager::updateSessionState($sessionId, $userId, $question, $answer, $platform);

        return $answer;
    }

    /**
     * 在本地知识库中搜索问题
     *
     * 使用VectorSearchFactory获取向量搜索实例，
     * 根据配置自动选择最合适的向量搜索实现（EnhancedVectorSearch或JiebaVectorSearch）。
     * 不再直接使用OptimizedVectorSearch，因为它已被更高级的实现所替代。
     *
     * @param string $question 用户问题
     * @param int $topK 返回的结果数量，默认为1（最佳匹配）
     * @return array|null 匹配结果数组或单个结果（取决于$topK参数）
     */
    protected function searchFAQ($question, $topK = 1)
    {
        // 初始化向量搜索实例（如果尚未初始化）
        if ($this->vectorSearch === null) {
            // 使用VectorSearchFactory获取向量搜索实例
            // VectorSearchFactory会根据配置自动选择最合适的向量搜索实现
            // 可能是EnhancedVectorSearch或JiebaVectorSearch
            $this->vectorSearch = VectorSearchFactory::getInstance();
            Log::info('使用VectorSearchFactory初始化向量搜索实例');
        }

        // 使用向量搜索
        $vectorResults = $this->vectorSearch->search($question, max(5, $topK));

        // 记录日志，用于调试
        Log::record('处理问题: ' . $question, 'info');
        Log::record('向量搜索结果数量: ' . count($vectorResults), 'info');

        if (!empty($vectorResults)) {
            // 如果只需要返回一个结果（默认行为）
            if ($topK === 1) {
                $bestMatch = $vectorResults[0];

                // 记录最佳匹配结果
                Log::record("最佳匹配: {$bestMatch['title']}, 得分: {$bestMatch['score']}", 'info');

                return [
                    'question' => $bestMatch['title'],   // 使用title字段作为question
                    'answer' => $bestMatch['content'],   // 使用content字段作为answer
                    'score' => $bestMatch['score']
                ];
            }
            // 如果需要返回多个结果
            else {
                $results = [];

                // 记录所有匹配结果，便于调试
                foreach ($vectorResults as $index => $result) {
                    Log::record("匹配结果 #{$index}: 标题=\"{$result['title']}\", 得分={$result['score']}", 'info');

                    if ($index < $topK) {
                        $results[] = [
                            'question' => $result['title'],   // 使用title字段作为question
                            'answer' => $result['content'],   // 使用content字段作为answer
                            'score' => $result['score']
                        ];
                    }
                }

                return $results;
            }
        } else {
            Log::record("未找到匹配项", 'info');
            return ($topK === 1) ? null : [];
        }
    }



    /**
     * 调用LLM API
     *
     * @param string $question 用户问题
     * @param string $sessionId 会话ID
     * @param bool $enableFunctions 是否启用函数调用
     * @param string|null $modelId 指定使用的模型ID，如果为null则使用配置中的模型
     * @return array|string AI生成的回答，如果启用函数调用则返回数组
     */
    protected function callDeepSeek($question, $sessionId = '', $enableFunctions = false, $modelId = null)
    {
        $config = Contentconfig::getConfigAll();
        $model = $modelId ?: ($config['deepseek_model'] ?: 'deepseek-chat');
        $systemPrompt = $config['system_prompt'] ?: '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。';

        // 准备消息
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt]
        ];

        // 如果有会话ID，获取会话上下文
        if (!empty($sessionId)) {
            $sessionState = SessionManager::getSessionState($sessionId);
            if ($sessionState && !empty($sessionState['context'])) {
                // 使用会话上下文
                $context = $sessionState['context'];

                // 如果上下文太长，只保留最近的几轮对话
                if (count($context) > SessionManager::MAX_HISTORY_SIZE * 2) {
                    $context = array_slice($context, -SessionManager::MAX_HISTORY_SIZE * 2);
                }

                // 合并上下文和系统提示
                $messages = array_merge($messages, $context);

                // 添加当前问题
                $messages[] = ['role' => 'user', 'content' => $question];

                Log::record("使用会话上下文，共 " . count($context) . " 条消息", 'info');
            } else {
                // 没有上下文，只添加当前问题
                $messages[] = ['role' => 'user', 'content' => $question];
            }
        } else {
            // 没有会话ID，只添加当前问题
            $messages[] = ['role' => 'user', 'content' => $question];
        }

        try {
            // 创建LLMClient实例，使用指定的模型或默认模型
            $llmClient = new LLMClient($model);

            // 如果启用函数调用
            if ($enableFunctions) {
                // 使用函数加载器获取函数定义
                $functionLoader = new \addons\dsassistant\library\FunctionLoader();
                $functions = $functionLoader->getFunctionsForDeepSeek();

                // 调用支持函数的API
                $result = $llmClient->chatCompletionWithFunctions($messages, $functions);

                Log::record("使用函数调用，函数数量: " . count($functions), 'info');
                if (!empty($result['function_calls'])) {
                    Log::record("LLM返回了函数调用，数量: " . count($result['function_calls']), 'info');
                }

                return $result;
            } else {
                // 调用普通API
                return $llmClient->chatCompletion($messages);
            }
        } catch (\Exception $e) {
            Log::error('LLM API调用失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 使用本地知识库上下文调用LLM API
     *
     * 该方法将本地知识库的检索结果作为上下文提供给LLM大模型，
     * 实现检索增强生成(RAG)模式。
     *
     * @param string $question 用户问题
     * @param string $contextInfo 本地知识库提供的上下文信息
     * @param string $sessionId 会话ID
     * @return string AI生成的回答
     */
    protected function callDeepSeekWithContext($question, $contextInfo, $sessionId = '')
    {
        $config = Contentconfig::getConfigAll();
        $model = $config['deepseek_model'] ?: 'deepseek-chat';

        // 基础系统提示
        $basePrompt = $config['system_prompt'] ?: '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。';

        // 增强系统提示，加入本地知识库信息
        $systemPrompt = $basePrompt;
        if (!empty($contextInfo)) {
            // 使用配置中的提示模板，如果没有则使用默认模板
            $promptTemplate = isset($config['rag_config']['prompt_template']) ?
                $config['rag_config']['prompt_template'] :
                "以下是关于用户问题的相关信息，请参考这些信息回答问题，但不要直接复制这些内容，而是用自然、流畅的语言回答：\n\n{context}";

            $systemPrompt .= "\n\n" . str_replace('{context}', $contextInfo, $promptTemplate);

            Log::record("使用RAG模式，添加了本地知识库上下文", 'info');
        }

        // 准备消息
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt]
        ];

        // 如果有会话ID，获取会话上下文
        if (!empty($sessionId)) {
            $sessionState = SessionManager::getSessionState($sessionId);
            if ($sessionState && !empty($sessionState['context'])) {
                // 使用会话上下文
                $context = $sessionState['context'];

                // 如果上下文太长，只保留最近的几轮对话
                if (count($context) > SessionManager::MAX_HISTORY_SIZE * 2) {
                    $context = array_slice($context, -SessionManager::MAX_HISTORY_SIZE * 2);
                }

                // 合并上下文和系统提示
                $messages = array_merge($messages, $context);

                // 添加当前问题
                $messages[] = ['role' => 'user', 'content' => $question];

                Log::record("使用会话上下文，共 " . count($context) . " 条消息", 'info');
            } else {
                // 没有上下文，只添加当前问题
                $messages[] = ['role' => 'user', 'content' => $question];
            }
        } else {
            // 没有会话ID，只添加当前问题
            $messages[] = ['role' => 'user', 'content' => $question];
        }

        try {
            // 创建LLMClient实例，使用指定的模型或默认模型
            $llmClient = new LLMClient($model);

            // 调用LLM API
            return $llmClient->chatCompletion($messages);
        } catch (\Exception $e) {
            Log::error('LLM API调用失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检查AI回答的置信度
     *
     * @param string $answer AI回答
     * @return bool 是否通过置信度检查
     */
    protected function confidenceCheck($answer)
    {
        // 简单实现，可以根据实际需求调整
        // 例如检查回答长度、是否包含特定关键词等
        if (empty($answer)) {
            return false;
        }

        if (strpos($answer, '我不知道') !== false ||
            strpos($answer, '无法回答') !== false ||
            strpos($answer, '没有相关信息') !== false) {
            return false;
        }

        return true;
    }

    /**
     * 保存聊天日志
     *
     * 该方法将用户的问题和系统的回答保存到聊天日志表(ds_chat_log)中，用于后续分析和改进。
     *
     * @param string $question 用户提出的问题
     * @param string $answer 系统生成的回答
     * @param string $source 回答来源，可能的值包括:
     *        - 'local': 表示回答来自本地知识库。这种情况下，系统通过向量搜索在预先存储的知识库中
     *                  找到了与用户问题最相似的内容，并直接返回对应的答案。这类回答通常更准确、
     *                  更符合景区的实际情况，因为它们是基于已验证的信息。本地知识库的回答速度更快，
     *                  不依赖外部API，且不会产生额外的API调用费用。
     *        - 'ai': 表示回答来自AI大模型(如DeepSeek)。当本地知识库中没有找到合适的匹配时，
     *               系统会调用外部AI服务生成回答。AI生成的回答更灵活，可以处理更广泛的问题，
     *               但可能不如本地知识库的回答准确，且会产生API调用费用。AI回答会考虑会话上下文，
     *               能够进行多轮对话，理解用户的连续提问。
     *
     *        通过区分这两种来源，我们可以:
     *        1. 分析哪些问题本地知识库能够回答，哪些需要AI辅助
     *        2. 评估系统的知识覆盖率和完整性
     *        3. 监控AI调用频率和相关成本
     *        4. 针对性地扩充本地知识库内容
     *
     * @param float $score 匹配得分或置信度，范围通常在0-1之间:
     *        - 对于'local'来源，表示向量搜索的相似度得分，越高表示匹配越精确
     *        - 对于'ai'来源，通常设置为一个固定值(如0.9)，因为AI生成没有明确的匹配度量
     * @param string $userId 用户唯一标识符
     * @param string $sessionId 会话ID，用于跟踪多轮对话
     * @param string $platform 用户使用的平台，如'web'、'wechat'、'app'等
     * @param string $ip 用户的IP地址，用于地理位置分析和安全监控
     * @param string|null $extra 额外信息，JSON格式字符串，包含策略类型和其他元数据
     * @return void
     */
    protected function saveChatLog($question, $answer, $source, $score, $userId, $sessionId, $platform, $ip, $extra = null)
    {
        $data = [
            'user_id' => $userId,
            'session_id' => $sessionId,
            'question' => $question,
            'answer' => $answer,
            'source' => $source,  // 'local'表示本地知识库，'ai'表示AI大模型生成
            'score' => $score,
            'platform' => $platform,
            'ip' => $ip,
            'createtime' => time()
        ];

        // 如果提供了额外信息，添加到数据中
        if ($extra !== null) {
            $data['extra'] = $extra;
        }

        Db::name('ds_chat_log')->insert($data);
    }
}
