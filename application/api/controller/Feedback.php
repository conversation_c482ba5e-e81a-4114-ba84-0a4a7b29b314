<?php

namespace app\api\controller;

use think\Db;
use think\Log;
use think\Exception;

/**
 * 反馈相关API
 */
class Feedback extends Base
{

    /**
     * 提交反馈
     */
    public function submit()
    {
        $content = $this->request->post('content');
        $type = $this->request->post('type', 'suggestion');
        $contact = $this->request->post('contact', '');

        // 优先使用传入的user_id，如果没有则尝试使用设备ID，最后才使用IP
        $userId = $this->request->post('user_id', '');

        // 如果没有提供user_id，尝试使用设备ID
        if (empty($userId)) {
            $deviceId = $this->request->post('device_id', '');
            if (!empty($deviceId)) {
                $userId = 'device_' . $deviceId;
            } else {
                // 最后才使用IP+UA组合作为标识
                $ip = $this->request->ip();
                $ua = $this->request->header('user-agent', '');
                $userId = 'ip_' . md5($ip . $ua);
            }
        }

        if (empty($content)) {
            $this->error('反馈内容不能为空');
        }

        $result = false;
        $errorMsg = '';

        try {
            $data = [
                'content' => $content,
                'type' => $type,
                'contact' => $contact,
                'user_id' => $userId,
                'ip' => $this->request->ip(),
                'createtime' => time(),
                'status' => 'pending'
            ];

            $result = Db::name('ds_feedback')->insert($data);
        } catch (Exception $e) {
            Log::error("提交反馈异常: " . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } elseif ($result) {
            $this->success('反馈提交成功');
        } else {
            $this->error('反馈提交失败');
        }
    }
}
