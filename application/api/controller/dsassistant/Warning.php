<?php

namespace app\api\controller\dsassistant;

use think\Db;
use think\Log;
use think\Exception;

/**
 * 预警信息API
 */
class Warning extends Base
{
    /**
     * 获取预警信息
     */
    public function getList()
    {
        $list = Db::name('ds_warning')
            ->where('status', 'normal')
            ->where('start_time', '<=', time())
            ->where('end_time', '>=', time())
            ->order('level DESC, id DESC')
            ->select();

        $this->success('', ['list' => $list]);
    }
}