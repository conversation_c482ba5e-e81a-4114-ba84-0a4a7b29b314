<?php

namespace app\api\controller\dsassistant;

use app\common\controller\Api;
use think\Log;

/**
 * DSASSISTANT API基础控制器
 */
class Base extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        
        // 记录API访问日志
        $this->logApiAccess();
        
        // 设置跨域头
        $this->setCorsHeaders();
    }

    /**
     * 记录API访问日志
     */
    protected function logApiAccess()
    {
        $controller = $this->request->controller();
        $action = $this->request->action();
        $method = $this->request->method();
        $ip = $this->request->ip();
        $userAgent = $this->request->header('user-agent', '');
        
        Log::info("DSASSISTANT API访问: {$method} {$controller}/{$action} - IP: {$ip} - UA: {$userAgent}");
    }

    /**
     * 设置跨域头
     */
    protected function setCorsHeaders()
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        // 处理OPTIONS预检请求
        if ($this->request->method() === 'OPTIONS') {
            exit();
        }
    }

    /**
     * 获取用户标识
     * 优先级：user_id > device_id > IP+UA
     */
    protected function getUserId()
    {
        $userId = $this->request->param('user_id', '');
        
        if (empty($userId)) {
            $deviceId = $this->request->param('device_id', '');
            if (!empty($deviceId)) {
                $userId = 'device_' . $deviceId;
            } else {
                $ip = $this->request->ip();
                $ua = $this->request->header('user-agent', '');
                $userId = 'ip_' . md5($ip . $ua);
            }
        }
        
        return $userId;
    }

    /**
     * 统一的参数验证
     */
    protected function validateParams($rules, $data = null)
    {
        if ($data === null) {
            $data = $this->request->param();
        }
        
        foreach ($rules as $field => $rule) {
            $value = isset($data[$field]) ? $data[$field] : null;
            
            // 必填验证
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $this->error($rule['message'] ?? "{$field}不能为空");
            }
            
            // 类型验证
            if (!empty($value) && isset($rule['type'])) {
                switch ($rule['type']) {
                    case 'int':
                        if (!is_numeric($value)) {
                            $this->error($rule['message'] ?? "{$field}必须是数字");
                        }
                        break;
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $this->error($rule['message'] ?? "{$field}格式不正确");
                        }
                        break;
                    case 'in':
                        if (!in_array($value, $rule['values'])) {
                            $this->error($rule['message'] ?? "{$field}值不在允许范围内");
                        }
                        break;
                }
            }
            
            // 长度验证
            if (!empty($value) && isset($rule['length'])) {
                $length = mb_strlen($value, 'utf-8');
                if (isset($rule['length']['min']) && $length < $rule['length']['min']) {
                    $this->error($rule['message'] ?? "{$field}长度不能少于{$rule['length']['min']}个字符");
                }
                if (isset($rule['length']['max']) && $length > $rule['length']['max']) {
                    $this->error($rule['message'] ?? "{$field}长度不能超过{$rule['length']['max']}个字符");
                }
            }
        }
        
        return true;
    }

    /**
     * 统一的错误处理
     */
    protected function handleException(\Exception $e, $defaultMessage = '系统错误，请稍后再试')
    {
        Log::error("DSASSISTANT API异常: " . $e->getMessage() . " - 文件: " . $e->getFile() . " - 行号: " . $e->getLine());
        $this->error($defaultMessage);
    }

    /**
     * 统一的成功响应
     */
    protected function successResponse($data = [], $message = '操作成功')
    {
        $this->success($message, $data);
    }

    /**
     * 统一的分页响应
     */
    protected function paginateResponse($list, $total, $page, $pageSize, $message = '获取成功')
    {
        $this->success($message, [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
            'hasMore' => ($page * $pageSize) < $total
        ]);
    }
}
