<?php

namespace app\api\controller\dsassistant;

use think\Db;
use think\Log;
use think\Exception;

/**
 * 失物招领API
 */
class LostFound extends Base
{
    /**
     * 获取失物招领列表
     */
    public function getList()
    {
        $type = $this->request->param('type', '');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 10);
        $keyword = $this->request->param('keyword', '');
        $categoryId = $this->request->param('category_id', '');
        $excludeId = $this->request->param('exclude_id/d', 0);

        $where = ['status' => ['in', ['pending', 'processed']]];

        // 按类型筛选
        if (in_array($type, ['lost', 'found'])) {
            $where['type'] = $type;
        }

        // 按分类ID筛选
        if (!empty($categoryId)) {
            $where['category_id'] = $categoryId;
        }

        // 排除指定ID（用于相关推荐）
        if ($excludeId > 0) {
            $where['id'] = ['<>', $excludeId];
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $where['title|description|location'] = ['like', "%{$keyword}%"];
        }

        $total = Db::name('ds_lost_found')
            ->where($where)
            ->count();

        $list = Db::name('ds_lost_found')
            ->where($where)
            ->order('id DESC')
            ->page($page, $limit)
            ->select();

        foreach ($list as &$item) {
            // 处理图片
            if (!empty($item['images'])) {
                $item['images'] = explode(',', $item['images']);
                $item['cover'] = $item['images'][0] ?? '';
            } else {
                $item['images'] = [];
                $item['cover'] = '';
            }

            // 获取分类名称
            if (!empty($item['category_id'])) {
                $category = Db::name('ds_lost_found_category')
                    ->where('id', $item['category_id'])
                    ->find();
                $item['category'] = $category ? $category['category'] : '';
            }
        }

        $this->success('', [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 提交失物招领
     */
    public function submit()
    {
        $data = [
            'title' => $this->request->post('title'),
            'description' => $this->request->post('description'),
            'type' => $this->request->post('type'),
            'category_id' => $this->request->post('category_id/d', 0), // 使用分类ID
            'location' => $this->request->post('location', ''),
            'contact_name' => $this->request->post('contact_name', ''),
            'contact_phone' => $this->request->post('contact_phone', ''),
            'images' => $this->request->post('images', ''),
            'status' => 'pending',
            'createtime' => time(),
            'updatetime' => time()
        ];

        // 兼容旧版本，如果提供了category字段但没有category_id
        if (empty($data['category_id'])) {
            $category = $this->request->post('category', '');
            if (!empty($category)) {
                // 尝试根据分类名称查找分类ID
                $categoryInfo = Db::name('ds_lost_found_category')
                    ->where('category', $category)
                    ->where('status', 'normal')
                    ->find();

                if ($categoryInfo) {
                    $data['category_id'] = $categoryInfo['id'];
                }
            }
        }

        if (empty($data['title']) || empty($data['description']) || !in_array($data['type'], ['lost', 'found'])) {
            $this->error('参数错误');
        }

        if (is_array($data['images'])) {
            $data['images'] = implode(',', $data['images']);
        }

        $id = 0;
        $errorMsg = '';

        try {
            $id = Db::name('ds_lost_found')->insertGetId($data);

            // 记录日志
            Log::info("提交失物招领成功: ID = {$id}, 标题 = {$data['title']}, 分类ID = {$data['category_id']}");

        } catch (Exception $e) {
            Log::error("提交失物招领异常: " . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } elseif ($id) {
            $this->success('提交成功', ['id' => $id]);
        } else {
            $this->error('提交失败，请稍后再试');
        }
    }

    /**
     * 获取失物招领详情
     */
    public function getDetail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->error('参数错误：ID不能为空');
        }

        // 查询失物招领信息
        $info = null;
        $errorMsg = '';

        try {
            $info = Db::name('ds_lost_found')
                ->where('id', $id)
                ->where('status', 'in', ['pending', 'processed'])
                ->find();

            if (!$info) {
                // 记录错误日志
                Log::error("失物招领信息不存在: ID = {$id}");

                // 获取所有失物招领ID列表，用于调试
                $allItems = Db::name('ds_lost_found')
                    ->field('id, title, status')
                    ->limit(10)
                    ->select();
                Log::info("最近10条失物招领列表: " . json_encode($allItems, JSON_UNESCAPED_UNICODE));

                $errorMsg = "失物招领信息不存在 (ID: {$id})";
                return;
            }

            // 处理图片
            if (!empty($info['images'])) {
                $info['images'] = explode(',', $info['images']);
            } else {
                $info['images'] = [];
            }

            // 获取分类信息
            if (!empty($info['category_id'])) {
                $category = Db::name('ds_lost_found_category')
                    ->where('id', $info['category_id'])
                    ->find();
                if ($category) {
                    $info['category'] = $category['category'];
                }
            }

            // 格式化时间
            $info['create_time'] = date('Y-m-d H:i:s', $info['createtime']);
            if (!empty($info['updatetime'])) {
                $info['update_time'] = date('Y-m-d H:i:s', $info['updatetime']);
            }
        } catch (\Exception $e) {
            Log::error("获取失物招领详情异常: " . $e->getMessage());
            $errorMsg = '系统错误，请稍后再试';
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success('获取成功', ['info' => $info]);
        }
    }

    /**
     * 获取失物招领分类列表
     */
    public function getCategories()
    {
        $where = ['status' => 'normal'];

        // 可选参数：是否包含隐藏分类
        $includeHidden = $this->request->param('include_hidden/b', false);
        if ($includeHidden) {
            $where = [];
        }

        $categories = null;
        $errorMsg = '';

        try {
            $categories = Db::name('ds_lost_found_category')
                ->where($where)
                ->order('weight DESC')
                ->select();

            // 记录日志
            Log::info("获取失物招领分类列表: " . count($categories) . " 条记录");

        } catch (\Exception $e) {
            Log::error("获取失物招领分类列表异常: " . $e->getMessage());
            $errorMsg = '系统错误，请稍后再试';
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success('获取成功', $categories);
        }
    }
}
