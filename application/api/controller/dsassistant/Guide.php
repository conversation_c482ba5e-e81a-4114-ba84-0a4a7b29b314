<?php

namespace app\api\controller\dsassistant;

use think\Db;
use think\Log;

/**
 * 导览相关API
 */
class Guide extends Base
{
    /**
     * 获取景点列表
     */
    public function getScenicSpots()
    {
        $page = $this->request->param('page/d', 1);
        $pageSize = $this->request->param('pageSize/d', 10);
        $type = $this->request->param('type', '');
        $keyword = $this->request->param('keyword', '');

        $query = Db::name('ds_scenic_spot')
            ->alias('s')
            ->leftJoin('ds_scenic_spot_type t', 's.type_id = t.id')
            ->field('s.*, t.name as type_name, t.code as type_code, t.color as type_color, t.icon as type_icon')
            ->where('s.status', 'normal');

        // 按类型筛选
        if (!empty($type) && $type !== 'all') {
            $query->where('t.code', $type);
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $query->where('s.name|s.description', 'like', '%' . $keyword . '%');
        }

        $total = $query->count();
        $list = $query->order('s.weight DESC, s.id DESC')
            ->page($page, $pageSize)
            ->select();

        foreach ($list as &$item) {
            if (!empty($item['images'])) {
                $item['images'] = explode(',', $item['images']);
                $item['cover'] = $item['images'][0] ?? '';
            } else {
                $item['images'] = [];
                $item['cover'] = '';
            }
            
            // 添加类型信息
            $item['type'] = $item['type_code'] ?? '';
            $item['type_info'] = [
                'name' => $item['type_name'] ?? '',
                'code' => $item['type_code'] ?? '',
                'color' => $item['type_color'] ?? '#1890ff',
                'icon' => $item['type_icon'] ?? ''
            ];
        }

        $this->success('', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize
        ]);
    }

    /**
     * 获取景点类型列表
     */
    public function getScenicSpotTypes()
    {
        $list = Db::name('ds_scenic_spot_type')
            ->where('status', 'normal')
            ->order('weight DESC, id ASC')
            ->select();

        // 添加"全部"选项
        array_unshift($list, [
            'id' => 0,
            'name' => '全部',
            'code' => 'all',
            'icon' => 'icon-all',
            'color' => '#666666'
        ]);

        $this->success('', ['list' => $list]);
    }

    /**
     * 获取景点详情
     */
    public function getScenicSpotDetail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->error('参数错误：景点ID不能为空');
        }

        // 先检查景点是否存在，不考虑状态
        $spot = null;
        $errorMsg = '';

        try {
            $spot = Db::name('ds_scenic_spot')
                ->alias('s')
                ->leftJoin('ds_scenic_spot_type t', 's.type_id = t.id')
                ->field('s.*, t.name as type_name, t.code as type_code, t.color as type_color, t.icon as type_icon')
                ->where('s.id', $id)
                ->find();

            if (!$spot) {
                // 记录错误日志
                Log::error("景点不存在: ID = {$id}");

                // 获取所有景点ID列表，用于调试
                $allSpots = Db::name('ds_scenic_spot')
                    ->field('id, name, status')
                    ->select();
                Log::info("可用景点列表: " . json_encode($allSpots, JSON_UNESCAPED_UNICODE));

                $errorMsg = "景点不存在 (ID: {$id})";
                return;
            }

            // 检查景点状态
            if ($spot['status'] !== 'normal') {
                Log::warning("景点状态异常: ID = {$id}, 状态 = {$spot['status']}");
                $errorMsg = "该景点暂时不可用 (ID: {$id}, 状态: {$spot['status']})";
                return;
            }

            // 处理图片
            if (!empty($spot['images'])) {
                $spot['images'] = explode(',', $spot['images']);
            } else {
                $spot['images'] = [];
            }

            // 添加类型信息
            $spot['type'] = $spot['type_code'] ?? '';
            $spot['type_info'] = [
                'name' => $spot['type_name'] ?? '',
                'code' => $spot['type_code'] ?? '',
                'color' => $spot['type_color'] ?? '#1890ff',
                'icon' => $spot['type_icon'] ?? ''
            ];
        } catch (\Exception $e) {
            Log::error("获取景点详情异常: " . $e->getMessage());
            $errorMsg = '系统错误，请稍后再试';
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            $this->success('获取成功', ['info' => $spot]);
        }
    }

    /**
     * 获取预警信息
     */
    public function getWarnings()
    {
        $list = Db::name('ds_warning')
            ->where('status', 'normal')
            ->where('start_time', '<=', time())
            ->where('end_time', '>=', time())
            ->order('level DESC, id DESC')
            ->select();

        $this->success('', ['list' => $list]);
    }
}
