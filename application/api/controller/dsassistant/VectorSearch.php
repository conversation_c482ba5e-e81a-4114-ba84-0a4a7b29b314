<?php

namespace app\api\controller\dsassistant;

use app\common\controller\Api;
use think\Config;
use addons\dsassistant\library\VectorSearchFactory;
use app\admin\model\dsassistant\Contentconfig;

/**
 * 向量搜索API
 */
class VectorSearch extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口
    protected $noNeedRight = ['*'];
    
    /**
     * 搜索知识库
     */
    public function search()
    {
        $title = $this->request->post('title');
        $type = $this->request->post('type', ''); // 可选参数，指定使用哪种向量搜索实现
        $topK = $this->request->post('top_k', 5);
        
        if (empty($title)) {
            $this->error('问题不能为空');
        }
        
        try {
            // 根据请求参数或配置选择向量搜索实现
            if (!empty($type)) {
                // 使用指定类型的向量搜索
                $vectorSearch = VectorSearchFactory::create($type);
            } else {
                // 使用默认的向量搜索（由配置决定）
                $vectorSearch = VectorSearchFactory::getInstance();
            }
            
            // 执行搜索
            $results = $vectorSearch->search($title, $topK);
            $config = Contentconfig::getGroupConfig('vector_search');
            // 返回结果
            $this->success('', [
                'results' => $results,
                'type' => $type ?: $config['vector_search_type']?? 'enhanced',
                'count' => count($results)
            ]);
        } catch (\Exception $e) {
            $this->error('搜索失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 索引知识库
     */
    public function index()
    {
        $type = $this->request->post('type', ''); // 可选参数，指定使用哪种向量搜索实现
        
        try {
            // 根据请求参数或配置选择向量搜索实现
            if (!empty($type)) {
                // 使用指定类型的向量搜索
                $vectorSearch = VectorSearchFactory::create($type);
            } else {
                // 使用默认的向量搜索（由配置决定）
                $vectorSearch = VectorSearchFactory::getInstance();
            }
            
            // 执行索引
            $count = $vectorSearch->indexKnowledgeBase();
            
            // 返回结果
            $this->success('索引完成', [
                'count' => $count,
                'type' => $type ?: Config::get('dsassistant.vector_search_type', 'optimized')
            ]);
        } catch (\Exception $e) {
            $this->error('索引失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 索引单个知识条目
     */
    public function indexItem()
    {
        $id = $this->request->post('id');
        $type = $this->request->post('type', ''); // 可选参数，指定使用哪种向量搜索实现
        
        if (empty($id)) {
            $this->error('ID不能为空');
        }
        
        try {
            // 根据请求参数或配置选择向量搜索实现
            if (!empty($type)) {
                // 使用指定类型的向量搜索
                $vectorSearch = VectorSearchFactory::create($type);
            } else {
                // 使用默认的向量搜索（由配置决定）
                $vectorSearch = VectorSearchFactory::getInstance();
            }
            
            // 执行索引
            $knowledge = \app\admin\model\dsassistant\Knowledge::get($id);
            $result = $vectorSearch->indexSingleItem($knowledge);
            $config = Contentconfig::getGroupConfig('vector_search');
            
            // 返回结果
            if ($result) {
                $this->success('索引完成', [
                    'id' => $id,
                    'type' => $type ?: $config['vector_search_type']?? 'enhanced'
                ]);
            } else {
                $this->error('索引失败');
            }
        } catch (\Exception $e) {
            $this->error('索引失败: ' . $e->getMessage());
        }
    }
}
