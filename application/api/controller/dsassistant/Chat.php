<?php

namespace app\api\controller\dsassistant;

use app\common\model\DsAssistant;
use think\Log;
use think\Exception;

/**
 * 对话聊天API
 */
class Chat extends Base
{
    protected $assistant = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->assistant = new DsAssistant();
    }

    /**
     * 重置会话
     */
    public function resetSession()
    {
        $sessionId = $this->request->post('session_id', '');

        if (empty($sessionId)) {
            $this->error('会话ID不能为空');
        }

        $result = \addons\dsassistant\library\SessionManager::closeSession($sessionId);

        if ($result) {
            $this->success('会话已重置');
        } else {
            $this->error('会话重置失败');
        }
    }

    /**
     * 聊天接口
     */
    public function chat()
    {
        $question = $this->request->post('question');
        // 优先使用传入的user_id，如果没有则尝试使用设备ID，最后才使用IP
        $userId = $this->request->post('user_id', '');

        // 如果没有提供user_id，尝试使用设备ID
        if (empty($userId)) {
            $deviceId = $this->request->post('device_id', '');
            if (!empty($deviceId)) {
                $userId = 'device_' . $deviceId;
            } else {
                // 最后才使用IP+UA组合作为标识
                $ip = $this->request->ip();
                $ua = $this->request->header('user-agent', '');
                $userId = 'ip_' . md5($ip . $ua);
            }
        }
        $sessionId = $this->request->post('session_id', md5($userId . date('Ymd')));
        $platform = $this->request->post('platform', 'miniapp');
        $ip = $this->request->ip();

        if (empty($question)) {
            $this->error('问题不能为空');
        }

        // 检查是否是重置会话的命令
        if ($question === '/reset' || $question === '重置会话') {
            // 重置会话
            \addons\dsassistant\library\SessionManager::closeSession($sessionId);
            $this->success('', [
                'answer' => '会话已重置，我们可以开始新的对话了。',
                'session_id' => $sessionId
            ]);
            return;
        }

        $result = '';
        $errorMsg = '';

        try {
            $result = $this->assistant->handleQuestion($question, $userId, $sessionId, $platform, $ip);
        } catch (Exception $e) {
            Log::error('聊天接口异常: ' . $e->getMessage());
            $errorMsg = $e->getMessage();
        }

        // try-catch块外部进行响应
        if (!empty($errorMsg)) {
            $this->error($errorMsg);
        } else {
            // 检查返回结果是否包含函数调用
            if (is_array($result) && isset($result['answer']) && isset($result['functionCalls'])) {
                // 包含函数调用的响应
                $this->success('', [
                    'answer' => $result['answer'],
                    'functionCalls' => $result['functionCalls'],
                    'session_id' => $sessionId
                ]);
            } else {
                // 普通文本响应
                $this->success('', [
                    'answer' => $result,
                    'session_id' => $sessionId
                ]);
            }
        }
    }
}
