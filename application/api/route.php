<?php
// +----------------------------------------------------------------------
// | DSASSISTANT API路由配置
// +----------------------------------------------------------------------

use think\Route;

// DSASSISTANT 景区助理API路由组
Route::group('dsassistant', function () {

    // 对话聊天相关
    Route::group('chat', function () {
        Route::post('', 'dsassistant/Chat/chat');                    // 聊天接口
        Route::post('reset', 'dsassistant/Chat/resetSession');       // 重置会话
    });

    // 导览相关
    Route::group('guide', function () {
        Route::get('spots', 'dsassistant/Guide/getScenicSpots');           // 获取景点列表
        Route::get('spot-types', 'dsassistant/Guide/getScenicSpotTypes');  // 获取景点类型
        Route::get('spot/:id', 'dsassistant/Guide/getScenicSpotDetail');   // 获取景点详情
        Route::get('warnings', 'dsassistant/Guide/getWarnings');           // 获取预警信息
    });

    // 失物招领相关
    Route::group('lostfound', function () {
        Route::get('list', 'dsassistant/LostFound/getList');               // 获取失物招领列表
        Route::post('submit', 'dsassistant/LostFound/submit');             // 提交失物招领
        Route::get('detail/:id', 'dsassistant/LostFound/getDetail');       // 获取失物招领详情
        Route::get('categories', 'dsassistant/LostFound/getCategories');   // 获取分类列表
    });

    // 反馈相关
    Route::group('feedback', function () {
        Route::post('submit', 'dsassistant/Feedback/submit');              // 提交反馈
    });

});

// 兼容旧版API路由（保持向后兼容）
Route::post('chat', 'dsassistant/Chat/chat');
Route::post('resetSession', 'dsassistant/Chat/resetSession');
Route::get('getScenicSpots', 'dsassistant/Guide/getScenicSpots');
Route::get('getScenicSpotTypes', 'dsassistant/Guide/getScenicSpotTypes');
Route::get('getScenicSpotDetail', 'dsassistant/Guide/getScenicSpotDetail');
Route::get('getWarnings', 'dsassistant/Guide/getWarnings');
Route::get('getLostFound', 'dsassistant/LostFound/getList');
Route::post('submitLostFound', 'dsassistant/LostFound/submit');
Route::get('getLostFoundDetail', 'dsassistant/LostFound/getDetail');
Route::get('getLostFoundCategories', 'dsassistant/LostFound/getCategories');
Route::post('submitFeedback', 'dsassistant/Feedback/submit');

return [];
