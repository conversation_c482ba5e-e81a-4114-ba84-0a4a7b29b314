<?php
// +----------------------------------------------------------------------
// | DSASSISTANT API路由配置
// +----------------------------------------------------------------------

use think\Route;

// 对话聊天相关
Route::group('chat', function () {
    Route::post('', 'Chat/chat');                    // 聊天接口
    Route::post('reset', 'Chat/resetSession');       // 重置会话
});

// 导览相关
Route::group('guide', function () {
    Route::get('spots', 'Guide/getScenicSpots');           // 获取景点列表
    Route::get('spot-types', 'Guide/getScenicSpotTypes');  // 获取景点类型
    Route::get('spot/:id', 'Guide/getScenicSpotDetail');   // 获取景点详情
    Route::get('warnings', 'Guide/getWarnings');           // 获取预警信息
});

// 失物招领相关
Route::group('lostfound', function () {
    Route::get('list', 'LostFound/getList');               // 获取失物招领列表
    Route::post('submit', 'LostFound/submit');             // 提交失物招领
    Route::get('detail/:id', 'LostFound/getDetail');       // 获取失物招领详情
    Route::get('categories', 'LostFound/getCategories');   // 获取分类列表
});

// 反馈相关
Route::group('feedback', function () {
    Route::post('submit', 'Feedback/submit');              // 提交反馈
});

// 兼容旧版API路由（保持向后兼容）
Route::post('chat', 'Chat/chat');
Route::post('resetSession', 'Chat/resetSession');
Route::get('getScenicSpots', 'Guide/getScenicSpots');
Route::get('getScenicSpotTypes', 'Guide/getScenicSpotTypes');
Route::get('getScenicSpotDetail', 'Guide/getScenicSpotDetail');
Route::get('getWarnings', 'Guide/getWarnings');
Route::get('getLostFound', 'LostFound/getList');
Route::post('submitLostFound', 'LostFound/submit');
Route::get('getLostFoundDetail', 'LostFound/getDetail');
Route::get('getLostFoundCategories', 'LostFound/getCategories');
Route::post('submitFeedback', 'Feedback/submit');

return [];
