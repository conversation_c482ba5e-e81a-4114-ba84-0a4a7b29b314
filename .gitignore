# IDE和编辑器文件
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
*.swp
*.swo
.DS_Store
Thumbs.db

# Composer依赖
/vendor/
composer.lock

# ThinkPHP和FastAdmin运行时文件
/runtime/
/public/uploads/
/public/assets/libs/
/public/storage/
.env
.env.backup

# 日志文件
*.log
/logs/

# 缓存文件
/cache/
*.cache

# 配置文件（可能包含敏感信息）
/application/database.php
/application/config/database.php
/application/extra/site.php

# 临时文件
/temp/
*.tmp

# 备份文件
*.bak
*.backup

# 其他不需要版本控制的文件
node_modules/
npm-debug.log
yarn-error.log

# uniapp项目特定文件
/addons/dsassistant/uniapp/node_modules/
/addons/dsassistant/uniapp/unpackage/
/addons/dsassistant/uniapp/dist/
/addons/dsassistant/uniapp/.hbuilderx/
/addons/dsassistant/uniapp/package-lock.json
/addons/dsassistant/uniapp/yarn.lock
/addons/dsassistant/uniapp/.DS_Store
/addons/dsassistant/uniapp/.npmrc
/addons/dsassistant/uniapp/.gitignore
#/addons/dsassistant/uniapp/dsassistant/node_modules/
/addons/dsassistant/uniapp/dsassistant/unpackage/
/addons/dsassistant/uniapp/dsassistant/dist/
/addons/dsassistant/uniapp/dsassistant/.hbuilderx/
/addons/dsassistant/uniapp/dsassistant/package-lock.json
/addons/dsassistant/uniapp/dsassistant/yarn.lock
/addons/dsassistant/uniapp/dsassistant/.DS_Store
/addons/dsassistant/uniapp/dsassistant/.npmrc
/addons/dsassistant/uniapp/dsassistant/.gitignore

# uniapp编译生成的临时文件
*.wxss
*.wxs
*.wxml
*.swan
*.swanss
*.ttss
*.ttml
*.ux
*.quickapp.js
*.quickapp.json
*.quickapp.style
*.quickapp.template
*.axml
*.acss
*.json.map
*.js.map
*.css.map
