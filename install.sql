-- ----------------------------
-- 景区DeepSeek智能助理插件数据库表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_dict` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `parent` varchar(50) NOT NULL DEFAULT 'root' COMMENT '父级',
  `name` varchar(50) NOT NULL COMMENT '名称',
  `title` varchar(50) NOT NULL COMMENT '标题',
  `remark` varchar(255) NULL COMMENT '备注',
  `weight` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `parent_name` (`parent`,`name`),
  KEY `idx_parent` (`parent`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典表';

CREATE TABLE IF NOT EXISTS `fa_ds_category_rule` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category` varchar(50) NOT NULL COMMENT '分类名称',
  `pattern` text NOT NULL COMMENT '匹配模式(正则表达式)',
  `boost` decimal(10,2) DEFAULT '0.10' COMMENT '分类加分',
  `weight` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识分类规则表';


CREATE TABLE IF NOT EXISTS `fa_ds_keyword_map` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `main_keyword` varchar(50) NOT NULL COMMENT '主关键词',
  `related_keywords` text COMMENT '相关关键词',
  `category_id` int(10) DEFAULT NULL COMMENT '所属分类ID',
  `weight` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_main_keyword` (`main_keyword`),
  KEY `idx_category` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键词映射表';

-- ----------------------------
-- 知识库表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_knowledge` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category_id` int(10) unsigned DEFAULT NULL COMMENT '主分类ID',
  `source_id` int(10) DEFAULT NULL COMMENT '来源ID',
  `has_vector` TINYINT(1) DEFAULT 0 COMMENT '是否已生成向量数据:0=未生成,1=已生成',
  `vector_time` INT(10) UNSIGNED DEFAULT NULL COMMENT '向量生成时间',
  `valid_from` int(10) unsigned DEFAULT NULL COMMENT '有效期开始',
  `valid_until` int(10) unsigned DEFAULT NULL COMMENT '有效期结束',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `weight` int(10) unsigned DEFAULT '0' COMMENT '权重',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
  `title` varchar(255) NOT NULL COMMENT '知识标题',
  `tags` varchar(255) DEFAULT '' COMMENT '标签/关键词',
  `content_type` varchar(20) DEFAULT 'description' COMMENT '内容类型',
  `source_type` varchar(20) DEFAULT NULL COMMENT '来源类型',
  `content` text NOT NULL COMMENT '知识内容',
  `structured_data` text COMMENT '结构化数据(JSON格式)',
  `media_refs` text COMMENT '媒体引用(JSON格式)',
  `segmented_text` text COMMENT '分词文本',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `content_type` (`content_type`),
  KEY `source_type` (`source_type`),
  KEY `valid_from` (`valid_from`),
  KEY `valid_until` (`valid_until`),
  KEY `has_vector` (`has_vector`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='知识库';
-- 修改表结构，添加全文索引
-- ALTER TABLE fa_ds_knowledge ADD FULLTEXT INDEX ft_question_keywords (question, keywords) WITH PARSER ngram;
-- 创建全文索引
ALTER TABLE fa_ds_knowledge ADD FULLTEXT INDEX ft_segmented (segmented_text);

-- ----------------------------
-- 会话状态表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_session_state` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `session_id` varchar(50) NOT NULL COMMENT '会话ID',
  `user_id` varchar(50) DEFAULT '' COMMENT '用户ID',
  `context` text COMMENT '对话上下文',
  `last_question` text COMMENT '最后一个问题',
  `last_answer` text COMMENT '最后一个回答',
  `platform` enum('wechat','miniapp','web') DEFAULT 'web' COMMENT '平台:wechat=微信,miniapp=小程序,web=网页',
  `status` enum('active','closed') DEFAULT 'active' COMMENT '状态:active=活动,closed=关闭',
  `expire_time` int(10) unsigned DEFAULT NULL COMMENT '过期时间',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  KEY `expire_time` (`expire_time`),
  KEY `status` (`status`),
  KEY `platform` (`platform`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='会话状态表';


-- ----------------------------
-- 聊天记录表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_chat_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` varchar(50) DEFAULT '' COMMENT '用户ID',
  `session_id` varchar(50) DEFAULT '' COMMENT '会话ID',
  `question` text COMMENT '问题',
  `answer` text COMMENT '答案',
  `source` enum('local','ai') DEFAULT 'local' COMMENT '答案来源',
  `score` decimal(5,2) DEFAULT '0.00' COMMENT '匹配得分',
  `platform` enum('wechat','miniapp','web') DEFAULT 'wechat' COMMENT '平台:wechat=微信,miniapp=小程序,web=网页',
  `ip` varchar(50) DEFAULT '' COMMENT 'IP地址',
  `extra` text COMMENT '额外信息',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `session_id` (`session_id`),
  KEY `createtime` (`createtime`),
  KEY `platform` (`platform`),
  KEY `source` (`source`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='聊天记录表';

-- ----------------------------
-- 预警信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_warning` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `type` enum('crowd','weather','traffic','other') DEFAULT 'other' COMMENT '类型:crowd=人流,weather=天气,traffic=交通,other=其他',
  `level` enum('info','warning','danger') DEFAULT 'info' COMMENT '级别:info=提示,warning=警告,danger=危险',
  `start_time` int(10) unsigned DEFAULT NULL COMMENT '开始时间',
  `end_time` int(10) unsigned DEFAULT NULL COMMENT '结束时间',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `level` (`level`),
  KEY `status` (`status`),
  KEY `start_time` (`start_time`),
  KEY `end_time` (`end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='预警信息表';


CREATE TABLE IF NOT EXISTS `fa_ds_warning_push_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `warning_id` int(11) NOT NULL COMMENT '预警ID',
  `total_users` int(11) DEFAULT '0' COMMENT '总用户数',
  `processed_users` int(11) DEFAULT '0' COMMENT '已处理用户数',
  `success_users` int(11) DEFAULT '0' COMMENT '成功用户数',
  `fail_users` int(11) DEFAULT '0' COMMENT '失败用户数',
  `status` enum('pending','processing','completed','failed') DEFAULT 'pending' COMMENT '状态',
  `last_user_id` int(11) DEFAULT '0' COMMENT '上次处理的用户ID',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `warning_id` (`warning_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警推送任务';

CREATE TABLE IF NOT EXISTS `fa_ds_warning_push_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `warning_id` int(11) NOT NULL COMMENT '预警ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信OpenID',
  `status` enum('pending','success','fail') DEFAULT 'pending' COMMENT '状态',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `retry_count` int(11) DEFAULT '0' COMMENT '重试次数',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `warning_id` (`warning_id`),
  KEY `openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警推送记录';


CREATE TABLE IF NOT EXISTS `fa_ds_lost_found_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category` varchar(50) NOT NULL COMMENT '分类名称',
  `weight` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='失物招领表';

-- ----------------------------
-- 失物招领表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_lost_found` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `description` text COMMENT '描述',
  `type` enum('lost','found') DEFAULT 'lost' COMMENT '类型:lost=寻物,found=招领',
  `category_id` varchar(50) DEFAULT '' COMMENT '物品类别ID',
  `location` varchar(100) DEFAULT '' COMMENT '地点',
  `contact_name` varchar(50) DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT '' COMMENT '联系电话',
  `images` varchar(1000) DEFAULT '' COMMENT '图片',
  `status` enum('pending','processed','closed') DEFAULT 'pending' COMMENT '状态:pending=等待,processed=已处理,closed=已关闭',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `category_id` (`category_id`),
  KEY `status` (`status`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='失物招领表';


CREATE TABLE IF NOT EXISTS `fa_ds_scenic` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '区域名称',
  `description` text COMMENT '描述',
  `type` enum('circle','polygon','rectangle') DEFAULT 'circle' COMMENT '区域类型:circle=圆形,polygon=多边形,rectangle=矩形',
  `center_lat` decimal(10,7) DEFAULT NULL COMMENT '中心点纬度',
  `center_lng` decimal(10,7) DEFAULT NULL COMMENT '中心点经度',
  `radius` int(11) DEFAULT NULL COMMENT '半径(米)',
  `points` text COMMENT '多边形顶点坐标',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='景区';

-- ----------------------------
-- 景点信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_scenic_spot` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `scenic_id` int(11) NOT NULL COMMENT '景区ID',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `description` text COMMENT '描述',
  `longitude` decimal(10,6) DEFAULT '0.000000' COMMENT '经度',
  `latitude` decimal(10,6) DEFAULT '0.000000' COMMENT '纬度',
  `address` varchar(255) DEFAULT '' COMMENT '地址',
  `opening_hours` varchar(255) DEFAULT '' COMMENT '开放时间',
  `ticket_price` varchar(255) DEFAULT '' COMMENT '票价',
  `images` varchar(1000) DEFAULT '' COMMENT '图片',
  `tips` text COMMENT '游玩提示',
  `weight` int(10) unsigned DEFAULT '0' COMMENT '排序权重',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `name` (`name`),
  KEY `scenic_id` (`scenic_id`),
  KEY `weight` (`weight`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='景点信息表';


CREATE TABLE IF NOT EXISTS `fa_ds_user_location_pool` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) NOT NULL COMMENT '微信OpenID',
  `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `in_scenic` tinyint(1) DEFAULT '1' COMMENT '是否在景区内',
  `last_report_time` int(10) DEFAULT NULL COMMENT '最后上报时间',
  `expire_time` int(10) DEFAULT NULL COMMENT '过期时间',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`),
  KEY `expire_time` (`expire_time`),
  KEY `in_scenic` (`in_scenic`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户位置池';


CREATE TABLE IF NOT EXISTS `fa_ds_user_wechat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信OpenID',
  `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `subscribe` tinyint(1) DEFAULT '0' COMMENT '是否关注',
  `subscribe_time` int(10) DEFAULT NULL COMMENT '关注时间',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户微信关联';


-- 创建配置表
CREATE TABLE IF NOT EXISTS `fa_ds_content_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '变量名',
  `group` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '分组',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '类型',
  `visible` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '可见条件',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '变量值',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '变量字典数据',
  `rule` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '扩展属性',
  `setting` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '配置',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='参数配置';

-- 创建页面内容表
CREATE TABLE IF NOT EXISTS `fa_ds_page_content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page` varchar(50) NOT NULL COMMENT '页面标识',
  `section` varchar(50) NOT NULL COMMENT '页面区块',
  `key` varchar(50) NOT NULL COMMENT '内容键名',
  `value` text COMMENT '内容值',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `page_section_key` (`page`,`section`,`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面内容';

-- ----------------------------
-- 向量数据库表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_knowledge_vectors` (
  `id` VARCHAR(64) PRIMARY KEY COMMENT '向量ID，通常是知识库ID',
  `vector` LONGTEXT NOT NULL COMMENT '向量数据，JSON格式存储的浮点数数组',
  `metadata` TEXT COMMENT '元数据，JSON格式存储的附加信息',
  `category_id` INT COMMENT '分类ID，冗余字段用于快速过滤',
  `category` VARCHAR(50) COMMENT '分类名称，冗余字段用于显示',
  `tags` TEXT COMMENT '标签/关键词，逗号分隔的字符串',
  `weight` INT DEFAULT 0 COMMENT '权重值，用于调整搜索结果排序',
  `status` VARCHAR(20) DEFAULT 'normal' COMMENT '状态：normal=正常，hidden=隐藏',
  `valid_from` INT COMMENT '有效期开始时间戳',
  `valid_until` INT COMMENT '有效期结束时间戳',
  `createtime` INT(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `updatetime` INT(10) UNSIGNED NOT NULL COMMENT '更新时间',
  INDEX idx_category_id (category_id) COMMENT '分类ID索引',
  INDEX idx_status (status) COMMENT '状态索引',
  INDEX idx_weight (weight) COMMENT '权重索引',
  INDEX idx_valid (valid_from, valid_until) COMMENT '有效期索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库向量表 - 存储向量嵌入和元数据，用于语义搜索';


CREATE TABLE IF NOT EXISTS `fa_ds_converter_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int(10) unsigned DEFAULT NULL COMMENT '主分类ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `source_table` varchar(100) NOT NULL COMMENT '数据源表名',
  `target_table` varchar(100) NOT NULL DEFAULT 'ds_knowledge' COMMENT '目标表名',
  `source_type` varchar(30) NOT NULL COMMENT '数据类型',
  `content_type` varchar(30) NOT NULL COMMENT '内容类型',
  `template_content` text NOT NULL COMMENT '模板内容(JSON)',
  `sync_mode` enum('full','increment') NOT NULL DEFAULT 'full' COMMENT '同步模式',
  `sync_field` varchar(50) DEFAULT NULL COMMENT '增量同步字段',
  `last_sync_time` int(10) DEFAULT NULL COMMENT '上次同步时间',
  `last_sync_id` int(10) DEFAULT NULL COMMENT '上次同步ID',
  `where_condition` varchar(255) DEFAULT NULL COMMENT '查询条件',
  `order_field` varchar(50) DEFAULT 'id' COMMENT '排序字段',
  `order_direction` enum('asc','desc') DEFAULT 'asc' COMMENT '排序方向',
  `batch_size` int(10) DEFAULT 100 COMMENT '每批处理数量',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX idx_category_id (category_id) COMMENT '主分类ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转换模板';