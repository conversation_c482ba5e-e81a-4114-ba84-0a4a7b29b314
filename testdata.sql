
-- ----------------------------
-- 初始数据
-- ----------------------------

-- 分类规则测试数据
INSERT INTO `fa_ds_category_rule` (`category`, `pattern`, `boost`, `status`, `createtime`, `updatetime`) VALUES
('票务', '门票|价格|多少钱|费用|优惠|便宜|贵|学生票|儿童票|老人票|免费|收费', 0.15, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('开放时间', '时间|几点|开门|关门|营业|开放|关闭|闭园|结束|停止|参观时间', 0.12, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('位置信息', '位置|在哪|怎么走|地址|地点|方位|交通|路线|怎么去|公交|地铁|自驾|打车|导航', 0.12, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('景点推荐', '特色|推荐|好玩|景点|必去|著名|游玩|参观|值得|热门|特点|亮点|独特|有名', 0.10, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('餐饮', '餐饮|吃饭|美食|小吃|餐厅|饭店|吃的|好吃|特色|美味|特产|用餐|就餐|饮食', 0.10, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('设施服务', '设施|厕所|洗手间|休息|停车|充电|WiFi|网络|停车场|泊车|车位|停靠|方便', 0.08, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('活动演出', '活动|表演|节目|演出|体验|参与|互动|秀', 0.10, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('青山瀑布', '青山瀑布|瀑布|青山|水流|水景', 0.20, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('翠谷幽林', '翠谷幽林|翠谷|幽林|森林|树林', 0.20, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('天空栈道', '天空栈道|栈道|天空|高空|玻璃|刺激', 0.20, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('古镇风情街', '古镇风情街|古镇|风情|街道', 0.20, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('百花园', '百花园|花园|鲜花|花卉|赏花', 0.20, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 关键词映射测试数据
INSERT INTO `fa_ds_keyword_map` (`main_keyword`, `related_keywords`, `category_id`, `weight`, `status`, `createtime`, `updatetime`) VALUES
-- 门票相关
('门票', '票价,价格,多少钱,优惠,学生票,儿童票,老人票,免费,收费', 1, 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('价格', '门票,多少钱,票价,费用,花费,便宜,贵', 1, 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('优惠', '折扣,打折,减免,便宜,学生,儿童,老人,团购', 1, 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 时间相关
('时间', '开放,关门,几点,营业时间,开门,关闭,几点到几点', 2, 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('开放', '时间,几点,营业,开门,参观', 2, 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('关门', '时间,几点,闭园,结束,停止', 2, 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 位置相关
('位置', '在哪里,怎么去,地址,路线,方位,地点,交通', 3, 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('交通', '怎么去,公交,地铁,自驾,打车,路线,到达', 3, 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('路线', '怎么走,路径,导航,地图,方向', 3, 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 景点相关
('景点', '景区,好玩,推荐,著名,特色,必去,游玩', 4, 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('推荐', '好玩,值得,必去,著名,热门,特色', 4, 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('特色', '特点,亮点,独特,有名,著名', 4, 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 餐饮相关
('餐饮', '吃饭,餐厅,美食,小吃,饭店,食物,吃的', 5, 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('美食', '好吃,特色,小吃,餐厅,美味,特产', 5, 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('餐厅', '吃饭,饭店,用餐,就餐,饮食', 5, 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 设施相关
('设施', '厕所,洗手间,休息,停车,充电,WiFi,网络', 6, 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('停车', '停车场,泊车,车位,停靠,自驾', 6, 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('卫生间', '厕所,洗手间,方便,休息', 6, 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 活动相关
('活动', '表演,节目,演出,体验,参与,互动', 7, 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('表演', '节目,演出,秀,时间,几点', 7, 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 青山瀑布相关
('青山瀑布', '瀑布,青山,水流,水景', 8, 120, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 翠谷幽林相关
('翠谷幽林', '翠谷,幽林,森林,树林,植物', 9, 120, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 天空栈道相关
('天空栈道', '栈道,天空,高空,玻璃,刺激', 10, 120, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 古镇风情街相关
('古镇风情街', '古镇,风情,街道,小吃,购物', 11, 120, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 百花园相关
('百花园', '花园,鲜花,花卉,植物,赏花', 12, 120, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());


-- 添加一些示例知识库数据
INSERT INTO `fa_ds_knowledge` (`title`, `content`, `category_id`, `tags`, `segmented_text`, `weight`, `valid_from`, `valid_until`, `status`, `createtime`, `updatetime`) VALUES
('景区门票多少钱？', '我们景区的门票价格为：成人票100元/人，儿童票（1.2米以下）免费，老人票（65岁以上）50元/人。', 5, '门票,价格,多少钱', '景区 门票 多少钱 价格 成人票 儿童票 老人票 免费', 100, NULL, NULL, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('景区开放时间是几点到几点？', '我们景区的开放时间为：旺季（4月-10月）8:00-18:00，淡季（11月-次年3月）8:30-17:30。', 1, '开放时间,几点,营业时间', '景区 开放时间 几点 营业时间 旺季 淡季', 100, NULL, NULL, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('景区有哪些著名景点？', '我们景区的著名景点包括：青山瀑布、翠谷幽林、天空栈道、古镇风情街、百花园等。每个景点都有其独特的魅力，建议您根据自己的兴趣和时间安排游览路线。', 3, '景点,著名,推荐', '景区 著名 景点 青山瀑布 翠谷幽林 天空栈道 古镇风情街 百花园 推荐', 90, NULL, NULL, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('如何前往景区？', '您可以通过以下方式前往景区：\n1. 自驾：导航搜索"XX景区"即可\n2. 公共交通：乘坐X路公交车到XX站下车\n3. 旅游专线：在市区旅游集散中心乘坐旅游专线直达景区\n4. 出租车：从市区打车约30分钟，费用约50元', 4, '怎么去,交通,路线', '如何 前往 景区 自驾 公共交通 旅游专线 出租车 路线', 90, NULL, NULL, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('景区内有餐厅吗？', '是的，景区内有多家餐厅和小吃店，提供各种美食选择。主要餐厅位于游客中心附近和古镇风情街上，提供当地特色菜和常见快餐。价格从30元到100元不等，视您的选择而定。', 7, '吃饭,餐厅,美食', '景区 餐厅 吃饭 美食 小吃店 游客中心 古镇风情街 特色菜 快餐', 80, NULL, NULL, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('暑假期间有什么特别活动？', '今年暑假期间（7月1日-8月31日），我们景区推出了"夏日狂欢"系列活动，包括水上嘉年华、夜间灯光秀、民俗表演等。其中，每周五晚上的"星空音乐会"特别受欢迎，建议提前预约。', 6, '暑假,活动,夏日,表演', '暑假 特别 活动 夏日狂欢 水上嘉年华 夜间灯光秀 民俗表演 星空音乐会 预约', 85, UNIX_TIMESTAMP('2023-07-01'), UNIX_TIMESTAMP('2023-08-31'), 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('冬季滑雪场开放时间？', '我们景区的冬季滑雪场将于12月15日至次年3月1日开放，开放时间为9:00-16:30。滑雪场提供初级、中级和高级雪道，以及儿童雪乐园。租赁滑雪装备每套150元/天，教练服务另计。', 11, '滑雪,冬季,雪道', '冬季 滑雪场 开放时间 雪道 儿童雪乐园 租赁 装备 教练', 85, UNIX_TIMESTAMP('2023-12-15'), UNIX_TIMESTAMP('2024-03-01'), 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加景区信息
INSERT INTO `fa_ds_scenic`
(`name`, `description`, `type`, `center_lat`, `center_lng`, `radius`, `points`, `status`, `createtime`, `updatetime`)
VALUES
('测试景区', '这是一个测试用的圆形景区范围', 'circle', 39.9087127, 116.3974900, 1000, NULL, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加景点类型数据
INSERT INTO `fa_ds_scenic_spot_type` (`name`, `code`, `icon`, `color`, `description`, `weight`, `status`, `createtime`, `updatetime`) VALUES
('自然风光', 'nature', 'icon-nature', '#52c41a', '自然景观、山水风光等', 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('历史古迹', 'history', 'icon-history', '#722ed1', '历史建筑、古迹遗址等', 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('文化场馆', 'culture', 'icon-culture', '#1890ff', '博物馆、展览馆、文化中心等', 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('休闲娱乐', 'entertainment', 'icon-entertainment', '#fa541c', '游乐设施、娱乐项目等', 70, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('餐饮购物', 'shopping', 'icon-shopping', '#eb2f96', '餐厅、商店、购物中心等', 60, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('服务设施', 'service', 'icon-service', '#13c2c2', '停车场、洗手间、服务中心等', 50, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加一些示例景点数据
INSERT INTO `fa_ds_scenic_spot` (`scenic_id`, `type_id`, `name`, `description`, `longitude`, `latitude`, `address`, `opening_hours`, `ticket_price`, `images`, `tips`, `weight`, `status`, `createtime`, `updatetime`) VALUES
(1, 1, '青山瀑布', '青山瀑布是景区内最著名的自然景观，瀑布高达50米，水流湍急，气势磅礴。站在观景台上，可以感受到大自然的壮丽与神奇。', '116.397428', '39.90923', '景区东北角', '全天开放', '免门票（需景区大门票）', '', '建议上午9点-11点游览，光线最佳，可以看到彩虹；请注意安全，不要在瀑布下方嬉戏。', 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 1, '翠谷幽林', '翠谷幽林是一片原始森林区域，树龄多在百年以上，林中空气清新，负氧离子含量高，是避暑纳凉、森林浴的绝佳去处。', '116.398428', '39.91023', '景区西部区域', '8:30-17:00', '免门票（需景区大门票）', '', '建议穿舒适的鞋子，带好防蚊虫喷雾；林中有标识清晰的步道，请勿随意离开步道以免迷路。', 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 4, '天空栈道', '天空栈道建在悬崖峭壁上，全长300米，高度达120米，栈道为透明玻璃结构，走在上面可以直接看到脚下的峡谷，刺激而震撼。', '116.396428', '39.90823', '景区南部山顶', '9:00-16:30（雨雪天气关闭）', '另收费30元/人', '', '恐高症患者慎入；请勿在栈道上跳跃或奔跑；拍照时注意安全；雷雨天气会临时关闭。', 95, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 5, '古镇风情街', '古镇风情街保留了明清时期的建筑风格，街道两旁是各种特色小店，包括手工艺品、当地特产、传统小吃等，是体验当地文化和购物的好去处。', '116.399428', '39.90723', '景区中心区域', '8:00-21:00', '免门票（需景区大门票）', '', '晚上有灯光秀，非常美丽；建议品尝当地特色小吃如糖油粑粑、豆腐脑等；购物时可适当讲价。', 85, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 1, '百花园', '百花园占地面积达50亩，种植了上百种花卉植物，四季都有不同的花卉盛开，春有樱花，夏有荷花，秋有菊花，冬有梅花，一年四季都很美丽。', '116.395428', '39.90623', '景区西北角', '8:00-18:00', '免门票（需景区大门票）', '', '春季3-5月和秋季9-10月是最佳观赏期；园内设有休息亭和茶座，可以小憩；爱拍照的游客可以带上合适的拍摄装备。', 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 2, '古寺遗址', '古寺遗址是一座有着千年历史的古寺，虽然现在只剩下部分建筑遗迹，但仍能感受到当年的宏伟气势，是了解当地历史文化的重要场所。', '116.400428', '39.90523', '景区东南角', '8:30-17:30', '免门票（需景区大门票）', '', '遗址内有文物保护标识，请勿触摸；建议请导游讲解，更能了解历史背景；适合对历史文化感兴趣的游客。', 75, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 3, '民俗博物馆', '民俗博物馆展示了当地的传统文化和民俗风情，馆内收藏了大量的民间文物、传统服饰、手工艺品等，是了解当地文化的窗口。', '116.398928', '39.90423', '景区游客中心旁', '9:00-17:00', '免门票（需景区大门票）', '', '每天上午10点和下午3点有免费讲解服务；馆内禁止拍照；适合带孩子的家庭游客参观学习。', 70, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 6, '游客服务中心', '游客服务中心提供各种旅游服务，包括咨询、导览、行李寄存、医疗急救等，是游客在景区的重要服务保障。', '116.398128', '39.90323', '景区入口处', '7:30-18:30', '免费', '', '提供免费WiFi、充电服务；有专业导游可预约；紧急情况可寻求帮助；建议游览前先到此处了解景区信息。', 65, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加一些示例预警信息
INSERT INTO `fa_ds_warning` (`title`, `content`, `type`, `level`, `start_time`, `end_time`, `status`, `createtime`, `updatetime`) VALUES
('周末游客量大提醒', '预计本周末（5月1日-5月3日）游客量将达到平日的3倍以上，景区可能出现排队现象，建议错峰出行，尽量避开10:00-14:00的高峰时段。', 'crowd', 'info', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()+604800, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('暴雨天气预警', '气象部门预报，今日下午14:00-18:00可能有强降雨，景区部分户外项目可能临时关闭，请游客注意安全，携带雨具，避免在山区和水域附近逗留。', 'weather', 'warning', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()+86400, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('道路施工公告', '景区南门至游客中心道路正在进行维修施工，预计施工期为6月1日-6月10日，期间可能造成交通缓慢，建议从东门或北门进入景区。', 'traffic', 'info', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()+864000, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加一些示例失物招领分类信息
INSERT INTO `fa_ds_lost_found_category`
(`category`, `weight`, `status`, `createtime`, `updatetime`)
VALUES
('证件', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('钱包', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('手机', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('背包', 4, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('钥匙', 5, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('其他', 99, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加一些示例失物招领信息
INSERT INTO `fa_ds_lost_found` (`title`, `description`, `type`, `category_id`, `location`, `contact_name`, `contact_phone`, `status`, `createtime`, `updatetime`) VALUES
('寻找黑色小米手机', '5月20日在青山瀑布观景台不慎遗失一部黑色小米11手机，有红色手机壳，锁屏壁纸是一只柯基犬。', 'lost', 3, '青山瀑布观景台', '张先生', '13800138000', 'pending', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('招领蓝色儿童背包', '5月21日在游客中心附近拾到一个蓝色儿童背包，内有水杯、零食和一本绘本，失主可联系认领。', 'found', 4, '游客中心', '李工作人员', '13900139000', 'pending', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('寻找粉色钱包', '5月19日在古镇风情街购物时不慎遗失一个粉色长款钱包，内有身份证、银行卡和少量现金，身份证姓名为王小明。', 'lost', 2, '古镇风情街', '王小明', '13700137000', 'processed', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());



-- 插入默认全局配置
INSERT INTO `fa_ds_content_config` (`name`, `title`, `value`, `type`, `group`, `tip`) VALUES

('site_name', '网站名称','景区智能助理', 'string', 'basic', '网站名称'),
('site_description', '网站描述','基于国内大模型技术，为游客提供全方位智能服务', 'string', 'basic', '网站描述'),
('copyright','版权信息', '© 2024 景区智能助理 - 基于国内大模型技术', 'string', 'basic', '版权信息'),
('contact_address', '联系地址','景区管理中心', 'string', 'contact', '联系地址'),
('contact_phone', '联系电话','************', 'string', 'contact', '联系电话'),
('contact_email', '联系邮箱','<EMAIL>', 'string', 'contact', '联系邮箱');

-- 插入首页内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('index', 'header', 'title', '景区智能助理', '首页标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'header', 'subtitle', '基于国内大模型技术，为游客提供全方位智能服务', '首页副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'header', 'button_text', '立即咨询', '咨询按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'title', '核心功能', '功能区标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature1_title', '智能问答', '功能1标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature1_desc', '基于国内大模型技术，解答游客各类问题，提供精准信息服务', '功能1描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature2_title', '实时预警', '功能2标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature2_desc', '及时推送人流高峰、极端天气等预警信息，保障游客安全', '功能2描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature3_title', '电子导览', '功能3标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature3_desc', '提供景区电子地图导航，让游客轻松找到目的地', '功能3描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature4_title', '失物招领', '功能4标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature4_desc', '提供失物登记与查询入口，帮助游客找回丢失物品', '功能4描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'title', '多种使用方式', '使用方式区标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage1_title', '微信公众号', '使用方式1标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage1_desc', '关注公众号，随时随地获取景区信息', '使用方式1描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage2_title', '小程序', '使用方式2标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage2_desc', '扫码进入小程序，享受更丰富功能', '使用方式2描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage3_title', '官方网站', '使用方式3标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage3_desc', '访问官网，体验全功能服务', '使用方式3描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入智能问答页面内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('chat', 'header', 'title', '智能问答', '页面标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'header', 'subtitle', '有任何问题，随时向我提问', '页面副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'greeting', '您好！我是景区智能助理，很高兴为您服务。', '欢迎语', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'intro', '您可以向我咨询以下内容：', '介绍文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'topic1', '景区开放时间和门票信息', '咨询主题1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'topic2', '景点介绍和游览路线推荐', '咨询主题2', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'topic3', '交通指南和周边住宿', '咨询主题3', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'topic4', '景区活动和特色服务', '咨询主题4', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'closing', '请问有什么可以帮助您的？', '结束语', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'input', 'placeholder', '请输入您的问题...', '输入框提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'input', 'button_text', '发送', '发送按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'status', 'thinking', '助理正在思考', '思考状态文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'status', 'error', '抱歉，出现了一些问题', '错误消息', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'status', 'network_error', '网络连接出现问题，请稍后再试', '网络错误消息', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入景点导览页面内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('guide', 'header', 'title', '景点导览', '页面标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'header', 'subtitle', '探索景区的精彩景点，规划您的完美旅程', '页面副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'map', 'title', '景区地图', '地图标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'map', 'description', '点击地图上的标记查看景点详情，规划您的游览路线', '地图描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'map', 'scroll_hint', '向下滑动查看热门景点', '滚动提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spots', 'title', '热门景点', '景点列表标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spots', 'loading', '正在加载景点信息...', '加载提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spots', 'empty', '暂无景点信息', '空数据提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spots', 'error', '加载景点信息失败，请刷新页面重试', '错误提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spot_card', 'badge', '热门景点', '徽章文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spot_card', 'detail_button', '查看详情', '详情按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spot_card', 'nav_button', '导航', '导航按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'title', '景点详情', '模态框标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'basic_info', '基本信息', '基本信息标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'tips', '游玩提示', '游玩提示标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'address', '地址', '地址标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'hours', '开放时间', '开放时间标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'price', '票价', '票价标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'map_button', '查看地图', '地图按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'close_button', '关闭', '关闭按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入失物招领页面内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('lostfound', 'header', 'title', '失物招领', '页面标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'header', 'subtitle', '丢失或拾获物品？在这里登记或查询', '页面副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'header', 'submit_button', '登记物品', '登记按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'header', 'search_button', '搜索物品', '搜索按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'tabs', 'all', '全部', '全部标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'tabs', 'lost', '寻物启事', '寻物标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'tabs', 'found', '招领启事', '招领标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'list', 'loading', '正在加载物品信息...', '加载提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'list', 'empty', '暂无数据', '空数据提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'pagination', 'prev', '上一页', '上一页文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'pagination', 'next', '下一页', '下一页文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'card', 'lost_badge', '寻物启事', '寻物徽章文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'card', 'found_badge', '招领启事', '招领徽章文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'card', 'detail_button', '查看详情', '详情按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'title', '登记物品', '登记模态框标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'type_label', '物品类型', '类型标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'title_label', '标题', '标题标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'desc_label', '描述', '描述标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'category_label', '物品类别', '类别标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'location_label', '地点', '地点标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'contact_label', '联系人', '联系人标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'phone_label', '联系电话', '电话标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'image_label', '上传图片', '图片标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'submit_button', '提交', '提交按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'cancel_button', '取消', '取消按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'title', '搜索物品', '搜索模态框标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'keyword_label', '关键词', '关键词标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'type_label', '物品类型', '类型标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'category_label', '物品类别', '类别标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'search_button', '搜索', '搜索按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'cancel_button', '取消', '取消按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入预警信息页面内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('warning', 'header', 'title', '预警信息', '页面标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'header', 'subtitle', '及时了解景区最新预警信息，确保您的游览安全', '页面副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'list', 'loading', '正在加载预警信息...', '加载提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'list', 'empty', '当前没有预警信息', '空数据提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'list', 'error', '加载失败', '错误提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'crowd_type', '人流预警', '人流预警文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'weather_type', '天气预警', '天气预警文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'traffic_type', '交通预警', '交通预警文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'other_type', '其他预警', '其他预警文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'info_level', '提示', '提示级别文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'warning_level', '警告', '警告级别文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'danger_level', '危险', '危险级别文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'publish_time', '发布时间', '发布时间标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'valid_period', '有效期', '有效期标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'publisher', '发布人', '发布人标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'detail_button', '查看详情', '详情按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'title', '预警详情', '模态框标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'basic_info', '基本信息', '基本信息标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'publish_info', '发布信息', '发布信息标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'valid_period', '有效期', '有效期标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'publish_time', '发布时间', '发布时间标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'publisher', '发布人', '发布人标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'contact_phone', '联系电话', '联系电话标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'close_button', '关闭', '关闭按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());


INSERT INTO `fa_ds_content_config` (`id`, `name`, `group`, `title`, `tip`, `type`, `visible`, `value`, `content`, `rule`, `msg`, `extend`, `setting`)
VALUES
	('1', 'site_name', 'basic', '网站名称', '网站名称', 'string', '', '景区智能助理', NULL, '', NULL, '', ''),
	('2', 'site_description', 'basic', '网站描述', '网站描述', 'string', '', '基于国内大模型技术，为游客提供全方位智能服务', NULL, '', NULL, '', ''),
	('3', 'copyright', 'basic', '版权信息', '版权信息', 'string', '', '© 2024 景区智能助理 - 基于国内大模型技术', NULL, '', NULL, '', ''),
	('4', 'contact_address', 'contact', '联系地址', '联系地址', 'string', '', '景区管理中心', NULL, '', NULL, '', ''),
	('5', 'contact_phone', 'contact', '联系电话', '联系电话', 'string', '', '************', NULL, '', NULL, '', ''),
	('6', 'contact_email', 'contact', '联系邮箱', '联系邮箱', 'string', '', '<EMAIL>', NULL, '', NULL, '', ''),
	('7', 'embedding_provider', 'vector_search', '向量嵌入服务提供商', '选择用于生成向量嵌入的服务提供商', 'select', '', 'tencent', '{\"baidu\":\"百度文心大模型\",\"xunfei\":\"讯飞星火大模型\",\"ali\":\"阿里云通义千问\",\"tencent\":\"腾讯云混元大模型\",\"huawei\":\"华为云盘古大模型\"}', 'required', '请选择向量嵌入服务提供商', '', ''),
	('8', 'embedding_backup_provider', 'vector_search', '备用向量嵌入服务提供商', '当主要服务提供商不可用时，使用备用服务提供商', 'select', '', 'ali', '{\"baidu\":\"百度文心大模型\",\"xunfei\":\"讯飞星火大模型\",\"ali\":\"阿里云通义千问\",\"tencent\":\"腾讯云混元大模型\",\"huawei\":\"华为云盘古大模型\"}', 'required', '请选择备用向量嵌入服务提供商', '', ''),
	('9', 'baidu_api_key', 'vector_search', '百度API密钥', '百度文心大模型API密钥', 'string', '', '', '[]', '', '请填写百度API密钥', '', ''),
	('10', 'baidu_secret_key', 'vector_search', '百度密钥', '百度文心大模型密钥', 'string', '', '', '[]', '', '请填写百度密钥', '', ''),
	('11', 'xunfei_app_id', 'vector_search', '讯飞应用ID', '讯飞星火大模型应用ID', 'string', '', '', '[]', '', '请填写讯飞应用ID', '', ''),
	('12', 'xunfei_api_key', 'vector_search', '讯飞API密钥', '讯飞星火大模型API密钥', 'string', '', '', '[]', '', '请填写讯飞API密钥', '', ''),
	('13', 'xunfei_api_secret', 'vector_search', '讯飞API密钥', '讯飞星火大模型API密钥', 'string', '', '', '[]', '', '请填写讯飞API密钥', '', ''),
	('14', 'ali_api_key', 'vector_search', '阿里API密钥', '阿里云通义千问API密钥', 'string', '', '', '[]', '', '请填写阿里API密钥', '', ''),
	('15', 'tencent_secret_id', 'vector_search', '腾讯云SecretId', '腾讯云API密钥ID', 'string', '', 'AKIDeT7cw75pZ5OUN5as0xjIenlX6SbgYDgx', '[]', '', '请填写腾讯云SecretId', '', ''),
	('16', 'tencent_secret_key', 'vector_search', '腾讯云SecretKey', '腾讯云API密钥', 'string', '', '8mHZhcLtHSRHuO3cAe4TxRnwsc8RWR9f', '[]', '', '请填写腾讯云SecretKey', '', ''),
	('17', 'tencent_region', 'vector_search', '腾讯云区域', '腾讯云API区域', 'select', 'embedding_provider == \'tencent\' || embedding_backup_provider == \'tencent\'', 'ap-guangzhou', '{\"ap-guangzhou\":\"广州\",\"ap-shanghai\":\"上海\",\"ap-beijing\":\"北京\",\"ap-nanjing\":\"南京\",\"ap-hongkong\":\"香港\"}', '', '请选择腾讯云区域', '', ''),
	('18', 'huawei_ak', 'vector_search', '华为云AK', '华为云API密钥ID', 'string', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', '', '[]', '', '请填写华为云AK', '', ''),
	('19', 'huawei_sk', 'vector_search', '华为云SK', '华为云API密钥', 'string', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', '', '[]', '', '请填写华为云SK', '', ''),
	('20', 'huawei_project_id', 'vector_search', '华为云项目ID', '华为云项目ID', 'string', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', '', '[]', '', '请填写华为云项目ID', '', ''),
	('21', 'huawei_region', 'vector_search', '华为云区域', '华为云API区域', 'select', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', 'cn-north-4', '{\"cn-north-4\":\"华北-北京四\",\"cn-north-1\":\"华北-北京一\",\"cn-east-3\":\"华东-上海一\",\"cn-south-1\":\"华南-广州\",\"ap-southeast-1\":\"中国-香港\"}', '', '请选择华为云区域', '', ''),
	('22', 'huawei_embedding_model', 'vector_search', '华为云嵌入模型', '华为云盘古嵌入模型名称', 'string', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', 'pangu-embedding', '[]', '', '请填写华为云嵌入模型名称', '', ''),
	('23', 'deepseek_provider', 'deepseek', 'DeepSeek提供商', '选择DeepSeek API的提供商', 'select', '', 'official', '{\"official\":\"DeepSeek官方\",\"tencent\":\"腾讯云DeepSeek\",\"baidu\":\"百度智能云DeepSeek\",\"ali\":\"阿里云DeepSeek\",\"huawei\":\"华为云DeepSeek\"}', 'required', '请选择DeepSeek提供商', '', ''),
	('24', 'deepseek_api_key', 'deepseek', 'DeepSeek API密钥', '请在DeepSeek官网申请API密钥', 'string', 'deepseek_provider == \'official\'', '***********************************', '[]', 'required', '请填写DeepSeek API密钥', '', ''),
	('25', 'deepseek_model', 'deepseek', 'DeepSeek模型', '选择使用的DeepSeek模型', 'select', '', 'deepseek-chat', '{\"deepseek-chat\":\"deepseek-chat\",\"deepseek-chat-pro\":\"deepseek-chat-pro\"}', 'required', '请选择DeepSeek模型', '', ''),
	('26', 'system_prompt', 'deepseek', '系统提示词', '设置AI助手的角色定位和行为指南', 'text', '', '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。', '[]', 'required', '请填写系统提示词', '', ''),
	('27', 'cache_time', 'performance', '缓存时间(秒)', '设置问答结果缓存时间，单位：秒', 'number', '', '3600', '[]', 'required', '请填写缓存时间', '', ''),
	('28', 'confidence_threshold', 'performance', '置信度阈值', '设置AI回答的置信度阈值，低于此值将转人工客服', 'number', '', '70', '[]', 'required', '请填写置信度阈值', '', ''),
	('29', 'vectordb_type', 'vectordb', '向量数据库类型', '选择用于存储和检索向量的数据库类型', 'select', '', 'mysql', '{\"mysql\":\"MySQL (内置)\",\"tencent\":\"腾讯云 VectorDB\",\"aliyun\":\"阿里云 VectorSearch\",\"baidu\":\"百度智能云 VectorDB\",\"huawei\":\"华为云 VSS\"}', 'required', '请选择向量数据库类型', '', ''),
	('30', 'vector_search_type', 'vector_search', '向量搜索实现', '选择向量搜索的实现方式，高级版需要Jieba分词服务支持', 'select', '', 'optimized', '{\"enhanced\":\"进阶版 (向量数据库)\",\"jieba\":\"高级版 (Jieba分词+向量数据库)\"}', 'required', '请选择向量搜索实现', '', ''),
	('31', 'wechat_appid', 'wechat_mp', '微信公众号AppID', '微信公众号的AppID', 'string', '', '', '[]', '', '请填写微信公众号AppID', '', ''),
	('32', 'wechat_appsecret', 'wechat_mp', '微信公众号AppSecret', '微信公众号的AppSecret', 'string', '', '', '[]', '', '请填写微信公众号AppSecret', '', ''),
	('33', 'wechat_token', 'wechat_mp', '微信公众号Token', '微信公众号的Token', 'string', '', '', '[]', '', '请填写微信公众号Token', '', ''),
	('34', 'tencent_map_key', 'map', '腾讯地图Key', '腾讯地图开发者Key，用于网页和小程序地图显示', 'string', '', '', '[]', '', '请填写腾讯地图Key', '', ''),
	('35', 'customer_service_phone', 'basic', '客服电话', '当AI无法回答问题时显示的客服电话', 'string', '', '************', '[]', '', '请填写客服电话', '', ''),
	('37', 'rag_enabled', 'rag_model', 'RAG模式启用', '是否启用检索增强生成模式', 'switch', '', '1', '[]', '', '请选择是否启用RAG模式', '', ''),
	('38', 'rag_top_k', 'rag_model', '检索结果数量', '从本地知识库检索的结果数量', 'number', '', '3', '[]', 'required', '请填写检索结果数量', '', ''),
	('39', 'rag_min_score', 'rag_model', '最低得分要求', '本地知识库结果的最低得分要求，低于此分数的结果将被过滤', 'number', '', '0.3', '[]', 'required', '请填写最低得分要求', '', ''),
	('40', 'rag_context_format', 'rag_model', '上下文格式', '上下文格式，支持{question}和{answer}占位符', 'text', '', '问题: {question}\n回答: {answer}\n\n', '[]', '', '请填写上下文格式', '', ''),
	('41', 'rag_prompt_template', 'rag_model', '提示模板', '提供给大模型的提示模板，支持{context}占位符', 'text', '', '以下是关于用户问题的相关信息，请参考这些信息回答问题，但不要直接复制这些内容，而是用自然、流畅的语言回答：\n\n{context}', '[]', '', '请填写提示模板', '', ''),
	('42', 'enable_functions', 'function_calling', '启用函数调用', '启用后，AI助理可以调用函数，如显示地图位置、景点详情等', 'switch', '', '1', '', '', '', '', '{\"table\":\"\",\"conditions\":\"\",\"key\":\"\",\"value\":\"\"}');
