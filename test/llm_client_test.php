<?php

// 定义应用目录
define('APP_PATH', __DIR__ . '/../../../application/');

// 加载框架引导文件
require __DIR__ . '/../../../thinkphp/start.php';

// 使用LLMClient
use addons\dsassistant\library\llm\LLMClient;
use addons\dsassistant\library\llm\LLMConfig;
use think\Log;

// 测试LLMClient
function testLLMClient()
{
    echo "测试LLMClient...\n";
    
    try {
        // 获取LLM配置
        $config = LLMConfig::getConfig();
        echo "当前LLM配置:\n";
        echo "默认服务商: " . $config['default_provider'] . "\n";
        echo "默认模型: " . $config['default_model'] . "\n";
        
        // 创建LLM客户端
        $llmClient = new LLMClient();
        
        // 测试普通聊天
        echo "\n测试普通聊天...\n";
        $messages = [
            ['role' => 'system', 'content' => '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。'],
            ['role' => 'user', 'content' => '你好，请介绍一下故宫博物院']
        ];
        
        $result = $llmClient->chatCompletion($messages);
        echo "回答: " . $result . "\n";
        
        // 测试函数调用
        echo "\n测试函数调用...\n";
        $functionLoader = new \addons\dsassistant\library\FunctionLoader();
        $functions = $functionLoader->getFunctionsForDeepSeek();
        
        $messages = [
            ['role' => 'system', 'content' => '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。'],
            ['role' => 'user', 'content' => '故宫博物院在哪里？']
        ];
        
        $result = $llmClient->chatCompletionWithFunctions($messages, $functions);
        echo "回答: " . $result['content'] . "\n";
        
        if (!empty($result['function_calls'])) {
            echo "函数调用:\n";
            foreach ($result['function_calls'] as $call) {
                echo "函数名: " . $call['name'] . "\n";
                echo "参数: " . json_encode($call['parameters'], JSON_UNESCAPED_UNICODE) . "\n";
            }
        } else {
            echo "没有函数调用\n";
        }
        
        echo "\n测试完成\n";
    } catch (\Exception $e) {
        echo "测试失败: " . $e->getMessage() . "\n";
        Log::error('LLMClient测试失败: ' . $e->getMessage());
    }
}

// 运行测试
testLLMClient();
