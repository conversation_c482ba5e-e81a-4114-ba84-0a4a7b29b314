<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警信息 - 景区智能助理</title>
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/modern.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/warning.css">
</head>
<body>
    {include file="common/nav" /}

    <!-- 页面标题区域 -->
    <div class="page-header mb-4">
        <div class="ai-pattern"></div>
        <div class="container text-center py-3">
            <h1 class="display-4 mb-2 font-weight-bold">预警信息</h1>
            <p class="lead mb-0">及时了解景区最新预警信息，确保您的游览安全</p>
        </div>
    </div>

    <div class="container py-4">
        <!-- 预警信息列表 -->
        <div class="row" id="warningList">
            <!-- 预警信息将通过AJAX加载 -->
            <div class="col-12">
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在加载预警信息...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化页脚 -->
    <footer>
        <div class="ai-pattern"></div>
        <div class="container">
            <div class="row">
                <div class="col-md-6 mb-4 mb-md-0">
                    <h4 class="mb-4"><i class="fas fa-robot mr-2"></i> 景区智能助理</h4>
                    <p class="mb-3">基于国内大模型技术，为游客提供全方位智能服务。包括智能问答、景点导览、失物招领和预警信息等功能。</p>
                    <div class="d-flex mt-4">
                        <a href="#" class="text-white mr-3"><i class="fab fa-weixin fa-lg"></i></a>
                        <a href="#" class="text-white mr-3"><i class="fab fa-weibo fa-lg"></i></a>
                        <a href="#" class="text-white"><i class="fas fa-envelope fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-md-3 mb-4 mb-md-0">
                    <h5 class="mb-4">快速链接</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{:addon_url('dsassistant/index/index')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>首页</a></li>
                        <li class="mb-2"><a href="{:addon_url('dsassistant/index/chat')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>智能问答</a></li>
                        <li class="mb-2"><a href="{:addon_url('dsassistant/index/guide')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>景点导览</a></li>
                        <li class="mb-2"><a href="{:addon_url('dsassistant/index/lostfound')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>失物招领</a></li>
                        <li><a href="{:addon_url('dsassistant/index/warning')}" class="text-white"><i class="fas fa-chevron-right mr-2"></i>预警信息</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5 class="mb-4">联系我们</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-map-marker-alt mr-2"></i> 景区管理中心</li>
                        <li class="mb-2"><i class="fas fa-phone mr-2"></i> ************</li>
                        <li><i class="fas fa-envelope mr-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class="mt-4 mb-4" style="border-color: rgba(255,255,255,0.1);">
            <div class="text-center">
                <p class="mb-0">© 2024 景区智能助理 - 基于国内大模型技术</p>
            </div>
        </div>
    </footer>

    <script src="__CDN__/assets/addons/dsassistant/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script>
        $(function() {
            // 加载预警信息
            function loadWarnings() {
                $.ajax({
                    url: '{:addon_url("dsassistant/api/getWarnings")}',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            const warnings = response.data.list;

                            if (warnings.length === 0) {
                                $('#warningList').html('<div class="col-12 text-center py-5"><p>当前没有预警信息</p></div>');
                                return;
                            }

                            // 渲染预警信息列表
                            renderWarnings(warnings);
                        } else {
                            showError('加载失败：' + response.msg);
                        }
                    },
                    error: function() {
                        showError('网络错误，请稍后再试');
                    }
                });
            }

            // 渲染预警信息列表
            function renderWarnings(warnings) {
                let html = '';

                warnings.forEach(warning => {
                    const levelClass = getLevelClass(warning.level);
                    const typeIcon = getTypeIcon(warning.type);
                    const typeText = getTypeText(warning.type);
                    const levelText = getLevelText(warning.level);

                    // 根据级别设置卡片样式
                    let cardClass = '';
                    let badgeClass = '';

                    switch (warning.level) {
                        case 'info':
                            cardClass = 'warning-low';
                            badgeClass = 'badge-info';
                            break;
                        case 'warning':
                            cardClass = 'warning-medium';
                            badgeClass = 'badge-warning';
                            break;
                        case 'danger':
                            cardClass = 'warning-high';
                            badgeClass = 'badge-danger';
                            break;
                        default:
                            cardClass = 'warning-low';
                            badgeClass = 'badge-info';
                    }

                    html += `
                        <div class="col-md-6 mb-4">
                            <div class="warning-card ${cardClass}">
                                <div class="warning-header ${levelClass}">
                                    <div class="d-flex align-items-center">
                                        <div class="type-icon">${typeIcon}</div>
                                        <div>
                                            <div class="warning-title">${warning.title}</div>
                                            <div class="small">${typeText} · ${levelText}</div>
                                        </div>
                                    </div>
                                    <span class="warning-badge ${badgeClass}">${levelText}</span>
                                </div>
                                <div class="warning-body">
                                    <div class="warning-content">${warning.content}</div>
                                    <div class="warning-meta">
                                        <div><i class="far fa-clock"></i> 发布时间：${formatDate(warning.createtime)}</div>
                                        <div><i class="fas fa-calendar-alt"></i> 有效期：${formatDate(warning.start_time)} 至 ${formatDate(warning.end_time)}</div>
                                        <div><i class="fas fa-user"></i> 发布人：${warning.publisher || '景区管理员'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                $('#warningList').html(html);
            }

            // 获取级别对应的CSS类
            function getLevelClass(level) {
                switch (level) {
                    case 'info': return 'level-info';
                    case 'warning': return 'level-warning';
                    case 'danger': return 'level-danger';
                    default: return 'level-info';
                }
            }

            // 获取类型对应的图标
            function getTypeIcon(type) {
                switch (type) {
                    case 'crowd': return '<i class="fas fa-users"></i>';
                    case 'weather': return '<i class="fas fa-cloud-sun-rain"></i>';
                    case 'traffic': return '<i class="fas fa-car"></i>';
                    default: return '<i class="fas fa-exclamation-triangle"></i>';
                }
            }

            // 获取类型对应的文本
            function getTypeText(type) {
                switch (type) {
                    case 'crowd': return '人流预警';
                    case 'weather': return '天气预警';
                    case 'traffic': return '交通预警';
                    default: return '其他预警';
                }
            }

            // 获取级别对应的文本
            function getLevelText(level) {
                switch (level) {
                    case 'info': return '提示';
                    case 'warning': return '警告';
                    case 'danger': return '危险';
                    default: return '提示';
                }
            }

            // 格式化日期
            function formatDate(timestamp) {
                if (!timestamp) return '未知时间';

                const date = new Date(timestamp * 1000);
                return date.getFullYear() + '-' +
                       padZero(date.getMonth() + 1) + '-' +
                       padZero(date.getDate()) + ' ' +
                       padZero(date.getHours()) + ':' +
                       padZero(date.getMinutes());
            }

            // 补零
            function padZero(num) {
                return num < 10 ? '0' + num : num;
            }

            // 显示错误信息
            function showError(message) {
                const errorHtml = `<div class="col-12 text-center py-5"><p class="text-danger">${message}</p></div>`;
                $('#warningList').html(errorHtml);
            }

            // 显示预警详情
            window.showWarningDetail = function(id) {
                // 查找对应的预警信息
                $.ajax({
                    url: '{:addon_url("dsassistant/index/get_warning_detail")}',
                    type: 'GET',
                    data: { id: id },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1 && res.data) {
                            const warning = res.data;
                            const levelClass = getLevelClass(warning.level);
                            const typeIcon = getTypeIcon(warning.type);
                            const typeText = getTypeText(warning.type);
                            const levelText = getLevelText(warning.level);

                            // 创建模态框
                            if (!$('#warningDetailModal').length) {
                                const modalHtml = `
                                    <div class="modal fade" id="warningDetailModal" tabindex="-1" role="dialog" aria-hidden="true">
                                        <div class="modal-dialog modal-lg" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">预警详情</h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body" id="warningDetailBody">
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">关闭</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                $('body').append(modalHtml);
                            }

                            // 构建详情内容
                            let badgeClass = '';
                            switch (warning.level) {
                                case 'info': badgeClass = 'badge-info'; break;
                                case 'warning': badgeClass = 'badge-warning'; break;
                                case 'danger': badgeClass = 'badge-danger'; break;
                                default: badgeClass = 'badge-info';
                            }

                            const detailHtml = `
                                <div class="mb-4">
                                    <h3 class="mb-3 font-weight-bold">${warning.title}</h3>
                                    <div class="d-flex align-items-center mb-3">
                                        <span class="badge ${badgeClass} mr-2">${levelText}</span>
                                        <span class="text-muted">${typeText}</span>
                                    </div>
                                </div>

                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-body">
                                        <p style="line-height: 1.7;">${warning.content}</p>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-4 mb-md-0">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header bg-white border-0 pt-4 pb-0">
                                                <h5 class="mb-0"><i class="fas fa-info-circle mr-2" style="color: var(--primary-color);"></i>基本信息</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-calendar-alt" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">有效期</div>
                                                        <div>${formatDate(warning.start_time)} 至 ${formatDate(warning.end_time)}</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="far fa-clock" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">发布时间</div>
                                                        <div>${formatDate(warning.createtime)}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header bg-white border-0 pt-4 pb-0">
                                                <h5 class="mb-0"><i class="fas fa-user mr-2" style="color: var(--primary-color);"></i>发布信息</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-user" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">发布人</div>
                                                        <div>${warning.publisher || '景区管理员'}</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="icon-circle bg-light mr-3">
                                                        <i class="fas fa-phone" style="color: var(--primary-color);"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-muted small">联系电话</div>
                                                        <div>${warning.contact_phone || '************'}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;

                            // 更新模态框内容并显示
                            $('#warningDetailBody').html(detailHtml);
                            $('#warningDetailModal').modal('show');
                        } else {
                            alert('获取预警详情失败');
                        }
                    },
                    error: function() {
                        alert('获取预警详情失败，请稍后再试');
                    }
                });
            };

            // 初始加载预警信息
            loadWarnings();

            // 每5分钟刷新一次预警信息
            setInterval(loadWarnings, 5 * 60 * 1000);
        });
    </script>
</body>
</html>
