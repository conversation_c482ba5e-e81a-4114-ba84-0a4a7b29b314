<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问答 - 景区智能助理</title>
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/modern.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/chat.css">
    <link rel="stylesheet" href="__CDN__/assets/addons/dsassistant/css/function-card.css">
    <style>
        /* 打字光标效果 */
        #current-response::after {
            content: '|';
            display: inline-block;
            color: var(--primary-color);
            animation: cursor-blink 0.8s infinite;
            font-weight: bold;
        }

        @keyframes cursor-blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }
    </style>
</head>
<body>
    <!-- 现代化导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="{:addon_url('dsassistant/index/index')}">
                <i class="fas fa-robot mr-2" style="color: var(--primary-color);"></i>景区智能助理
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{:addon_url('dsassistant/index/index')}">
                            <i class="fas fa-home mr-1"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a class="nav-link" href="{:addon_url('dsassistant/index/chat')}">
                            <i class="fas fa-comments mr-1"></i> 智能问答
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:addon_url('dsassistant/index/guide')}">
                            <i class="fas fa-map-marked-alt mr-1"></i> 景点导览
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:addon_url('dsassistant/index/lostfound')}">
                            <i class="fas fa-search mr-1"></i> 失物招领
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:addon_url('dsassistant/index/warning')}">
                            <i class="fas fa-exclamation-triangle mr-1"></i> 预警信息
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 页面标题区域 -->
    <div class="page-header mb-4">
        <div class="ai-pattern"></div>
            <div class="container text-center py-3">
                <h1 class="display-4 mb-2 font-weight-bold">{ds:content page="chat" section="header" key="title" default="智能问答" /}</h1>
                <p class="lead mb-0">{ds:content page="chat" section="header" key="subtitle" default="有任何问题，随时向我提问" /}</p>
            </div>
        </div>
    </div>
    <div class="chat-container">
        <div class="chat-box" id="chatBox">
            <div class="message assistant">
                <div class="message-content markdown-content">
                    <p>{ds:content page="chat" section="welcome" key="greeting" default="您好！我是景区智能助理，很高兴为您服务。" /}</p>
                    <p>{ds:content page="chat" section="welcome" key="intro" default="您可以向我咨询以下内容：" /}</p>
                    <ul>
                        <li>{ds:content page="chat" section="welcome" key="topic1" default="景区开放时间和门票信息" /}</li>
                        <li>{ds:content page="chat" section="welcome" key="topic2" default="景点介绍和游览路线推荐" /}</li>
                        <li>{ds:content page="chat" section="welcome" key="topic3" default="交通指南和周边住宿" /}</li>
                        <li>{ds:content page="chat" section="welcome" key="topic4" default="景区活动和特色服务" /}</li>
                    </ul>
                    <p>{ds:content page="chat" section="welcome" key="closing" default="请问有什么可以帮助您的？" /}</p>
                </div>
            </div>
        </div>
        <div class="input-group chat-input">
            <input type="text" class="form-control" id="messageInput" placeholder="{ds:content page="chat" section="input" key="placeholder" default="请输入您的问题..." /}">
            <div class="input-group-append">
                <button class="btn btn-primary d-flex align-items-center justify-content-center" type="button" id="sendButton">
                    <i class="fas fa-paper-plane mr-1"></i> <span>{ds:content page="chat" section="input" key="button_text" default="发送" /}</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 函数调用卡片模板 -->
    <template id="function-card-template">
        <div class="function-card">
            <div class="card-icon">
                <i class="fas fa-code"></i>
            </div>
            <div class="card-content">
                <div class="card-title"></div>
                <div class="card-description"></div>
            </div>
            <div class="card-action">
                <span></span>
            </div>
        </div>
    </template>

    <script src="__CDN__/assets/addons/dsassistant/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script src="__CDN__/assets/addons/dsassistant/libs/marked/2.1.3/marked.min.js"></script>
    <script>
        $(function() {
            // 配置Marked.js
            marked.setOptions({
                renderer: new marked.Renderer(),
                pedantic: false,
                gfm: true,
                breaks: true,
                sanitize: false,
                smartypants: false,
                xhtml: false
            });

            // 获取或生成设备ID
            let deviceId = localStorage.getItem('deviceId');
            if (!deviceId) {
                // 生成一个随机的设备ID
                deviceId = 'device_' + Math.random().toString(36).substring(2, 15) + Date.now().toString(36);
                localStorage.setItem('deviceId', deviceId);
            }

            // 获取或生成用户ID
            let userId = localStorage.getItem('userId');
            if (!userId) {
                userId = 'user_' + Math.random().toString(36).substring(2, 15);
                localStorage.setItem('userId', userId);
            }

            // 生成会话ID
            const sessionId = 'web_' + Math.random().toString(36).substring(2, 15);

            // 发送消息
            function sendMessage() {
                const message = $('#messageInput').val().trim();
                if (message === '') return;

                // 添加用户消息到聊天框
                $('#chatBox').append(`
                    <div class="message user">
                        <div class="message-content">${message}</div>
                    </div>
                `);

                // 清空输入框
                $('#messageInput').val('');

                // 添加正在思考指示器
                const thinkingText = "{ds:content page='chat' section='status' key='thinking' default='助理正在思考' /}";
                const typingIndicator = `
                    <div class="message assistant typing-indicator">
                        <div class="message-content">
                            <span class="thinking-text">${thinkingText}</span>
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                `;
                $('#chatBox').append(typingIndicator);

                // 确保滚动到底部，使思考指示器可见
                scrollToBottom();

                // 禁用发送按钮和输入框，防止重复发送
                $('#sendButton').prop('disabled', true);
                $('#messageInput').prop('disabled', true);

                // 滚动到底部
                scrollToBottom();

                // 发送API请求
                $.ajax({
                    url: '{:addon_url("dsassistant/api/chat")}',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        question: message,
                        user_id: userId,
                        device_id: deviceId,
                        session_id: sessionId,
                        platform: 'web'
                    },
                    success: function(response) {
                        // 移除正在思考指示器
                        $('.typing-indicator').fadeOut(200, function() {
                            $(this).remove();
                        });

                        // 重新启用发送按钮和输入框
                        $('#sendButton').prop('disabled', false);
                        $('#messageInput').prop('disabled', false);
                        $('#messageInput').focus();

                        if (response.code === 1) {
                            // 添加助手回复到聊天框，添加小延迟使回复看起来更自然
                            setTimeout(function() {
                                // 创建一个空的消息容器
                                $('#chatBox').append(`
                                    <div class="message assistant">
                                        <div class="message-content">
                                            <div class="markdown-content" id="current-response"></div>
                                            <div class="function-cards" id="current-function-cards"></div>
                                        </div>
                                    </div>
                                `);

                                // 模拟SSE流式响应，逐字显示回答
                                const answer = response.data.answer;
                                simulateStreamResponse(answer);

                                // 处理函数调用
                                if (response.data.functionCalls && Array.isArray(response.data.functionCalls) && response.data.functionCalls.length > 0) {
                                    // 延迟显示函数调用卡片，等待文本完全显示
                                    setTimeout(function() {
                                        renderFunctionCards(response.data.functionCalls);
                                    }, answer.length * 30 + 500); // 根据文本长度估算完成时间
                                } else {
                                    // 如果没有函数调用，也需要移除ID，避免后续操作冲突
                                    const functionCardsContainer = document.getElementById('current-function-cards');
                                    if (functionCardsContainer) {
                                        functionCardsContainer.removeAttribute('id');
                                    }
                                }
                            }, 500); // 500毫秒延迟
                        } else {
                            // 显示错误消息，添加小延迟使回复看起来更自然
                            setTimeout(function() {
                                const errorMsg = "{ds:content page='chat' section='status' key='error' defualt='抱歉，出现了一些问题' /}";
                                $('#chatBox').append(`
                                    <div class="message assistant">
                                        <div class="message-content">
                                            <div class="markdown-content" id="current-response"></div>
                                        </div>
                                    </div>
                                `);
                                // 使用逐字显示效果显示错误消息
                                simulateStreamResponse(`${errorMsg}：${response.msg}`);
                                
                                // 确保移除function-cards的ID，避免后续操作冲突
                                const functionCardsContainer = document.getElementById('current-function-cards');
                                if (functionCardsContainer) {
                                    functionCardsContainer.removeAttribute('id');
                                }
                            }, 500); // 500毫秒延迟
                        }
                    },
                    error: function() {
                        // 移除正在思考指示器
                        $('.typing-indicator').fadeOut(200, function() {
                            $(this).remove();
                        });

                        // 重新启用发送按钮和输入框
                        $('#sendButton').prop('disabled', false);
                        $('#messageInput').prop('disabled', false);
                        $('#messageInput').focus();

                        // 显示错误消息，添加小延迟使回复看起来更自然
                        setTimeout(function() {
                            const networkErrorMsg = "{ds:content page='chat' section='status' key='network_error' default='网络连接出现问题，请稍后再试。' /}";
                            $('#chatBox').append(`
                                <div class="message assistant">
                                    <div class="message-content">
                                        <div class="markdown-content" id="current-response"></div>
                                    </div>
                                </div>
                            `);
                            // 使用逐字显示效果显示网络错误消息
                            simulateStreamResponse(networkErrorMsg);
                            
                            // 确保移除function-cards的ID，避免后续操作冲突
                            const functionCardsContainer = document.getElementById('current-function-cards');
                            if (functionCardsContainer) {
                                functionCardsContainer.removeAttribute('id');
                            }
                        }, 500); // 500毫秒延迟
                    }
                });
            }

            // 滚动到底部
            function scrollToBottom() {
                const chatBox = document.getElementById('chatBox');
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            // 模拟SSE流式响应，逐字显示回答
            function simulateStreamResponse(text) {
                const responseElement = document.getElementById('current-response');
                let rawText = '';
                let index = 0;
                let markdownBuffer = '';

                // 随机的打字速度，使显示更自然
                function getRandomTypingDelay() {
                    // 基础延迟20-40毫秒，有时会有短暂停顿
                    return Math.random() < 0.1 ? Math.random() * 100 + 50 : Math.random() * 20 + 20;
                }

                // 定时器函数，逐字添加文本
                function typeNextChar() {
                    if (index < text.length) {
                        // 添加下一个字符到原始文本
                        rawText += text.charAt(index);
                        index++;

                        // 每添加一个字符，就更新一次Markdown渲染
                        // 为了性能考虑，可以考虑降低渲染频率
                        if (index % 3 === 0 || index === text.length) {
                            markdownBuffer = marked.parse(rawText);
                            responseElement.innerHTML = markdownBuffer;
                        }

                        // 滚动到底部，确保用户看到最新内容
                        scrollToBottom();

                        // 设置下一个字符的延迟
                        setTimeout(typeNextChar, getRandomTypingDelay());
                    } else {
                        // 完成后，确保最终内容被正确渲染
                        responseElement.innerHTML = marked.parse(rawText);

                        // 移除临时ID，避免后续操作冲突
                        responseElement.removeAttribute('id');

                        // 最后一次滚动到底部
                        scrollToBottom();
                    }
                }

                // 开始逐字显示
                typeNextChar();
            }

            // 点击发送按钮
            $('#sendButton').click(sendMessage);

            // 按回车键发送
            $('#messageInput').keypress(function(e) {
                if (e.which === 13) {
                    sendMessage();
                    return false;
                }
            });

            // 渲染函数调用卡片
            function renderFunctionCards(functionCalls) {
                const functionCardsContainer = document.getElementById('current-function-cards');
                if (!functionCardsContainer) return;

                // 移除ID，避免后续操作冲突
                functionCardsContainer.removeAttribute('id');

                // 遍历函数调用
                functionCalls.forEach(function(call) {
                    // 克隆模板
                    const template = document.getElementById('function-card-template');
                    const card = document.importNode(template.content, true);

                    // 设置图标
                    const iconElement = card.querySelector('.card-icon i');
                    if (iconElement) {
                        // 根据函数名设置不同的图标
                        const iconClass = getFunctionIcon(call.name);
                        iconElement.className = iconClass;
                    }

                    // 设置标题
                    const titleElement = card.querySelector('.card-title');
                    if (titleElement) {
                        titleElement.textContent = getFunctionTitle(call.name, call.parameters);
                    }

                    // 设置描述
                    const descriptionElement = card.querySelector('.card-description');
                    if (descriptionElement) {
                        descriptionElement.textContent = getFunctionDescription(call.name);
                    }

                    // 设置操作按钮文本
                    const actionElement = card.querySelector('.card-action span');
                    if (actionElement) {
                        actionElement.textContent = getFunctionActionText(call.name);
                    }

                    // 添加点击事件
                    const cardElement = card.querySelector('.function-card');
                    if (cardElement) {
                        cardElement.addEventListener('click', function() {
                            handleFunctionCardClick(call.name, call.parameters);
                        });
                    }

                    // 添加到容器
                    functionCardsContainer.appendChild(card);
                });

                // 滚动到底部，确保卡片可见
                scrollToBottom();
            }

            // 获取函数图标
            function getFunctionIcon(functionName) {
                const icons = {
                    'showOnMap': 'fas fa-map-marker-alt',
                    'showScenicSpotDetails': 'fas fa-info-circle',
                    'searchNearby': 'fas fa-search-location',
                    'bookTicket': 'fas fa-ticket-alt',
                    'getWeather': 'fas fa-cloud-sun'
                };
                return icons[functionName] || 'fas fa-code';
            }

            // 获取函数标题
            function getFunctionTitle(functionName, parameters) {
                switch (functionName) {
                    case 'showOnMap':
                        return `${parameters.spotName || '位置'}的地图`;
                    case 'showScenicSpotDetails':
                        return `${parameters.spotName || '景点'}详情`;
                    case 'searchNearby':
                        return `搜索附近${parameters.keyword || '地点'}`;
                    case 'bookTicket':
                        return `预订${parameters.spotName || '景点'}门票`;
                    case 'getWeather':
                        return `${parameters.location || '当地'}天气`;
                    default:
                        return functionName;
                }
            }

            // 获取函数描述
            function getFunctionDescription(functionName) {
                const descriptions = {
                    'showOnMap': '查看景点在地图上的位置',
                    'showScenicSpotDetails': '查看景点的详细介绍',
                    'searchNearby': '搜索附近的服务设施',
                    'bookTicket': '在线预订景点门票',
                    'getWeather': '查看天气预报信息'
                };
                return descriptions[functionName] || '执行操作';
            }

            // 获取函数操作按钮文本
            function getFunctionActionText(functionName) {
                const actions = {
                    'showOnMap': '查看地图',
                    'showScenicSpotDetails': '查看详情',
                    'searchNearby': '搜索',
                    'bookTicket': '预订',
                    'getWeather': '查看'
                };
                return actions[functionName] || '执行';
            }

            // 处理函数卡片点击
            function handleFunctionCardClick(functionName, parameters) {
                console.log('Function called:', functionName, parameters);

                switch (functionName) {
                    case 'showOnMap':
                        // 跳转到地图页面
                        window.location.href = `{:addon_url('dsassistant/map/index')}?spotId=${parameters.spotId || ''}&spotName=${parameters.spotName || ''}&latitude=${parameters.latitude || ''}&longitude=${parameters.longitude || ''}`;
                        break;
                    case 'showScenicSpotDetails':
                        // 跳转到景点详情页面
                        window.location.href = `{:addon_url('dsassistant/scenic/detail')}?spotId=${parameters.spotId || ''}&spotName=${parameters.spotName || ''}`;
                        break;
                    case 'searchNearby':
                        // 跳转到搜索页面
                        window.location.href = `{:addon_url('dsassistant/search/index')}?keyword=${parameters.keyword || ''}&latitude=${parameters.latitude || ''}&longitude=${parameters.longitude || ''}`;
                        break;
                    case 'bookTicket':
                        // 跳转到订票页面
                        window.location.href = `{:addon_url('dsassistant/ticket/book')}?spotId=${parameters.spotId || ''}&spotName=${parameters.spotName || ''}`;
                        break;
                    case 'getWeather':
                        // 跳转到天气页面
                        window.location.href = `{:addon_url('dsassistant/weather/index')}?location=${parameters.location || ''}`;
                        break;
                    default:
                        console.log('未知函数:', functionName);
                }
            }

            // 初始滚动到底部
            scrollToBottom();
        });
    </script>
</body>
</html>
