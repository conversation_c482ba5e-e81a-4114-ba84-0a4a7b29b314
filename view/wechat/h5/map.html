<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>景点地图</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 0;
            margin: 0;
        }
        .header {
            background-color: #337ab7;
            color: #fff;
            padding: 10px 15px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }
        .header h4 {
            margin: 0;
        }
        .back-btn {
            color: #fff;
            text-decoration: none;
            margin-right: 10px;
        }
        #map-container {
            position: absolute;
            top: 50px;
            left: 0;
            right: 0;
            bottom: 0;
        }
        .info-panel {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            padding: 15px;
            border-top: 1px solid #ddd;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="javascript:history.back();" class="back-btn"><i class="glyphicon glyphicon-chevron-left"></i></a>
        <span>{$spotName|default='景点地图'}</span>
    </div>

    <div id="map-container"></div>

    {if !empty($spotName)}
    <div class="info-panel">
        <h4>{$spotName}</h4>
        <p>点击下方按钮导航至此景点</p>
        <button id="navigate-btn" class="btn btn-primary btn-block">导航到这里</button>
    </div>
    {/if}

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    <script src="https://map.qq.com/api/gljs?v=1.exp&key={$tencentMapKey|default=''}"></script>
    {if isset($jsConfig)}
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        wx.config({
            debug: false,
            appId: '{$jsConfig.appId}',
            timestamp: {$jsConfig.timestamp},
            nonceStr: '{$jsConfig.nonceStr}',
            signature: '{$jsConfig.signature}',
            jsApiList: [
                'updateAppMessageShareData',
                'updateTimelineShareData',
                'getLocation',
                'openLocation'
            ]
        });

        wx.ready(function(){
            // 配置分享信息
            wx.updateAppMessageShareData({
                title: '{$spotName|default="景点地图"}',
                desc: '查看景点位置和导航信息',
                link: window.location.href,
                imgUrl: '{$site.cdnurl}/assets/addons/dsassistant/images/functions/map.jpg',
                success: function () {}
            });

            {if !empty($latitude) && !empty($longitude)}
            // 导航按钮点击事件
            $('#navigate-btn').click(function() {
                wx.openLocation({
                    latitude: parseFloat('{$latitude}'),
                    longitude: parseFloat('{$longitude}'),
                    name: '{$spotName}',
                    address: '{$spotName}',
                    scale: 18
                });
            });
            {/if}
        });
    </script>
    {/if}

    <script>
        $(function() {
            // 初始化腾讯地图
            var center = {if !empty($latitude) && !empty($longitude)}
                new TMap.LatLng({$latitude}, {$longitude})
            {else}
                new TMap.LatLng(39.908802, 116.397502) // 默认北京中心
            {/if};

            var map = new TMap.Map("map-container", {
                center: center,
                zoom: 16,
                viewMode: '2D'
            });

            {if !empty($latitude) && !empty($longitude)}
            // 添加标记
            var marker = new TMap.MultiMarker({
                map: map,
                styles: {
                    "normal": new TMap.MarkerStyle({
                        width: 25,
                        height: 35,
                        anchor: { x: 12.5, y: 35 }
                    })
                },
                geometries: [{
                    id: 'spot',
                    position: new TMap.LatLng({$latitude}, {$longitude}),
                    properties: {
                        title: '{$spotName}'
                    }
                }]
            });

            // 添加标签
            var label = new TMap.MultiLabel({
                map: map,
                styles: {
                    label: new TMap.LabelStyle({
                        color: '#333',
                        size: 14,
                        offset: { x: 0, y: -40 },
                        padding: { top: 2, right: 5, bottom: 2, left: 5 },
                        backgroundColor: '#fff',
                        borderWidth: 1,
                        borderColor: '#ccc',
                        borderRadius: 3
                    })
                },
                geometries: [{
                    id: 'label-spot',
                    position: new TMap.LatLng({$latitude}, {$longitude}),
                    content: '{$spotName}',
                    styleId: 'label'
                }]
            });
            {else}
            // 使用微信接口获取用户位置（火星坐标系/GCJ-02）
            if (typeof wx !== 'undefined' && wx.getLocation) {
                wx.getLocation({
                    type: 'gcj02', // 使用腾讯地图需要gcj02坐标系
                    success: function(res) {
                        var userLatLng = new TMap.LatLng(res.latitude, res.longitude);
                        map.setCenter(userLatLng);

                        // 添加用户位置标记
                        var marker = new TMap.MultiMarker({
                            map: map,
                            styles: {
                                "normal": new TMap.MarkerStyle({
                                    width: 25,
                                    height: 35,
                                    anchor: { x: 12.5, y: 35 }
                                })
                            },
                            geometries: [{
                                id: 'user',
                                position: userLatLng,
                                properties: {
                                    title: '您的位置'
                                }
                            }]
                        });

                        // 添加标签
                        var label = new TMap.MultiLabel({
                            map: map,
                            styles: {
                                label: new TMap.LabelStyle({
                                    color: '#333',
                                    size: 14,
                                    offset: { x: 0, y: -40 },
                                    padding: { top: 2, right: 5, bottom: 2, left: 5 },
                                    backgroundColor: '#fff',
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 3
                                })
                            },
                            geometries: [{
                                id: 'label-user',
                                position: userLatLng,
                                content: '您的位置',
                                styleId: 'label'
                            }]
                        });

                        console.log('微信定位成功:', res.latitude, res.longitude);
                    },
                    fail: function(error) {
                        console.error('微信定位失败:', error);

                        // 微信接口失败后，尝试使用浏览器定位
                        if (navigator.geolocation) {
                            navigator.geolocation.getCurrentPosition(function(position) {
                                var userLatLng = new TMap.LatLng(position.coords.latitude, position.coords.longitude);
                                map.setCenter(userLatLng);

                                // 添加用户位置标记
                                var marker = new TMap.MultiMarker({
                                    map: map,
                                    styles: {
                                        "normal": new TMap.MarkerStyle({
                                            width: 25,
                                            height: 35,
                                            anchor: { x: 12.5, y: 35 }
                                        })
                                    },
                                    geometries: [{
                                        id: 'user',
                                        position: userLatLng,
                                        properties: {
                                            title: '您的位置'
                                        }
                                    }]
                                });

                                console.log('浏览器定位成功:', position.coords.latitude, position.coords.longitude);
                            });
                        }
                    }
                });
            } else if (navigator.geolocation) {
                // 如果微信接口不可用，使用浏览器定位
                navigator.geolocation.getCurrentPosition(function(position) {
                    var userLatLng = new TMap.LatLng(position.coords.latitude, position.coords.longitude);
                    map.setCenter(userLatLng);

                    // 添加用户位置标记
                    var marker = new TMap.MultiMarker({
                        map: map,
                        styles: {
                            "normal": new TMap.MarkerStyle({
                                width: 25,
                                height: 35,
                                anchor: { x: 12.5, y: 35 }
                            })
                        },
                        geometries: [{
                            id: 'user',
                            position: userLatLng,
                            properties: {
                                title: '您的位置'
                            }
                        }]
                    });

                    console.log('浏览器定位成功:', position.coords.latitude, position.coords.longitude);
                });
            }
            {/if}
        });
    </script>
</body>
</html>
