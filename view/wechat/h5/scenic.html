<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{$spotName|default='景点详情'}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #337ab7;
            color: #fff;
            padding: 10px 15px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }
        .header h4 {
            margin: 0;
        }
        .back-btn {
            color: #fff;
            text-decoration: none;
            margin-right: 10px;
        }
        .content {
            padding: 60px 15px 15px;
        }
        .spot-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .spot-info {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .spot-info h4 {
            margin-top: 0;
            color: #337ab7;
        }
        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        .action-buttons .btn {
            flex: 1;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="javascript:history.back();" class="back-btn"><i class="glyphicon glyphicon-chevron-left"></i></a>
        <span>{$spotName|default='景点详情'}</span>
    </div>
    
    <div class="content">
        {if isset($spotInfo)}
        <img src="{$spotInfo.image|default='{$site.cdnurl}/assets/addons/dsassistant/images/functions/scenic.jpg'}" class="spot-image" alt="{$spotName}">
        
        <div class="spot-info">
            <h4>{$spotInfo.name|default=$spotName}</h4>
            <p><strong>开放时间：</strong>{$spotInfo.opening_hours|default='08:00-18:00'}</p>
            <p><strong>门票价格：</strong>{$spotInfo.ticket_price|default='¥50/人'}</p>
            <p><strong>推荐游玩时间：</strong>{$spotInfo.recommended_time|default='2小时'}</p>
            
            <div class="action-buttons">
                <a href="map?spotId={$spotId}&spotName={$spotName}&latitude={$spotInfo.latitude|default=''}&longitude={$spotInfo.longitude|default=''}" class="btn btn-primary">查看地图</a>
                <a href="ticket?spotId={$spotId}&spotName={$spotName}" class="btn btn-success">预订门票</a>
            </div>
        </div>
        
        <div class="spot-info">
            <h4>景点介绍</h4>
            <p>{$spotInfo.description|default='暂无介绍'}</p>
        </div>
        
        <div class="spot-info">
            <h4>游客评价</h4>
            {if isset($spotInfo.reviews) && !empty($spotInfo.reviews)}
                {foreach $spotInfo.reviews as $review}
                <div class="review">
                    <p><strong>{$review.username}</strong> <span class="text-muted">{$review.time}</span></p>
                    <p>{$review.content}</p>
                </div>
                {/foreach}
            {else}
                <p>暂无评价</p>
            {/if}
        </div>
        {else}
        <div class="spot-info">
            <h4>{$spotName|default='景点详情'}</h4>
            <p>暂无该景点的详细信息</p>
            
            <div class="action-buttons">
                <a href="javascript:history.back();" class="btn btn-default">返回</a>
            </div>
        </div>
        {/if}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    {if isset($jsConfig)}
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        wx.config({
            debug: false,
            appId: '{$jsConfig.appId}',
            timestamp: {$jsConfig.timestamp},
            nonceStr: '{$jsConfig.nonceStr}',
            signature: '{$jsConfig.signature}',
            jsApiList: [
                'updateAppMessageShareData',
                'updateTimelineShareData'
            ]
        });
        
        wx.ready(function(){
            // 配置分享信息
            wx.updateAppMessageShareData({
                title: '{$spotName|default="景点详情"}',
                desc: '{$spotInfo.description|default="查看景点详细信息"}',
                link: window.location.href,
                imgUrl: '{$site.cdnurl}/assets/addons/dsassistant/images/functions/scenic.jpg',
                success: function () {}
            });
            
            wx.updateTimelineShareData({
                title: '{$spotName|default="景点详情"}',
                link: window.location.href,
                imgUrl: '{$site.cdnurl}/assets/addons/dsassistant/images/functions/scenic.jpg',
                success: function () {}
            });
        });
    </script>
    {/if}
</body>
</html>
