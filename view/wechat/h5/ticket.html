<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>门票预订</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #337ab7;
            color: #fff;
            padding: 10px 15px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }
        .header h4 {
            margin: 0;
        }
        .back-btn {
            color: #fff;
            text-decoration: none;
            margin-right: 10px;
        }
        .content {
            padding: 60px 15px 15px;
        }
        .spot-info {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .spot-info h4 {
            margin-top: 0;
            color: #337ab7;
        }
        .ticket-item {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: relative;
        }
        .ticket-item h4 {
            margin-top: 0;
            color: #337ab7;
        }
        .ticket-item .price {
            color: #f0ad4e;
            font-size: 18px;
            font-weight: bold;
        }
        .ticket-item .original-price {
            color: #999;
            text-decoration: line-through;
            margin-left: 5px;
        }
        .ticket-item .discount {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #d9534f;
            color: #fff;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        .ticket-item .action-buttons {
            margin-top: 10px;
        }
        .booking-form {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .booking-form h4 {
            margin-top: 0;
            color: #337ab7;
        }
        .booking-form .form-group {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="javascript:history.back();" class="back-btn"><i class="glyphicon glyphicon-chevron-left"></i></a>
        <span>门票预订 - {$spotName|default='景区'}</span>
    </div>
    
    <div class="content">
        <div class="spot-info">
            <h4>{$spotName|default='景区'}</h4>
            <p><strong>开放时间：</strong>08:00-18:00</p>
            <p><strong>地址：</strong>景区详细地址</p>
        </div>
        
        {if isset($tickets) && !empty($tickets)}
            {foreach $tickets as $ticket}
            <div class="ticket-item">
                {if isset($ticket.discount) && $ticket.discount > 0}
                <span class="discount">{$ticket.discount}折</span>
                {/if}
                <h4>{$ticket.name}</h4>
                <p>{$ticket.description|default=''}</p>
                <p class="price">¥{$ticket.price}
                    {if isset($ticket.original_price) && $ticket.original_price > $ticket.price}
                    <span class="original-price">¥{$ticket.original_price}</span>
                    {/if}
                </p>
                <div class="action-buttons">
                    <button class="btn btn-success btn-block book-btn" data-id="{$ticket.id}" data-name="{$ticket.name}" data-price="{$ticket.price}">立即预订</button>
                </div>
            </div>
            {/foreach}
        {else}
            <div class="ticket-item">
                <h4>成人票</h4>
                <p>适用于18-60周岁游客</p>
                <p class="price">¥50 <span class="original-price">¥80</span></p>
                <span class="discount">6.3折</span>
                <div class="action-buttons">
                    <button class="btn btn-success btn-block book-btn" data-id="1" data-name="成人票" data-price="50">立即预订</button>
                </div>
            </div>
            
            <div class="ticket-item">
                <h4>儿童/老人票</h4>
                <p>适用于6-18周岁未成年人和60周岁以上老人</p>
                <p class="price">¥25 <span class="original-price">¥80</span></p>
                <span class="discount">3.1折</span>
                <div class="action-buttons">
                    <button class="btn btn-success btn-block book-btn" data-id="2" data-name="儿童/老人票" data-price="25">立即预订</button>
                </div>
            </div>
            
            <div class="ticket-item">
                <h4>家庭套票</h4>
                <p>2大1小，享受更多优惠</p>
                <p class="price">¥100 <span class="original-price">¥210</span></p>
                <span class="discount">4.8折</span>
                <div class="action-buttons">
                    <button class="btn btn-success btn-block book-btn" data-id="3" data-name="家庭套票" data-price="100">立即预订</button>
                </div>
            </div>
        {/if}
        
        <div class="booking-form" style="display: none;">
            <h4>预订信息</h4>
            <form id="booking-form">
                <input type="hidden" name="ticket_id" id="ticket-id">
                <input type="hidden" name="ticket_name" id="ticket-name">
                <input type="hidden" name="ticket_price" id="ticket-price">
                
                <div class="form-group">
                    <label for="visit-date">游玩日期</label>
                    <input type="date" class="form-control" id="visit-date" name="visit_date" required>
                </div>
                
                <div class="form-group">
                    <label for="quantity">数量</label>
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default" id="decrease-btn">-</button>
                        </span>
                        <input type="number" class="form-control text-center" id="quantity" name="quantity" value="1" min="1" max="10" readonly>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default" id="increase-btn">+</button>
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="contact-name">联系人</label>
                    <input type="text" class="form-control" id="contact-name" name="contact_name" required>
                </div>
                
                <div class="form-group">
                    <label for="contact-phone">手机号码</label>
                    <input type="tel" class="form-control" id="contact-phone" name="contact_phone" required>
                </div>
                
                <div class="form-group">
                    <label>总价</label>
                    <h3 class="text-danger" id="total-price">¥0</h3>
                </div>
                
                <button type="submit" class="btn btn-primary btn-block">确认预订</button>
                <button type="button" class="btn btn-default btn-block" id="cancel-btn">取消</button>
            </form>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    {if isset($jsConfig)}
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        wx.config({
            debug: false,
            appId: '{$jsConfig.appId}',
            timestamp: {$jsConfig.timestamp},
            nonceStr: '{$jsConfig.nonceStr}',
            signature: '{$jsConfig.signature}',
            jsApiList: [
                'updateAppMessageShareData',
                'updateTimelineShareData'
            ]
        });
        
        wx.ready(function(){
            // 配置分享信息
            wx.updateAppMessageShareData({
                title: '门票预订 - {$spotName|default="景区"}',
                desc: '在线预订景区门票，享受优惠价格',
                link: window.location.href,
                imgUrl: '{$site.cdnurl}/assets/addons/dsassistant/images/functions/ticket.jpg',
                success: function () {}
            });
        });
    </script>
    {/if}
    
    <script>
        $(function() {
            // 设置默认日期为明天
            var tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            $('#visit-date').val(tomorrow.toISOString().split('T')[0]);
            
            // 预订按钮点击事件
            $('.book-btn').click(function() {
                var ticketId = $(this).data('id');
                var ticketName = $(this).data('name');
                var ticketPrice = $(this).data('price');
                
                $('#ticket-id').val(ticketId);
                $('#ticket-name').val(ticketName);
                $('#ticket-price').val(ticketPrice);
                
                // 计算总价
                updateTotalPrice();
                
                // 显示预订表单
                $('.booking-form').show();
                $('html, body').animate({
                    scrollTop: $('.booking-form').offset().top - 70
                }, 500);
            });
            
            // 取消按钮点击事件
            $('#cancel-btn').click(function() {
                $('.booking-form').hide();
            });
            
            // 增加数量按钮
            $('#increase-btn').click(function() {
                var quantity = parseInt($('#quantity').val());
                if (quantity < 10) {
                    $('#quantity').val(quantity + 1);
                    updateTotalPrice();
                }
            });
            
            // 减少数量按钮
            $('#decrease-btn').click(function() {
                var quantity = parseInt($('#quantity').val());
                if (quantity > 1) {
                    $('#quantity').val(quantity - 1);
                    updateTotalPrice();
                }
            });
            
            // 更新总价
            function updateTotalPrice() {
                var price = parseFloat($('#ticket-price').val());
                var quantity = parseInt($('#quantity').val());
                var total = price * quantity;
                $('#total-price').text('¥' + total.toFixed(2));
            }
            
            // 表单提交
            $('#booking-form').submit(function(e) {
                e.preventDefault();
                
                // 这里可以添加表单验证和提交逻辑
                alert('预订成功！我们将尽快处理您的订单。');
                
                // 隐藏表单
                $('.booking-form').hide();
            });
        });
    </script>
</body>
</html>
