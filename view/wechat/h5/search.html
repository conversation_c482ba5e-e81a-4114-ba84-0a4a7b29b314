<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>周边搜索</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #337ab7;
            color: #fff;
            padding: 10px 15px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }
        .header h4 {
            margin: 0;
        }
        .back-btn {
            color: #fff;
            text-decoration: none;
            margin-right: 10px;
        }
        .content {
            padding: 60px 15px 15px;
        }
        .search-box {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .search-box .input-group {
            margin-bottom: 10px;
        }
        .search-box .btn-group {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        .search-box .btn-group .btn {
            margin: 5px 0;
            flex-basis: 48%;
        }
        .result-item {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .result-item h4 {
            margin-top: 0;
            color: #337ab7;
        }
        .result-item .distance {
            color: #5cb85c;
            font-weight: bold;
        }
        .result-item .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        .result-item .action-buttons .btn {
            flex: 1;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="javascript:history.back();" class="back-btn"><i class="glyphicon glyphicon-chevron-left"></i></a>
        <span>周边搜索</span>
    </div>

    <div class="content">
        <div class="search-box">
            <form action="" method="get">
                <div class="input-group">
                    <input type="text" name="keyword" class="form-control" placeholder="搜索景点、餐厅、酒店等" value="{$keyword}">
                    <span class="input-group-btn">
                        <button class="btn btn-primary" type="submit">搜索</button>
                    </span>
                </div>
                <input type="hidden" name="latitude" id="latitude" value="{$latitude}">
                <input type="hidden" name="longitude" id="longitude" value="{$longitude}">
            </form>

            <div class="btn-group">
                <button class="btn btn-default quick-search" data-keyword="景点">景点</button>
                <button class="btn btn-default quick-search" data-keyword="餐厅">餐厅</button>
                <button class="btn btn-default quick-search" data-keyword="酒店">酒店</button>
                <button class="btn btn-default quick-search" data-keyword="停车场">停车场</button>
                <button class="btn btn-default quick-search" data-keyword="厕所">厕所</button>
                <button class="btn btn-default quick-search" data-keyword="商店">商店</button>
            </div>
        </div>

        {if isset($results) && !empty($results)}
            {foreach $results as $item}
            <div class="result-item">
                <h4>{$item.name}</h4>
                <p>{$item.description|default=''}</p>
                {if !empty($latitude) && !empty($longitude) && !empty($item.latitude) && !empty($item.longitude)}
                <p class="distance">{:round(getDistance($latitude, $longitude, $item.latitude, $item.longitude), 1)}公里</p>
                {/if}

                <div class="action-buttons">
                    <a href="scenic?spotId={$item.id}&spotName={$item.name}" class="btn btn-info">查看详情</a>
                    {if !empty($item.latitude) && !empty($item.longitude)}
                    <a href="map?spotId={$item.id}&spotName={$item.name}&latitude={$item.latitude}&longitude={$item.longitude}" class="btn btn-primary">查看地图</a>
                    {/if}
                </div>
            </div>
            {/foreach}
        {elseif !empty($keyword)}
            <div class="result-item">
                <p>未找到与"{$keyword}"相关的结果</p>
            </div>
        {/if}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    <script src="https://map.qq.com/api/gljs?v=1.exp&key={$tencentMapKey|default=''}"></script>
    {if isset($jsConfig)}
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        wx.config({
            debug: false,
            appId: '{$jsConfig.appId}',
            timestamp: {$jsConfig.timestamp},
            nonceStr: '{$jsConfig.nonceStr}',
            signature: '{$jsConfig.signature}',
            jsApiList: [
                'updateAppMessageShareData',
                'updateTimelineShareData',
                'getLocation'
            ]
        });

        wx.ready(function(){
            // 配置分享信息
            wx.updateAppMessageShareData({
                title: '周边搜索',
                desc: '搜索景区周边的景点、餐厅、酒店等设施',
                link: window.location.href,
                imgUrl: '{$site.cdnurl}/assets/addons/dsassistant/images/functions/search.jpg',
                success: function () {}
            });

            // 如果没有位置信息，尝试获取
            if (!$('#latitude').val() || !$('#longitude').val()) {
                wx.getLocation({
                    type: 'gcj02',
                    success: function (res) {
                        $('#latitude').val(res.latitude);
                        $('#longitude').val(res.longitude);
                    }
                });
            }
        });
    </script>
    {/if}

    <script>
        $(function() {
            // 快速搜索按钮点击事件
            $('.quick-search').click(function() {
                var keyword = $(this).data('keyword');
                $('input[name="keyword"]').val(keyword);
                $('form').submit();
            });

            // 如果没有位置信息，尝试获取用户位置
            if (!$('#latitude').val() || !$('#longitude').val()) {
                // 优先使用微信接口获取用户位置（火星坐标系/GCJ-02）
                if (typeof wx !== 'undefined' && wx.getLocation) {
                    wx.getLocation({
                        type: 'gcj02', // 使用腾讯地图需要gcj02坐标系
                        success: function(res) {
                            $('#latitude').val(res.latitude);
                            $('#longitude').val(res.longitude);
                            console.log('微信定位成功:', res.latitude, res.longitude);
                        },
                        fail: function(error) {
                            console.error('微信定位失败:', error);
                            // 微信接口失败后，尝试使用浏览器定位
                            if (navigator.geolocation) {
                                navigator.geolocation.getCurrentPosition(function(position) {
                                    $('#latitude').val(position.coords.latitude);
                                    $('#longitude').val(position.coords.longitude);
                                    console.log('浏览器定位成功:', position.coords.latitude, position.coords.longitude);
                                });
                            }
                        }
                    });
                } else if (navigator.geolocation) {
                    // 如果微信接口不可用，使用浏览器定位
                    navigator.geolocation.getCurrentPosition(function(position) {
                        $('#latitude').val(position.coords.latitude);
                        $('#longitude').val(position.coords.longitude);
                        console.log('浏览器定位成功:', position.coords.latitude, position.coords.longitude);
                    });
                }
            }
        });
    </script>
</body>
</html>
