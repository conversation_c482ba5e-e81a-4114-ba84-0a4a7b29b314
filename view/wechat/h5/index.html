<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>景区智能助手</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 15px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .function-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .function-card h4 {
            margin-top: 0;
            color: #337ab7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h3>景区智能助手</h3>
            <p>为您提供景区信息、导览服务、预警通知等</p>
        </div>
        
        <div class="function-card">
            <h4>景点导览</h4>
            <p>查看景区地图、景点介绍、游览路线等信息</p>
            <a href="map" class="btn btn-primary btn-sm">查看地图</a>
        </div>
        
        <div class="function-card">
            <h4>门票预订</h4>
            <p>在线预订景区门票，享受优惠价格</p>
            <a href="ticket" class="btn btn-success btn-sm">预订门票</a>
        </div>
        
        <div class="function-card">
            <h4>周边搜索</h4>
            <p>查找景区周边的餐厅、酒店、停车场等设施</p>
            <a href="search" class="btn btn-info btn-sm">搜索周边</a>
        </div>
        
        <div class="function-card">
            <h4>天气查询</h4>
            <p>查看景区实时天气和未来天气预报</p>
            <a href="weather" class="btn btn-warning btn-sm">查看天气</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    {if isset($jsConfig)}
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        wx.config({
            debug: false,
            appId: '{$jsConfig.appId}',
            timestamp: {$jsConfig.timestamp},
            nonceStr: '{$jsConfig.nonceStr}',
            signature: '{$jsConfig.signature}',
            jsApiList: [
                'updateAppMessageShareData',
                'updateTimelineShareData',
                'getLocation',
                'openLocation'
            ]
        });
        
        wx.ready(function(){
            // 配置分享信息
            wx.updateAppMessageShareData({
                title: '景区智能助手',
                desc: '为您提供景区信息、导览服务、预警通知等',
                link: window.location.href,
                imgUrl: '{$site.cdnurl}/assets/addons/dsassistant/images/logo.png',
                success: function () {}
            });
            
            wx.updateTimelineShareData({
                title: '景区智能助手',
                link: window.location.href,
                imgUrl: '{$site.cdnurl}/assets/addons/dsassistant/images/logo.png',
                success: function () {}
            });
        });
    </script>
    {/if}
</body>
</html>
