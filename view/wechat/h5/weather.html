<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>天气查询</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #337ab7;
            color: #fff;
            padding: 10px 15px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }
        .header h4 {
            margin: 0;
        }
        .back-btn {
            color: #fff;
            text-decoration: none;
            margin-right: 10px;
        }
        .content {
            padding: 60px 15px 15px;
        }
        .search-box {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .weather-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .weather-card h4 {
            margin-top: 0;
            color: #337ab7;
        }
        .weather-now {
            text-align: center;
            padding: 20px 0;
        }
        .weather-now .temperature {
            font-size: 48px;
            font-weight: bold;
            color: #f0ad4e;
        }
        .weather-now .weather-icon {
            font-size: 64px;
            margin: 10px 0;
        }
        .weather-now .weather-text {
            font-size: 24px;
            color: #333;
        }
        .weather-info {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .weather-info .info-item {
            text-align: center;
        }
        .weather-info .info-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #5bc0de;
        }
        .forecast-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .forecast-item:last-child {
            border-bottom: none;
        }
        .forecast-item .date {
            flex: 1;
        }
        .forecast-item .weather {
            flex: 1;
            text-align: center;
        }
        .forecast-item .temperature {
            flex: 1;
            text-align: right;
            color: #f0ad4e;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="javascript:history.back();" class="back-btn"><i class="glyphicon glyphicon-chevron-left"></i></a>
        <span>天气查询</span>
    </div>
    
    <div class="content">
        <div class="search-box">
            <form action="" method="get">
                <div class="input-group">
                    <input type="text" name="location" class="form-control" placeholder="输入城市或景区名称" value="{$location}">
                    <span class="input-group-btn">
                        <button class="btn btn-primary" type="submit">查询</button>
                    </span>
                </div>
            </form>
        </div>
        
        {if isset($weatherData)}
        <div class="weather-card">
            <h4>{$weatherData.location} 实时天气</h4>
            <div class="weather-now">
                <div class="temperature">{$weatherData.temperature}</div>
                <div class="weather-icon">
                    {switch name="weatherData.weather"}
                        {case value="晴"}☀️{/case}
                        {case value="多云"}⛅{/case}
                        {case value="阴"}☁️{/case}
                        {case value="小雨"}🌧️{/case}
                        {case value="中雨"}🌧️{/case}
                        {case value="大雨"}🌧️{/case}
                        {case value="暴雨"}⛈️{/case}
                        {case value="雷阵雨"}⛈️{/case}
                        {case value="小雪"}🌨️{/case}
                        {case value="中雪"}🌨️{/case}
                        {case value="大雪"}🌨️{/case}
                        {case value="暴雪"}❄️{/case}
                        {case value="雾"}🌫️{/case}
                        {case value="霾"}🌫️{/case}
                        {default}🌤️{/default}
                    {/switch}
                </div>
                <div class="weather-text">{$weatherData.weather}</div>
            </div>
            <div class="weather-info">
                <div class="info-item">
                    <div class="label">湿度</div>
                    <div class="value">{$weatherData.humidity}</div>
                </div>
                <div class="info-item">
                    <div class="label">风向</div>
                    <div class="value">{$weatherData.wind}</div>
                </div>
                <div class="info-item">
                    <div class="label">更新时间</div>
                    <div class="value">{:date('H:i')}</div>
                </div>
            </div>
        </div>
        
        <div class="weather-card">
            <h4>未来天气预报</h4>
            {foreach $weatherData.forecast as $forecast}
            <div class="forecast-item">
                <div class="date">{$forecast.date}</div>
                <div class="weather">
                    {switch name="forecast.weather"}
                        {case value="晴"}☀️{/case}
                        {case value="多云"}⛅{/case}
                        {case value="阴"}☁️{/case}
                        {case value="小雨"}🌧️{/case}
                        {case value="中雨"}🌧️{/case}
                        {case value="大雨"}🌧️{/case}
                        {case value="暴雨"}⛈️{/case}
                        {case value="雷阵雨"}⛈️{/case}
                        {case value="小雪"}🌨️{/case}
                        {case value="中雪"}🌨️{/case}
                        {case value="大雪"}🌨️{/case}
                        {case value="暴雪"}❄️{/case}
                        {case value="雾"}🌫️{/case}
                        {case value="霾"}🌫️{/case}
                        {default}🌤️{/default}
                    {/switch}
                    {$forecast.weather}
                </div>
                <div class="temperature">{$forecast.temperature}</div>
            </div>
            {/foreach}
        </div>
        
        <div class="weather-card">
            <h4>景区游玩建议</h4>
            <p>
                {switch name="weatherData.weather"}
                    {case value="晴"}今天天气晴朗，非常适合户外活动。建议做好防晒措施，多补充水分。{/case}
                    {case value="多云"}今天天气多云，适合户外活动。建议随身携带雨具，以防天气变化。{/case}
                    {case value="阴"}今天天气阴沉，温度适宜。建议随身携带雨具，以防下雨。{/case}
                    {case value="小雨" break="0"}{/case}
                    {case value="中雨" break="0"}{/case}
                    {case value="大雨"}今天有雨，建议带好雨具，穿防滑鞋。部分户外景点可能关闭，请提前确认。{/case}
                    {case value="暴雨" break="0"}{/case}
                    {case value="雷阵雨"}今天天气恶劣，不建议户外活动。请关注景区公告，部分景点可能关闭。{/case}
                    {case value="小雪" break="0"}{/case}
                    {case value="中雪" break="0"}{/case}
                    {case value="大雪" break="0"}{/case}
                    {case value="暴雪"}今天有雪，道路可能湿滑，请注意安全。雪景很美，适合拍照，但请注意保暖。{/case}
                    {case value="雾" break="0"}{/case}
                    {case value="霾"}今天能见度较低，部分高空景点可能关闭。建议佩戴口罩，减少户外活动时间。{/case}
                    {default}天气适宜，建议随身携带雨具，以防天气变化。{/default}
                {/switch}
            </p>
        </div>
        {elseif !empty($location)}
        <div class="weather-card">
            <p>未能获取"{$location}"的天气信息，请检查输入是否正确。</p>
        </div>
        {/if}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    {if isset($jsConfig)}
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        wx.config({
            debug: false,
            appId: '{$jsConfig.appId}',
            timestamp: {$jsConfig.timestamp},
            nonceStr: '{$jsConfig.nonceStr}',
            signature: '{$jsConfig.signature}',
            jsApiList: [
                'updateAppMessageShareData',
                'updateTimelineShareData',
                'getLocation'
            ]
        });
        
        wx.ready(function(){
            // 配置分享信息
            wx.updateAppMessageShareData({
                title: '{$location|default="景区"} 天气查询',
                desc: '查看景区实时天气和未来天气预报',
                link: window.location.href,
                imgUrl: '{$site.cdnurl}/assets/addons/dsassistant/images/functions/weather.jpg',
                success: function () {}
            });
        });
    </script>
    {/if}
</body>
</html>
