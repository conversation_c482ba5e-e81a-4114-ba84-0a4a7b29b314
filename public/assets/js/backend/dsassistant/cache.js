define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            Controller.api.bindevent();
        },
        refresh: function () {
            Controller.api.bindevent();
        },
        refreshKeywordMap: function () {
            Controller.api.bindevent();
        },
        refreshCategoryRule: function () {
            Controller.api.bindevent();
        },
        clear: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
