define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        _queryString: '',
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'dsassistant/scenicspottype/index' + Controller._queryString,
                    add_url: 'dsassistant/scenicspottype/add' + Controller._queryString,
                    edit_url: 'dsassistant/scenicspottype/edit',
                    del_url: 'dsassistant/scenicspottype/del',
                    multi_url: 'dsassistant/scenicspottype/multi',
                    import_url: 'dsassistant/scenicspottype/import',
                    table: 'ds_scenic_spot_type',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weight',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'code', title: __('Code'), operate: 'LIKE'},
                        {
                            field: 'icon', 
                            title: __('Icon'), 
                            operate: false,
                            formatter: function (value, row, index) {
                                if (value) {
                                    return '<i class="' + value + '"></i> ' + value;
                                }
                                return '-';
                            }
                        },
                        {
                            field: 'color', 
                            title: __('Color'), 
                            operate: false,
                            formatter: function (value, row, index) {
                                if (value) {
                                    return '<span style="display:inline-block;width:20px;height:20px;background-color:' + value + ';border-radius:3px;margin-right:5px;"></span>' + value;
                                }
                                return '-';
                            }
                        },
                        {field: 'description', title: __('Description'), operate: 'LIKE'},
                        {field: 'weight', title: __('Weight'), operate: false},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Status normal'),"hidden":__('Status hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
