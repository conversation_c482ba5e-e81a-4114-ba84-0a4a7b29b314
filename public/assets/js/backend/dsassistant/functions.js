define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'dsassistant/functions/index',
                    detail_url: 'dsassistant/functions/detail',
                    table: 'dsassistant_functions',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'name',
                sortName: 'priority',
                columns: [
                    [
                        {field: 'name', title: '函数名称', align: 'left'},
                        {field: 'description', title: '描述', align: 'left'},
                        {
                            field: 'source', 
                            title: '来源', 
                            align: 'center',
                            formatter: function (value, row, index) {
                                if (value === 'core') {
                                    return '<span class="label label-primary">核心</span>';
                                } else {
                                    return '<span class="label label-success">自定义</span>';
                                }
                            }
                        },
                        {field: 'priority', title: '优先级', align: 'center'},
                        {
                            field: 'enabled', 
                            title: '状态', 
                            align: 'center',
                            formatter: function (value, row, index) {
                                return '<a href="javascript:;" class="btn-toggle" data-name="' + row.name + '" data-enabled="' + (value ? 0 : 1) + '">' +
                                    (value ? '<span class="text-success"><i class="fa fa-toggle-on"></i> 已启用</span>' : '<span class="text-gray"><i class="fa fa-toggle-off"></i> 已禁用</span>') +
                                    '</a>';
                            },
                            events: {
                                'click .btn-toggle': function (e, value, row, index) {
                                    var name = $(this).data('name');
                                    var enabled = $(this).data('enabled');
                                    
                                    // 发送请求切换状态
                                    $.ajax({
                                        url: 'dsassistant/functions/toggle',
                                        type: 'post',
                                        dataType: 'json',
                                        data: {
                                            name: name,
                                            enabled: enabled
                                        },
                                        success: function (data) {
                                            if (data.code === 1) {
                                                Toastr.success(data.msg);
                                                table.bootstrapTable('refresh');
                                            } else {
                                                Toastr.error(data.msg);
                                            }
                                        },
                                        error: function () {
                                            Toastr.error('操作失败');
                                        }
                                    });
                                }
                            }
                        },
                        {
                            field: 'operate',
                            title: '操作',
                            table: table,
                            events: Table.api.events.operate,
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        detail: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
