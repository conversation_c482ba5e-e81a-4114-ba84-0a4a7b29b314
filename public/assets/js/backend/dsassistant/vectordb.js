define(['jquery'], function ($,) {
    var Controller = {
        index: function () { },
        rebuild: function () {
            // 初始化表单验证
            $('#rebuild-form').validator({});

            // 点击重建按钮
            $(document).on('click', '.btn-rebuild', function () {
                var that = this;
                $(that).attr('disabled', true).text('处理中...');

                Layer.confirm('确认要重建向量索引吗？此操作可能需要较长时间。', {
                    icon: 3,
                    title: '提示',
                    shadeClose: true
                }, function (index) {
                    Layer.close(index);

                    // 显示加载层
                    var loading = Layer.load(1, { shade: [0.1, '#fff'] });

                    // 提交表单
                    $('#rebuild-form').submit();
                }, function (index) {
                    Layer.close(index);
                    $(that).attr('disabled', false).text('开始重建索引');
                });

                return false;
            });
        },
        benchmark: function () {
            // 表单提交
            $('#benchmark-form').on('submit', function () {
                var that = this;
                $(that).find('.btn-benchmark').attr('disabled', true).text('测试中...');

                // 显示加载层
                var loading = Layer.load(1, { shade: [0.1, '#fff'] });

                // 提交表单
                $.ajax({
                    url: $(that).attr('action'),
                    type: 'post',
                    dataType: 'json',
                    data: $(that).serialize(),
                    success: function (response) {
                        Layer.close(loading);
                        $(that).find('.btn-benchmark').attr('disabled', false).text('开始测试');

                        if (response.code === 1) {
                            // 显示结果
                            var data = response.data;
                            var results = data.results;
                            var avgTime = data.avg_time;
                            var html = '';

                            $('#avg-time').text(avgTime + 'ms');

                            $.each(results, function (index, item) {
                                html += '<tr>';
                                html += '<td>' + item.question + '</td>';
                                html += '<td>' + item.best_match + '</td>';
                                html += '<td>' + item.score.toFixed(4) + '</td>';
                                html += '<td>' + item.time + '</td>';
                                html += '</tr>';
                            });

                            $('#result-body').html(html);
                            $('#result-container').show();
                        } else {
                            Toastr.error(response.msg);
                        }
                    },
                    error: function () {
                        Layer.close(loading);
                        $(that).find('.btn-benchmark').attr('disabled', false).text('开始测试');
                        Toastr.error('网络错误，请重试');
                    }
                });

                return false;
            });
        },
        config: function () {
            // 初始化表单验证
            $('#config-form').validator({
                rules: {
                    // 自定义验证规则
                },
                fields: {
                    // 字段验证规则
                }
            });

            // 显示当前选中的数据库配置
            function showSelectedDbConfig() {
                var type = $('#c-type').val();
                $('.db-config').hide();

                if (type !== 'mysql') {
                    $('#' + type + '-config').show();
                }
            }

            // 初始化显示
            showSelectedDbConfig();

            // 切换数据库类型时显示对应配置
            $('#c-type').change(function () {
                showSelectedDbConfig();
            });
        },
        test: function () {
            // 初始化表单验证
            $('#test-form').validator({
                fields: {
                    'question': 'required'
                }
            });

            // 表单提交
            $('#test-form').on('submit', function () {
                var that = this;
                $(that).find('.btn-search').attr('disabled', true).text('搜索中...');

                // 显示加载层
                var loading = Layer.load(1, { shade: [0.1, '#fff'] });

                // 提交表单
                $.ajax({
                    url: $(that).attr('action'),
                    type: 'post',
                    dataType: 'json',
                    data: $(that).serialize(),
                    success: function (response) {
                        Layer.close(loading);
                        $(that).find('.btn-search').attr('disabled', false).text('搜索');

                        if (response.code === 1) {
                            // 显示结果
                            var results = response.data;
                            var html = '';

                            if (results.length === 0) {
                                html = '<tr><td colspan="7" class="text-center">未找到匹配结果</td></tr>';
                            } else {
                                $.each(results, function (index, item) {
                                    html += '<tr>';
                                    html += '<td>' + item.question + '</td>';
                                    html += '<td>' + item.answer + '</td>';
                                    html += '<td>' + item.score.toFixed(4) + '</td>';
                                    html += '<td>' + item.similarity.toFixed(4) + '</td>';
                                    html += '<td>' + item.weight_factor.toFixed(4) + '</td>';
                                    html += '<td>' + item.category_boost.toFixed(4) + '</td>';
                                    html += '<td>' + item.keyword_boost.toFixed(4) + '</td>';
                                    html += '</tr>';
                                });
                            }

                            $('#result-body').html(html);
                            $('#result-container').show();
                        } else {
                            Toastr.error(response.msg);
                        }
                    },
                    error: function () {
                        Layer.close(loading);
                        $(that).find('.btn-search').attr('disabled', false).text('搜索');
                        Toastr.error('网络错误，请重试');
                    }
                });

                return false;
            });
        },
        migrate: function () {
            // 初始化表单验证
            $('#migrate-form').validator({});

            // 点击迁移按钮
            $(document).on('click', '.btn-migrate', function () {
                var that = this;
                $(that).attr('disabled', true).text('处理中...');

                Layer.confirm('确认要从MySQL迁移数据吗？此操作可能需要较长时间。', {
                    icon: 3,
                    title: '提示',
                    shadeClose: true
                }, function (index) {
                    Layer.close(index);

                    // 显示加载层
                    var loading = Layer.load(1, { shade: [0.1, '#fff'] });

                    // 提交表单
                    $('#migrate-form').submit();
                }, function (index) {
                    Layer.close(index);
                    $(that).attr('disabled', false).text('开始迁移');
                });

                return false;
            });
        }
    };
    return Controller;

})