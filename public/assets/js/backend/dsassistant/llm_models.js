define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        _queryString: '',
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'dsassistant/llm_models/index' + Controller._queryString,
                    add_url: 'dsassistant/llm_models/add' + Controller._queryString,
                    edit_url: 'dsassistant/llm_models/edit',
                    del_url: 'dsassistant/llm_models/del',
                    multi_url: 'dsassistant/llm_models/multi',
                    import_url: 'dsassistant/llm_models/import',
                    table: 'ds_llm_models',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weight',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'model_id', title: __('Model_id'), operate: 'LIKE'},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'provider', title: __('Provider'), operate: 'LIKE'},
                        {field: 'provider_name', title: __('Provider_name'), operate: 'LIKE'},
                        {field: 'base_url', title: __('Base_url'), operate: 'LIKE', formatter: Table.api.formatter.url},
                        {field: 'api_key', title: __('Api_key'), operate: 'LIKE'},
                        {field: 'supports_functions', title: __('Supports_functions'), searchList: {"1":__('Yes'),"0":__('No')}, formatter: Table.api.formatter.normal},
                        {field: 'max_tokens', title: __('Max_tokens')},
                        {field: 'enabled', title: __('Enabled'), searchList: {"1":__('Yes'),"0":__('No')}, formatter: Table.api.formatter.normal},
                        {field: 'is_default', title: __('Is_default'), searchList: {"1":__('Yes'),"0":__('No')}, formatter: Table.api.formatter.normal},
                        {field: 'weight', title: __('Weight')},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            Controller.api.rebuildAddUrl(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            queryString: function () {
                return location.search.replace("dialog=1", "").split('&').filter(function (item) {
                    return !!item;
                }).join("&");
            },
            rebuildAddUrl: function (table) {
                var $tabs = $('.nav-tabs[data-field]');
                if ($tabs.length > 0) {
                    var field = $tabs.data("field");
                    var options = table.bootstrapTable('getOptions');
                    table.on("pre-body.bs.table", function () {
                        var activeTab = $('.active a', $tabs);
                        var value = activeTab.data("value");
                        var reg = new RegExp(field + "\=(.*?)");
                        var queryString = location.search
                            .replace("dialog=1", "")
                            .replace(reg, "")
                            .split('&')
                            .filter(function (item) {
                                return !!item;
                            }).join("&");
                        if (queryString.indexOf("?") == 0) {
                            queryString = queryString + "&" + field + "=" + value
                        } else {
                            queryString = queryString + "?" + field + "=" + value
                        }
                        options.extend.add_url = 'dsassistant/llm_models/add' + queryString
                    })
                }
            }
        }
    };
    Controller._queryString = Controller.api.queryString();
    return Controller;
});