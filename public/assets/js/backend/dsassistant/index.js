define(['jquery', 'bootstrap', 'backend'], function ($, undefined, Backend) {

    var Controller = {
        _queryString: '',
        index: function () {
            Backend.init();
        },
        api: {
            queryString: function () {
                return location.search.replace("dialog=1", "").split('&').filter(function (item) {
                    return !!item;
                }).join("&");
            },
        }
    };
    Controller._queryString = Controller.api.queryString();
    return Controller;
});