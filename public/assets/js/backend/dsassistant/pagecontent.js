define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        _queryString: '',
        index: function () {
             // 初始化表格参数配置
             Table.api.init();
             this.tables.leftTable();
             this.tables.rightTable();
        },
        tables: {
            leftTable: function(){
                // 表格1
                var table1 = $("#table-left");
                table1.bootstrapTable({
                    url: 'dsassistant/pagecontent/pages',
                    toolbar: '#toolbar-left',
                    sortName: 'page',
                    search: false,
                    columns: [
                        [
                            // {field: 'state', checkbox: true,},
                            {field: 'page', title: '页面'},
                            {field: 'num', title: '数量'},
                            {
                                field: 'operate', title: __('Operate'), table: table1, events: Table.api.events.operate, buttons: [
                                    {
                                        name: 'text',
                                        text: '页面内容',
                                        title: '查看和管理页面的文案',
                                        icon: 'fa fa-list',
                                        classname: 'btn btn-primary btn-xs btn-click',
                                        click: function (e, data) {
                                            $("#myTabContent .form-commonsearch input[name='page']").val(data.page);
                                            $("#myTabContent .btn-refresh").trigger("click");
                                        }
                                    }
                                ], formatter: Table.api.formatter.operate
                            }
                        ]
                    ]
                });

                // 为表格1绑定事件
                Table.api.bindevent(table1);
            },
            rightTable: function(){
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'dsassistant/pagecontent/index' + Controller._queryString,
                        add_url: 'dsassistant/pagecontent/add' + Controller._queryString,
                        edit_url: 'dsassistant/pagecontent/edit',
                        del_url: 'dsassistant/pagecontent/del',
                        multi_url: 'dsassistant/pagecontent/multi',
                        import_url: 'dsassistant/pagecontent/import',
                        table: 'ds_page_content',
                    }
                });

                var table = $("#table");

                // 初始化表格
                table.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    pk: 'id',
                    sortName: 'id',
                    toolbar: '#toolbar',
                    columns: [
                        [
                            {checkbox: true},
                            {field: 'id', title: __('Id')},
                            {field: 'page', title: __('Page'), operate: 'LIKE'},
                            {field: 'section', title: __('Section'), operate: 'LIKE'},
                            {field: 'key', title: __('Key'), operate: 'LIKE'},
                            {field: 'description', title: __('Description'), operate: 'LIKE'},
                            // {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                            // {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                            {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                        ]
                    ]
                });

                // 为表格绑定事件
                Table.api.bindevent(table);
                Controller.api.rebuildAddUrl(table);
            }
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            queryString: function () {
                return location.search.replace("dialog=1", "").split('&').filter(function (item) {
                    return !!item;
                }).join("&");
            },
            rebuildAddUrl: function (table) {
                var $tabs = $('.nav-tabs[data-field]');
                if ($tabs.length > 0) {
                    var field = $tabs.data("field");
                    var options = table.bootstrapTable('getOptions');
                    table.on("pre-body.bs.table", function () {
                        var activeTab = $('.active a', $tabs);
                        var value = activeTab.data("value");
                        var reg = new RegExp(field + "\=(.*?)");
                        var queryString = location.search
                            .replace("dialog=1", "")
                            .replace(reg, "")
                            .split('&')
                            .filter(function (item) {
                                return !!item;
                            }).join("&");
                        if (queryString.indexOf("?") == 0) {
                            queryString = queryString + "&" + field + "=" + value
                        } else {
                            queryString = queryString + "?" + field + "=" + value
                        }
                        options.extend.add_url = 'dsassistant/pagecontent/add' + queryString
                    })
                }
            }
        }
    };
    Controller._queryString = Controller.api.queryString();
    return Controller;
});