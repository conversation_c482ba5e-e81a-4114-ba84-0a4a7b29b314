require.config({
    paths: {
        'dsassistant-jsoneditor': '../addons/dsassistant/libs/jsoneditor/9.9.2/jsoneditor.min',
    },
    shim: {
        'dsassistant-jsoneditor': {
            deps: ['css!../addons/dsassistant/libs/jsoneditor/9.9.2/jsoneditor.min.css']
        },
    }
});
define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'dsassistant-jsoneditor'], 
    function ($, undefined, Backend, Table, Form, JSONEditor) {

    var Controller = {
        _queryString: '',
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'dsassistant/converter_template/index' + Controller._queryString,
                    add_url: 'dsassistant/converter_template/add' + Controller._queryString,
                    edit_url: 'dsassistant/converter_template/edit',
                    del_url: 'dsassistant/converter_template/del',
                    multi_url: 'dsassistant/converter_template/multi',
                    import_url: 'dsassistant/converter_template/import',
                    execute_url: 'dsassistant/converter_template/execute',
                    executeall_url: 'dsassistant/converter_template/executeall',
                    initialize_url: 'dsassistant/converter_template/initialize',
                    table: 'ds_converter_template',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'source_table', title: __('Source_table'), operate: 'LIKE'},
                        {field: 'target_table', title: __('Target_table'), operate: 'LIKE'},
                        {field: 'category_id', title: __('Category_id'), searchList: Config.categoryruleList, formatter: Table.api.formatter.label},
                        {field: 'content_type', title: __('Content_type'), searchList: Config.contentTypeList, formatter: Table.api.formatter.label},
                        {field: 'source_type', title: __('Source_type'), searchList: Config.sourceTypeList, formatter: Table.api.formatter.label},
                        {field: 'sync_mode', title: __('Sync_mode'), searchList: {"full":__('Full'),"increment":__('Increment')}, formatter: Table.api.formatter.normal},
                        {field: 'sync_field', title: __('Sync_field'), operate: 'LIKE'},
                        {field: 'last_sync_time', title: __('Last_sync_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'last_sync_id', title: __('Last_sync_id')},
                        {field: 'where_condition', title: __('Where_condition'), operate: 'LIKE'},
                        {field: 'order_field', title: __('Order_field'), operate: 'LIKE'},
                        {field: 'order_direction', title: __('Order_direction'), searchList: {"asc":__('Asc'),"desc":__('Desc')}, formatter: Table.api.formatter.normal},
                        {field: 'batch_size', title: __('Batch_size')},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'execute',
                                    text: __('Execute'),
                                    icon: 'fa fa-play',
                                    classname: 'btn btn-xs btn-ajax btn-success btn-execute',
                                    url: 'dsassistant/converter_template/execute'
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            Controller.api.rebuildAddUrl(table);

            // // 添加初始化按钮
            // $(".btn-add").parent().after('<a href="javascript:;" class="btn btn-info btn-initialize" title="初始化模板"><i class="fa fa-magic"></i> 初始化模板</a>');

            // // 添加批量执行按钮
            // $(".btn-add").parent().after('<a href="javascript:;" class="btn btn-success btn-executeall btn-disabled disabled" title="批量执行"><i class="fa fa-play"></i> 批量执行</a>');

            // 初始化模板
            $(document).on("click", ".btn-initialize", function () {
                var url = $.fn.bootstrapTable.defaults.extend.initialize_url;
                Fast.api.open(url, '初始化模板');
            });

            // 批量执行
            $(document).on("click", ".btn-executeall", function () {
                var ids = Table.api.selectedids(table);
                var url = $.fn.bootstrapTable.defaults.extend.executeall_url + "?ids=" + ids.join(",");
                Fast.api.ajax({
                    url: url,
                    data: {force: 0}
                }, function (data, ret) {
                    if (ret.data && ret.data.messages) {
                        var html = '';
                        $.each(ret.data.messages, function (i, message) {
                            html += '<p>' + message + '</p>';
                        });
                        Layer.alert(html, {title: ret.msg});
                    }
                    return false;
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        execute: function () {
            // 提交表单
            Form.api.bindevent($("form[role=form]"), function (data, ret) {
                // 显示结果
                var html = '<p>总记录数: ' + data.total + '</p>';
                html += '<p>成功转换: ' + data.success + '</p>';
                html += '<p>失败记录: ' + data.error + '</p>';
                Layer.alert(html, {title: ret.msg});
                return false;
            });
        },
        initialize: function () {
            // 提交表单
            Form.api.bindevent($("form[role=form]"));
        },
        api: {
            bindevent: function () {
                Controller.api.jsoneditor();
                Form.api.bindevent($("form[role=form]"));
            },
            queryString: function () {
                return location.search.replace("dialog=1", "").split('&').filter(function (item) {
                    return !!item;
                }).join("&");
            },
            rebuildAddUrl: function (table) {
                var $tabs = $('.nav-tabs[data-field]');
                if ($tabs.length > 0) {
                    var field = $tabs.data("field");
                    var options = table.bootstrapTable('getOptions');
                    table.on("pre-body.bs.table", function () {
                        var activeTab = $('.active a', $tabs);
                        var value = activeTab.data("value");
                        var reg = new RegExp(field + "\=(.*?)");
                        var queryString = location.search
                            .replace("dialog=1", "")
                            .replace(reg, "")
                            .split('&')
                            .filter(function (item) {
                                return !!item;
                            }).join("&");
                        if (queryString.indexOf("?") == 0) {
                            queryString = queryString + "&" + field + "=" + value
                        } else {
                            queryString = queryString + "?" + field + "=" + value
                        }
                        options.extend.add_url = 'dsassistant/converter_template/add' + queryString
                    })
                }
            },
            jsoneditor: function () {
                var container = document.getElementById('jsoneditor');
                var options = {
                    mode: 'code',
                    modes: ['tree', 'view', 'form', 'code', 'text'],
                    onChange: function () {
                        var json = editor.get();
                        $('#c-template_content').val(JSON.stringify(json));
                    }
                };
                var editor = new JSONEditor(container, options);
                
                // 设置初始值
                try {
                    var initialJson = JSON.parse($('#c-template_content').val() || '{}');
                    editor.set(initialJson);
                } catch (e) {
                    editor.set({});
                }
            }
        }
    };
    Controller._queryString = Controller.api.queryString();
    return Controller;
});