/* 预警信息页面特定样式 */
.warning-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
    transition: var(--transition);
    background-color: white;
    border: none;
    position: relative;
}

.warning-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.warning-card.warning-high {
    border-left: 4px solid var(--danger-color);
}

.warning-card.warning-medium {
    border-left: 4px solid #f59e0b;
}

.warning-card.warning-low {
    border-left: 4px solid var(--secondary-color);
}

.warning-header {
    padding: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.warning-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    z-index: 0;
}

.warning-body {
    padding: 1.5rem;
    background-color: white;
}

.warning-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.warning-content {
    color: var(--dark-color);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.warning-meta {
    color: var(--gray-color);
    font-size: 0.875rem;
    margin-top: 1rem;
}

.warning-meta i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.warning-meta div {
    margin-bottom: 0.5rem;
}

.level-info {
    background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%);
}

.level-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.level-danger {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.type-icon {
    font-size: 2rem;
    margin-right: 1rem;
    position: relative;
    z-index: 1;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.warning-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 1;
}

.badge-info {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.badge-warning {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.badge-danger {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}
