body {
    background-color: #f8f9fa;
    padding-top: 20px;
    padding-bottom: 40px;
}
.container {
    max-width: 800px;
}
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}
.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    font-weight: bold;
}
.item-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 15px;
}
.item-meta {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 15px;
}
.item-meta i {
    margin-right: 5px;
}
.item-description {
    margin-bottom: 20px;
    line-height: 1.6;
}
.item-images {
    margin-bottom: 20px;
}
.item-image {
    width: 100%;
    border-radius: 5px;
    margin-bottom: 10px;
}
.contact-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}
.btn-back {
    margin-bottom: 20px;
}
.badge-lost {
    background-color: #dc3545;
    color: white;
}
.badge-found {
    background-color: #28a745;
    color: white;
}
.carousel-item img {
    max-height: 400px;
    object-fit: contain;
}