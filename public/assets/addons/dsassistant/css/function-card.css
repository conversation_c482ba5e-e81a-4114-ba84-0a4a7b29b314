/* 函数调用卡片样式 */
.function-cards {
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.function-card {
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s ease;
}

.function-card:hover {
    background-color: #f0f2f5;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background-color: #e6f7ff;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.card-icon i {
    font-size: 18px;
    color: var(--primary-color);
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.card-description {
    font-size: 12px;
    color: #666;
}

.card-action {
    padding: 6px 12px;
    background-color: var(--primary-color);
    border-radius: 20px;
    margin-left: 10px;
    flex-shrink: 0;
}

.card-action span {
    font-size: 12px;
    color: #fff;
    white-space: nowrap;
}

/* 响应式调整 */
@media (min-width: 576px) {
    .function-card {
        padding: 14px;
    }
    
    .card-icon {
        width: 45px;
        height: 45px;
    }
    
    .card-icon i {
        font-size: 20px;
    }
    
    .card-title {
        font-size: 15px;
    }
    
    .card-description {
        font-size: 13px;
    }
    
    .card-action {
        padding: 7px 14px;
    }
    
    .card-action span {
        font-size: 13px;
    }
}

@media (min-width: 768px) {
    .function-cards {
        margin-top: 20px;
        gap: 12px;
    }
    
    .function-card {
        padding: 16px;
        border-radius: 14px;
    }
    
    .card-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        margin-right: 15px;
    }
    
    .card-icon i {
        font-size: 22px;
    }
    
    .card-title {
        font-size: 16px;
        margin-bottom: 5px;
    }
    
    .card-description {
        font-size: 14px;
    }
    
    .card-action {
        padding: 8px 16px;
        border-radius: 22px;
    }
    
    .card-action span {
        font-size: 14px;
    }
}
