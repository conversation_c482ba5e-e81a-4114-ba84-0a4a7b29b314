.jumbotron {
    background-image: url('/assets/addons/dsassistant/img/header-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 120px 0;
    margin-bottom: 0;
    position: relative;
    border-radius: 0;
}

.jumbotron::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.jumbotron .container {
    position: relative;
    z-index: 1;
}

.jumbotron h1 {
    font-weight: 700;
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
}

.jumbotron .lead {
    font-size: 1.5rem;
    font-weight: 300;
    margin-bottom: 2rem;
}

.feature-box {
    padding: 2.5rem 1.5rem;
    text-align: center;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    transition: var(--transition);
    box-shadow: var(--card-shadow);
    background-color: white;
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.feature-box:hover .feature-icon {
    transform: scale(1.1);
}

.section {
    padding: 5rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
    position: relative;
    display: inline-block;
}

.section-title h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.section-title p {
    color: var(--gray-color);
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
}

.bg-light-blue {
    background-color: #f1f5f9;
}

.cta-section {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.cta-section .ai-pattern {
    opacity: 0.2;
}

.cta-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.cta-text {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}
