@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --secondary-color: #10b981;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --gray-color: #94a3b8;
    --danger-color: #ef4444;
    --success-color: #10b981;
    --border-radius: 12px;
    --card-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.01);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* 响应式断点 */
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
}

/* 移动端优先的基础样式 */
html {
    box-sizing: border-box;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

*, *::before, *::after {
    box-sizing: inherit;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
    font-size: 16px;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

img {
    max-width: 100%;
    height: auto;
}

/* 响应式容器 */
.container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* 媒体查询使用移动端优先的方法 */
@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

/* 现代化导航栏 - 移动端优先 */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 0.75rem 0;
    position: sticky;
    top: 0;
    z-index: 1030;
}

.navbar-brand {
    color: var(--dark-color) !important;
    font-weight: 600;
    font-size: 1.25rem;
    padding: 0.5rem 0;
}

.navbar-brand i {
    font-size: 1.1rem;
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
    outline: none !important;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link i {
    margin-right: 0.25rem;
    font-size: 0.9rem;
}

.nav-link.active {
    color: var(--primary-color) !important;
    font-weight: 600;
    background-color: rgba(99, 102, 241, 0.08);
}

.nav-link:hover:not(.active) {
    background-color: rgba(0, 0, 0, 0.03);
}

/* 导航栏响应式调整 */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .nav-link {
        padding: 0.75rem;
        margin-bottom: 0.25rem;
    }

    .navbar-nav {
        padding-top: 0.5rem;
    }
}

@media (min-width: 992px) {
    .navbar {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        margin-right: 2rem;
    }

    .nav-link {
        margin: 0 0.25rem;
    }
}

/* 页面标题区域 - 移动端优先 */
.page-header {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    padding: 1.5rem 1rem;
    position: relative;
    overflow: hidden;
    /* margin-bottom: 1.5rem; */
    border-radius: 0;
}

/* 页面标题区域内部元素统一样式 */
.page-header .container {
    padding: 0; /* 移除容器内边距，避免嵌套容器的内边距叠加 */
    position: relative;
    z-index: 1;
}

.page-header h1 {
    font-size: 1.75rem; /* 移动端标题大小 */
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.page-header .lead {
    font-size: 1rem; /* 移动端副标题大小 */
    margin-bottom: 0;
    line-height: 1.5;
}

.page-header .py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

/* 页面标题区域装饰元素 */
.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -30%;
    width: 80%;
    height: 80%;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
}

/* 页面标题区域按钮 */
.page-header .btn {
    margin-top: 0.75rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* 在移动端隐藏页面标题区域的副标题 */
@media (max-width: 576px) {
    .page-header .lead {
        display: none;
    }

    .page-header {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }

    .page-header h1 {
        font-size: 1.5rem !important;
        margin-bottom: 0 !important;
    }
}

/* 页面标题区域响应式调整 */
@media (min-width: 576px) {
    .page-header {
        padding: 1.75rem 1.25rem;
        /* border-radius: var(--border-radius); */
        /* margin: 0.75rem 0.75rem 1.5rem; */
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .page-header .lead {
        font-size: 1.05rem;
    }
}

@media (min-width: 768px) {
    .page-header {
        padding: 2rem 1.5rem;
        /* margin: 1rem 1rem 1.75rem; */
    }

    .page-header h1 {
        font-size: 2.25rem;
    }

    .page-header .lead {
        font-size: 1.1rem;
    }

    .page-header .py-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }

    .page-header .btn {
        padding: 0.6rem 1.5rem;
        font-size: 1rem;
    }
}

@media (min-width: 992px) {
    .page-header {
        padding: 2.5rem 2rem;
        /* margin: 1.5rem 1.5rem 2rem; */
    }

    .page-header h1 {
        font-size: 2.5rem;
    }
}

/* 按钮样式 - 移动端优先 */
.btn {
    border-radius: 50px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    touch-action: manipulation;
    cursor: pointer;
    user-select: none;
}

/* 按钮中的图标垂直居中 */
.btn i {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    font-size: 0.875rem;
    margin-right: 0.25rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
}

/* 按钮尺寸变体 */
.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* 按钮响应式调整 */
@media (min-width: 576px) {
    .btn {
        padding: 0.55rem 1.25rem;
        font-size: 0.9rem;
    }

    .btn i {
        font-size: 0.9rem;
    }

    .btn-sm {
        padding: 0.3rem 0.85rem;
        font-size: 0.8rem;
    }

    .btn-lg {
        padding: 0.8rem 1.75rem;
        font-size: 1.05rem;
    }
}

@media (min-width: 768px) {
    .btn {
        padding: 0.6rem 1.5rem;
        font-size: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .btn i {
        font-size: 1rem;
        margin-right: 0.5rem;
    }

    .btn-sm {
        padding: 0.35rem 1rem;
        font-size: 0.875rem;
    }

    .btn-lg {
        padding: 0.85rem 2rem;
        font-size: 1.1rem;
    }
}

/* 标签页样式 */
.nav-tabs {
    border: none;
    background-color: white;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    color: var(--gray-color);
    transition: var(--transition);
}

.nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    color: white !important;
    font-weight: 500;
}

/* 卡片样式 - 移动端优先 */
.item-card, .card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    transition: var(--transition);
    background-color: white;
    border: none;
    width: 100%;
}

.item-card:hover, .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.item-img, .card-img-top {
    height: 180px;
    object-fit: cover;
    transition: var(--transition);
    width: 100%;
}

.item-card:hover .item-img, .card:hover .card-img-top {
    transform: scale(1.03);
}

.item-info, .card-body {
    padding: 1rem;
}

.item-title, .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    line-height: 1.3;
}

.item-desc, .card-text {
    color: var(--gray-color);
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    height: 4.05em; /* 3行文本的高度 */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3; /* 标准属性 */
    -webkit-box-orient: vertical;
    line-height: 1.35;
}

.item-meta {
    color: var(--gray-color);
    font-size: 0.8rem;
    margin-bottom: 0.75rem;
}

.item-meta i {
    margin-right: 0.35rem;
    color: var(--primary-color);
    font-size: 0.85rem;
}

.item-meta div {
    margin-bottom: 0.35rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 卡片响应式调整 */
@media (min-width: 576px) {
    .item-card, .card {
        margin-bottom: 1.75rem;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05);
    }

    .item-img, .card-img-top {
        height: 200px;
    }

    .item-info, .card-body {
        padding: 1.25rem;
    }

    .item-title, .card-title {
        font-size: 1.15rem;
    }

    .item-desc, .card-text {
        font-size: 0.925rem;
    }

    .item-meta {
        font-size: 0.825rem;
    }
}

@media (min-width: 768px) {
    .item-card, .card {
        margin-bottom: 2rem;
        box-shadow: var(--card-shadow);
    }

    .item-card:hover, .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .item-img, .card-img-top {
        height: 220px;
    }

    .item-card:hover .item-img, .card:hover .card-img-top {
        transform: scale(1.05);
    }

    .item-info, .card-body {
        padding: 1.5rem;
    }

    .item-title, .card-title {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .item-desc, .card-text {
        font-size: 1rem;
        margin-bottom: 1rem;
        height: 4.8em;
    }

    .item-meta {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .item-meta i {
        margin-right: 0.5rem;
    }

    .item-meta div {
        margin-bottom: 0.5rem;
    }
}

/* 徽章样式 */
.badge {
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 50px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-lost {
    background-color: var(--danger-color);
    color: white;
}

.badge-found {
    background-color: var(--success-color);
    color: white;
}

/* 模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--card-shadow);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.form-group {
    margin-bottom: 1.5rem;
}

/* 分页样式 */
.pagination {
    margin-top: 2rem;
}

.page-item .page-link {
    border: none;
    color: var(--dark-color);
    margin: 0 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
}

.page-item .page-link:hover {
    background-color: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

/* 页脚样式 - 移动端优先 */
footer {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    color: white;
    padding: 2rem 0 1.5rem;
    margin-top: 2.5rem;
    font-size: 0.9rem;
    position: relative;
}

footer h4 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

footer h5 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

footer p {
    font-size: 0.9rem;
    line-height: 1.5;
    opacity: 0.85;
}

footer .list-unstyled li {
    margin-bottom: 0.5rem;
}

footer a.text-white {
    transition: var(--transition);
    text-decoration: none;
    opacity: 0.85;
    display: inline-flex;
    align-items: center;
}

footer a.text-white:hover {
    color: var(--primary-color) !important;
    text-decoration: none;
    opacity: 1;
}

footer a.text-white i {
    margin-right: 0.5rem;
    font-size: 0.8rem;
}

footer hr {
    opacity: 0.1;
    margin: 1.5rem 0;
}

footer .mb-4 {
    margin-bottom: 1.5rem !important;
}

/* 页脚响应式调整 */
@media (min-width: 576px) {
    footer {
        padding: 2.5rem 0 2rem;
        margin-top: 3rem;
    }

    footer h4 {
        font-size: 1.35rem;
    }

    footer h5 {
        font-size: 1.1rem;
    }

    footer p {
        font-size: 0.95rem;
    }
}

@media (min-width: 768px) {
    footer {
        padding: 3rem 0;
        margin-top: 4rem;
        font-size: 1rem;
    }

    footer h4 {
        font-size: 1.5rem;
        margin-bottom: 1.25rem;
    }

    footer h5 {
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }

    footer p {
        font-size: 1rem;
    }

    footer a.text-white i {
        font-size: 0.9rem;
    }

    footer hr {
        margin: 2rem 0;
    }
}

/* AI科技感元素 */
.ai-pattern {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.5;
    pointer-events: none;
}

.glow-effect {
    position: relative;
}

.glow-effect::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #6366f1, #8b5cf6, #10b981, #6366f1);
    background-size: 400% 400%;
    border-radius: calc(var(--border-radius) + 10px);
    z-index: -1;
    animation: glowAnimation 10s ease infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glow-effect:hover::before {
    opacity: 0.7;
}

@keyframes glowAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 加载动画 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(99, 102, 241, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    margin-top: 1rem;
    color: var(--gray-color);
    font-weight: 500;
}

/* 图标圆圈 */
.icon-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

@media (min-width: 768px) {
    .icon-circle {
        width: 40px;
        height: 40px;
    }
}

/* 响应式工具类 */
/* 在小屏幕上隐藏元素 */
.hide-sm {
    display: none !important;
}

/* 在中等屏幕及以上显示元素 */
@media (min-width: 768px) {
    .hide-sm {
        display: initial !important;
    }

    .show-sm {
        display: none !important;
    }
}

/* 文本截断 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 多行文本截断 */
.text-truncate-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* 标准属性 */
    -webkit-box-orient: vertical;
}

.text-truncate-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3; /* 标准属性 */
    -webkit-box-orient: vertical;
}

/* 响应式间距 */
.mb-sm-1 {
    margin-bottom: 0.25rem !important;
}

.mb-sm-2 {
    margin-bottom: 0.5rem !important;
}

.mb-sm-3 {
    margin-bottom: 1rem !important;
}

@media (min-width: 768px) {
    .mb-md-0 {
        margin-bottom: 0 !important;
    }

    .mb-md-4 {
        margin-bottom: 1.5rem !important;
    }

    .mb-md-5 {
        margin-bottom: 3rem !important;
    }
}

/* 聊天界面样式 */
.chat-container {
    /* border-radius: var(--border-radius); */
    border-radius: 0;
    background-color: white;
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.chat-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    padding: 1.5rem;
}

.message {
    margin-bottom: 1.5rem;
    display: flex;
}

.message-user {
    justify-content: flex-end;
}

.message-content {
    max-width: 70%;
    padding: 1rem;
    border-radius: 18px;
}

.message-user .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: 4px;
}

.message-ai .message-content {
    background-color: #f1f5f9;
    color: var(--dark-color);
    border-bottom-left-radius: 4px;
}

.chat-input {
    /* padding: 1rem; */
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.chat-input .form-control {
    border-radius: 50px;
}

.chat-input .btn {
    border-radius: 50px;
    width: 50px;
    height: 50px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 景点卡片样式 */
.spot-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
    transition: var(--transition);
    background-color: white;
    border: none;
}

.spot-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.spot-img {
    height: 220px;
    object-fit: cover;
    transition: var(--transition);
}

.spot-card:hover .spot-img {
    transform: scale(1.05);
}

.spot-info {
    padding: 1.5rem;
}

.spot-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--dark-color);
}

.spot-desc {
    color: var(--gray-color);
    margin-bottom: 1rem;
    height: 4.8em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3; /* 标准属性 */
    -webkit-box-orient: vertical;
}

.spot-meta {
    color: var(--gray-color);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.spot-meta i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.spot-meta div {
    margin-bottom: 0.5rem;
}

/* 地图容器 */
.map-container {
    height: 500px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}

/* 预警信息样式 */
.warning-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
    transition: var(--transition);
    background-color: white;
    border: none;
}

.warning-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.warning-card.warning-high {
    border-left: 4px solid var(--danger-color);
}

.warning-card.warning-medium {
    border-left: 4px solid #f59e0b;
}

.warning-card.warning-low {
    border-left: 4px solid var(--secondary-color);
}

.warning-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.warning-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.warning-meta {
    color: var(--gray-color);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

.warning-meta i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.warning-meta span {
    margin-right: 1.5rem;
}

.warning-body {
    padding: 1.5rem;
}

.warning-content {
    color: var(--dark-color);
    line-height: 1.7;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem;
    }

    .page-header {
        padding: 2rem 1rem;
        display:none;
    }

    .item-img, .card-img-top, .spot-img {
        height: 180px;
    }

    .map-container {
        height: 300px;
    }

    .chat-messages {
        height: 350px;
    }

    .message-content {
        max-width: 85%;
    }
}
