/* 移动端优先的样式 */

/* 地图容器 - 移动端优先 */
.map-container {
    height: 300px; /* 移动端使用更小的高度，让热门景点部分露出 */
    margin-bottom: 1rem; /* 减小底部间距 */
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    position: relative;
}

#map {
    width: 100%;
    height: 100%;
}

/* 在地图底部添加渐变提示，暗示下方有内容可滚动 */
.map-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(248,250,252,0.8));
    pointer-events: none; /* 确保不会干扰地图交互 */
    z-index: 10;
}

/* 添加向下滚动提示 */
.scroll-hint {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 0.8rem;
    color: var(--dark-color);
    box-shadow: var(--card-shadow);
    z-index: 20;
    display: flex;
    align-items: center;
    animation: bounce 2s infinite;
}

.scroll-hint i {
    margin-left: 5px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* 响应式调整 */
@media (min-width: 576px) {
    .map-container {
        height: 350px;
        margin-bottom: 1.5rem;
    }
}

@media (min-width: 768px) {
    .map-container {
        height: 400px;
        margin-bottom: 1.75rem;
    }

    .scroll-hint {
        display: none; /* 在较大屏幕上隐藏滚动提示 */
    }
}

@media (min-width: 992px) {
    .map-container {
        height: 500px;
        margin-bottom: 2rem;
    }

    .map-container::after {
        display: none; /* 在大屏幕上隐藏渐变提示 */
    }
}

.map-overlay {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    z-index: 1000;
    max-width: 300px;
}

.map-overlay h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.map-overlay p {
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-bottom: 0;
}

/* 景点卡片 - 移动端优先 */
.spot-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.25rem;
    transition: var(--transition);
    background-color: white;
    border: none;
    height: 100%;
}

.spot-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.spot-img {
    height: 180px; /* 移动端使用更小的图片高度 */
    object-fit: cover;
    transition: var(--transition);
}

.spot-card:hover .spot-img {
    transform: scale(1.03);
}

.spot-info {
    padding: 1rem; /* 移动端使用更小的内边距 */
}

.spot-title {
    font-size: 1.1rem; /* 移动端使用更小的标题字体 */
    font-weight: 600;
    margin-bottom: 0.5rem; /* 减小底部间距 */
    color: var(--dark-color);
}

.spot-desc {
    color: var(--gray-color);
    margin-bottom: 0.75rem; /* 减小底部间距 */
    height: 4.05em; /* 约3行的高度 */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-height: 1.35; /* 移动端使用更紧凑的行高 */
    font-size: 0.9rem; /* 移动端使用更小的字体 */
}

.spot-meta {
    color: var(--gray-color);
    font-size: 0.8rem; /* 移动端使用更小的字体 */
    margin-bottom: 0.75rem; /* 减小底部间距 */
}

.spot-meta i {
    margin-right: 0.35rem; /* 减小图标右边距 */
    color: var(--primary-color);
    font-size: 0.85rem; /* 移动端使用更小的图标 */
}

.spot-meta div {
    margin-bottom: 0.35rem; /* 减小底部间距 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; /* 确保文本不会换行，超出部分显示省略号 */
}

/* 移动端优化按钮组 */
.spot-card .btn {
    padding: 0.5rem 0.75rem; /* 移动端使用更小的按钮内边距 */
    font-size: 0.85rem; /* 移动端使用更小的按钮字体 */
}

.spot-card .btn i {
    margin-right: 0.25rem; /* 减小图标右边距 */
}

/* 响应式调整 */
@media (min-width: 576px) {
    .spot-card {
        margin-bottom: 1.5rem;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05);
    }

    .spot-img {
        height: 200px;
    }

    .spot-info {
        padding: 1.25rem;
    }

    .spot-title {
        font-size: 1.15rem;
        margin-bottom: 0.6rem;
    }

    .spot-desc {
        font-size: 0.925rem;
        margin-bottom: 0.85rem;
        line-height: 1.45;
    }

    .spot-meta {
        font-size: 0.825rem;
        margin-bottom: 0.85rem;
    }

    .spot-card .btn {
        padding: 0.55rem 0.85rem;
        font-size: 0.9rem;
    }
}

@media (min-width: 768px) {
    .spot-card {
        margin-bottom: 2rem;
        box-shadow: var(--card-shadow);
    }

    .spot-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .spot-img {
        height: 220px;
    }

    .spot-card:hover .spot-img {
        transform: scale(1.05);
    }

    .spot-info {
        padding: 1.5rem;
    }

    .spot-title {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .spot-desc {
        font-size: 1rem;
        margin-bottom: 1rem;
        height: 4.8em;
        line-height: 1.6;
    }

    .spot-meta {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .spot-meta i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    .spot-meta div {
        margin-bottom: 0.5rem;
    }

    .spot-card .btn {
        padding: 0.6rem 1.5rem;
        font-size: 1rem;
    }

    .spot-card .btn i {
        margin-right: 0.5rem;
    }
}

/* 溢出隐藏样式 */
.spot-description-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* 显示3行 */
    line-clamp: 3; /* 标准属性 */
    -webkit-box-orient: vertical;
    max-height: 4.5em; /* 约3行的高度 */
    line-height: 1.5;
}

/* 模态框描述样式 */
.spot-description-modal {
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.6;
    padding-right: 5px;
}