/* 移动端优先的聊天界面样式 */
body {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--light-color);
    overflow: hidden; /* 防止页面滚动，使聊天界面占满屏幕 */
}

.navbar {
    flex-shrink: 0;
}

/* 聊天页面特定样式 */

.chat-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding: 0.5rem; /* 移动端使用更小的内边距 */
    max-width: 100%; /* 移动端占满屏幕宽度 */
    margin: 0 auto;
    width: 100%;
    overflow: hidden; /* 防止内容溢出 */
}

.chat-box {
    flex-grow: 1;
    overflow-y: auto;
    border-radius: 0;
    padding: 0.75rem; 
    /* 移动端使用更小的内边距 */
    background-color: white;
    margin-bottom: 0.75rem; /* 减小底部间距 */
    height: calc(100vh - 180px); /* 移动端使用更大的高度 */
    /* box-shadow: var(--card-shadow); */
    -webkit-overflow-scrolling: touch; /* 在iOS上提供更好的滚动体验 */
}

/* 响应式调整 */
@media (min-width: 576px) {
 

    .chat-container {
        padding: 0.75rem;
        padding-top: 0.5rem;
    }

    .chat-box {
        padding: 1rem;
        margin-bottom: 1rem;
        height: calc(100vh - 220px);
    }
}

@media (min-width: 768px) {


    .chat-container {
        padding: 1rem;
        max-width: 900px;
    }

    .chat-box {
        padding: 1.25rem;
        margin-bottom: 1.25rem;
        height: calc(100vh - 250px);
        min-height: 300px;
    }
}

@media (min-width: 992px) {
    .chat-container {
        padding: 1.5rem;
        padding-top: 1rem;
    }

    .chat-box {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        height: calc(100vh - 280px);
        max-height: 600px;
    }
}

.message {
    margin-bottom: 1rem; /* 移动端使用更小的间距 */
    display: flex;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 85%; /* 移动端使用更大的宽度比例 */
    padding: 0.75rem 1rem; /* 移动端使用更小的内边距 */
    border-radius: 16px; /* 移动端使用更小的圆角 */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
    line-height: 1.5; /* 移动端使用更紧凑的行高 */
    position: relative;
    font-size: 0.95rem; /* 移动端使用更小的字体 */
}

/* 响应式调整 */
@media (min-width: 576px) {
    .message {
        margin-bottom: 1.25rem;
    }

    .message-content {
        max-width: 80%;
        padding: 0.85rem 1.15rem;
        border-radius: 17px;
        font-size: 0.975rem;
    }
}

@media (min-width: 768px) {
    .message {
        margin-bottom: 1.5rem;
    }

    .message-content {
        max-width: 75%;
        padding: 1rem 1.25rem;
        border-radius: 18px;
        line-height: 1.6;
        font-size: 1rem;
    }
}

.user .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: 5px;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
}

.user .message-content::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: -8px;
    width: 16px;
    height: 16px;
    background-color: var(--primary-color);
    border-radius: 0 0 0 16px;
    clip-path: polygon(0 0, 0% 100%, 100% 100%);
}

.assistant .message-content {
    background-color: white;
    color: var(--dark-color);
    border-bottom-left-radius: 5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.assistant .message-content::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -8px;
    width: 16px;
    height: 16px;
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-right: 0;
    border-top: 0;
    border-radius: 0 0 16px 0;
    clip-path: polygon(100% 0, 0 100%, 100% 100%);
}

.input-group {
    margin-top: auto;
    box-shadow: var(--card-shadow);
    border-radius: 50px;
    overflow: hidden;
    background-color: white;
    display: flex;
    align-items: stretch; /* 确保子元素拉伸到相同高度 */
}

.input-group .form-control {
    border: none;
    padding: 0.75rem 1rem; /* 移动端使用更小的内边距 */
    font-size: 0.95rem; /* 移动端使用更小的字体 */
    background-color: white;
    height: auto; /* 使用自动高度，而不是固定高度 */
    line-height: 1.5; /* 确保文本垂直居中 */
}

.input-group .form-control:focus {
    box-shadow: none;
}

.input-group .btn {
    padding: 0.5rem 1rem; /* 移动端使用更小的内边距 */
    border-radius: 0;
    height: 100%;
    min-width: 80px; /* 移动端使用更小的按钮宽度 */
}

.input-group .btn span {
    display: none; /* 在移动端隐藏按钮文本，只显示图标 */
}

/* 响应式调整 */
@media (min-width: 576px) {
    .input-group .form-control {
        padding: 0.85rem 1.25rem;
        font-size: 0.975rem;
    }

    .input-group .btn {
        padding: 0.6rem 1.25rem;
        min-width: 90px;
    }
}

@media (min-width: 768px) {
    .input-group .form-control {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .input-group .btn {
        padding: 0.75rem 1.5rem;
        min-width: 100px;
    }

    .input-group .btn span {
        display: inline; /* 在大屏幕上显示按钮文本 */
    }
}

.typing-indicator {
    margin-bottom: 1.5rem;
}

.typing-indicator .message-content {
    background-color: white;
    color: var(--gray-color);
    display: flex;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.typing-indicator .thinking-text {
    margin-right: 10px;
    font-weight: 500;
}

.typing-dots {
    display: inline-flex;
}

.typing-dots span {
    height: 8px;
    width: 8px;
    margin: 0 2px;
    background-color: var(--primary-color);
    display: block;
    border-radius: 50%;
    opacity: 0.4;
}

.typing-dots span:nth-of-type(1) {
    animation: 1s blink infinite 0.3333s;
}

.typing-dots span:nth-of-type(2) {
    animation: 1s blink infinite 0.6666s;
}

.typing-dots span:nth-of-type(3) {
    animation: 1s blink infinite 0.9999s;
}

@keyframes blink {
    50% {
        opacity: 1;
    }
}


/* Markdown样式 - 移动端优先 */
.markdown-content {
    font-size: 13px; /* 移动端使用更小的基础字体 */
}

.markdown-content p {
    margin-bottom: 8px; /* 移动端使用更小的段落间距 */
}

.markdown-content h1, .markdown-content h2, .markdown-content h3,
.markdown-content h4, .markdown-content h5, .markdown-content h6 {
    margin-top: 12px; /* 移动端使用更小的标题上边距 */
    margin-bottom: 8px; /* 移动端使用更小的标题下边距 */
    font-weight: 600;
    line-height: 1.3; /* 移动端使用更紧凑的行高 */
}

.markdown-content h1 { font-size: 1.4em; } /* 移动端使用更小的标题字体 */
.markdown-content h2 { font-size: 1.3em; }
.markdown-content h3 { font-size: 1.15em; }
.markdown-content h4 { font-size: 1.05em; }
.markdown-content h5, .markdown-content h6 { font-size: 1em; }

.markdown-content ul, .markdown-content ol {
    margin-bottom: 8px; /* 移动端使用更小的列表间距 */
    padding-left: 16px; /* 移动端使用更小的列表缩进 */
}

.markdown-content blockquote {
    padding: 0 8px; /* 移动端使用更小的引用内边距 */
    color: #6a737d;
    border-left: 2px solid #dfe2e5; /* 移动端使用更细的引用边框 */
    margin: 8px 0; /* 移动端使用更小的引用外边距 */
}

.markdown-content pre {
    background-color: #f6f8fa;
    border-radius: 3px;
    padding: 8px; /* 移动端使用更小的代码块内边距 */
    overflow: auto;
    margin-bottom: 8px; /* 移动端使用更小的代码块外边距 */
    font-family: monospace;
    font-size: 12px; /* 移动端使用更小的代码字体 */
}

.markdown-content code {
    font-family: monospace;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
    padding: 0.15em 0.3em; /* 移动端使用更小的内联代码内边距 */
    font-size: 85%;
}

.markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 8px; /* 移动端使用更小的表格外边距 */
    font-size: 12px; /* 移动端使用更小的表格字体 */
}

.markdown-content table th, .markdown-content table td {
    border: 1px solid #dfe2e5;
    padding: 4px 8px; /* 移动端使用更小的表格单元格内边距 */
}

.markdown-content table th {
    background-color: #f6f8fa;
}

.markdown-content img {
    max-width: 100%;
    height: auto;
}

.markdown-content hr {
    height: 1px;
    background-color: #dfe2e5;
    border: none;
    margin: 12px 0; /* 移动端使用更小的分隔线外边距 */
}

.assistant .markdown-content a {
    color: #0366d6;
    text-decoration: none;
}

.assistant .markdown-content a:hover {
    text-decoration: underline;
}

/* 响应式调整 */
@media (min-width: 576px) {
    .markdown-content {
        font-size: 13.5px;
    }

    .markdown-content p {
        margin-bottom: 9px;
    }

    .markdown-content h1, .markdown-content h2, .markdown-content h3,
    .markdown-content h4, .markdown-content h5, .markdown-content h6 {
        margin-top: 14px;
        margin-bottom: 9px;
    }

    .markdown-content h1 { font-size: 1.5em; }
    .markdown-content h2 { font-size: 1.35em; }
    .markdown-content h3 { font-size: 1.18em; }
    .markdown-content h4 { font-size: 1.08em; }

    .markdown-content pre {
        padding: 9px;
        font-size: 12.5px;
    }

    .markdown-content table {
        font-size: 12.5px;
    }

    .markdown-content table th, .markdown-content table td {
        padding: 5px 10px;
    }
}

@media (min-width: 768px) {
    .markdown-content {
        font-size: 14px;
    }

    .markdown-content p {
        margin-bottom: 10px;
    }

    .markdown-content h1, .markdown-content h2, .markdown-content h3,
    .markdown-content h4, .markdown-content h5, .markdown-content h6 {
        margin-top: 16px;
        margin-bottom: 10px;
        line-height: 1.4;
    }

    .markdown-content h1 { font-size: 1.6em; }
    .markdown-content h2 { font-size: 1.4em; }
    .markdown-content h3 { font-size: 1.2em; }
    .markdown-content h4 { font-size: 1.1em; }

    .markdown-content ul, .markdown-content ol {
        margin-bottom: 10px;
        padding-left: 20px;
    }

    .markdown-content blockquote {
        padding: 0 10px;
        border-left: 3px solid #dfe2e5;
        margin: 10px 0;
    }

    .markdown-content pre {
        padding: 10px;
        margin-bottom: 10px;
        font-size: 13px;
    }

    .markdown-content table {
        font-size: 13px;
        margin-bottom: 10px;
    }

    .markdown-content table th, .markdown-content table td {
        padding: 6px 13px;
    }

    .markdown-content hr {
        margin: 15px 0;
    }
}
