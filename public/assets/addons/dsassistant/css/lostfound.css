.item-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
    transition: var(--transition);
    background-color: white;
    border: none;
}

.item-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.item-img {
    height: 220px;
    object-fit: cover;
    transition: var(--transition);
}

.item-card:hover .item-img {
    transform: scale(1.05);
}

.item-info {
    padding: 1.5rem;
}

.item-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--dark-color);
}

.item-desc {
    color: var(--gray-color);
    margin-bottom: 1rem;
    height: 4.8em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.item-meta {
    color: var(--gray-color);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.item-meta i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.item-meta div {
    margin-bottom: 0.5rem;
}

.badge {
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 50px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-lost {
    background-color: var(--danger-color);
    color: white;
}

.badge-found {
    background-color: var(--success-color);
    color: white;
}

.nav-tabs {
    border: none;
    background-color: white;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    color: var(--gray-color);
    transition: var(--transition);
}

.nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    color: white !important;
    font-weight: 500;
}

.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}
