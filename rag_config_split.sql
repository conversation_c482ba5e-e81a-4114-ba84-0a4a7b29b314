-- 将rag_config拆分为独立的配置项

-- 删除原有的rag_config配置项（如果存在）
DELETE FROM `fa_ds_content_config` WHERE `name` = 'rag_config';

-- 插入拆分后的独立配置项
INSERT INTO `fa_ds_content_config` (`name`, `title`, `type`, `group`, `visible`, `content`, `value`, `rule`, `msg`, `tip`, `extend`, `createtime`, `updatetime`) VALUES
('rag_enabled', 'RAG模式启用', 'switch', 'RAG模式设置', '', '[]', '1', '', '请选择是否启用RAG模式', '是否启用检索增强生成模式', '', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('rag_top_k', '检索结果数量', 'number', 'RAG模式设置', '', '[]', '3', 'required', '请填写检索结果数量', '从本地知识库检索的结果数量', '', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('rag_min_score', '最低得分要求', 'number', 'RAG模式设置', '', '[]', '0.3', 'required', '请填写最低得分要求', '本地知识库结果的最低得分要求，低于此分数的结果将被过滤', '', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('rag_context_format', '上下文格式', 'text', 'RAG模式设置', '', '[]', '问题: {question}\n回答: {answer}\n\n', '', '请填写上下文格式', '上下文格式，支持{question}和{answer}占位符', '', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('rag_prompt_template', '提示模板', 'text', 'RAG模式设置', '', '[]', '以下是关于用户问题的相关信息，请参考这些信息回答问题，但不要直接复制这些内容，而是用自然、流畅的语言回答：\n\n{context}', '', '请填写提示模板', '提供给大模型的提示模板，支持{context}占位符', '', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
