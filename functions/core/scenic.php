<?php
/**
 * 显示景点详情函数定义
 * 
 * 用于显示景点的详细信息
 */
return [
    'name' => 'showScenicSpotDetails',
    'description' => '显示景点详细信息',
    'parameters' => [
        'type' => 'object',
        'properties' => [
            'spotId' => [
                'type' => 'string',
                'description' => '景点ID'
            ],
            'spotName' => [
                'type' => 'string',
                'description' => '景点名称'
            ]
        ],
        'required' => ['spotId']
    ],
    'enabled' => true,  // 默认启用状态
    'priority' => 200   // 优先级，数字越小优先级越高
];
