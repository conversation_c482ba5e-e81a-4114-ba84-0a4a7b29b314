<?php
/**
 * 显示地图位置函数定义
 *
 * 用于在地图上显示景点位置
 */
return [
    'name' => 'showOnMap',
    'description' => '当用户询问位置、地点、怎么走、在哪里、地图等相关问题时，使用此函数显示景点或地点在地图上的位置，并提供导航功能',
    'parameters' => [
        'type' => 'object',
        'properties' => [
            'spotId' => [
                'type' => 'string',
                'description' => '景点ID，如果知道的话可以提供，否则可以留空'
            ],
            'spotName' => [
                'type' => 'string',
                'description' => '景点或地点的名称，例如"天空栈道"、"故宫"、"颐和园"等，必须提供'
            ],
            'latitude' => [
                'type' => 'number',
                'description' => '纬度坐标，如果知道的话可以提供，否则可以留空'
            ],
            'longitude' => [
                'type' => 'number',
                'description' => '经度坐标，如果知道的话可以提供，否则可以留空'
            ]
        ],
        'required' => ['spotName']
    ],
    'enabled' => true,  // 默认启用状态
    'priority' => 100   // 优先级，数字越小优先级越高
];
