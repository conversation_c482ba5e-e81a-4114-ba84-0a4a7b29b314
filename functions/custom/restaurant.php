<?php
/**
 * 显示餐厅信息函数定义
 * 
 * 用于显示景区附近的餐厅信息
 */
return [
    'name' => 'showRestaurantInfo',
    'description' => '显示景区附近的餐厅信息',
    'parameters' => [
        'type' => 'object',
        'properties' => [
            'restaurantId' => [
                'type' => 'string',
                'description' => '餐厅ID'
            ],
            'restaurantName' => [
                'type' => 'string',
                'description' => '餐厅名称'
            ],
            'cuisine' => [
                'type' => 'string',
                'description' => '菜系类型'
            ],
            'distance' => [
                'type' => 'number',
                'description' => '与当前景点的距离（米）'
            ]
        ],
        'required' => ['restaurantName']
    ],
    'enabled' => true,  // 默认启用状态
    'priority' => 300   // 优先级，数字越小优先级越高
];
