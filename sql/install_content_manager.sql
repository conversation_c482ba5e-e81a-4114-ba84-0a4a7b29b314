-- 创建配置表
CREATE TABLE IF NOT EXISTS `fa_ds_content_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '变量名',
  `group` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '分组',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '类型',
  `visible` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '可见条件',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '变量值',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '变量字典数据',
  `rule` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '扩展属性',
  `setting` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '配置',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='参数配置';

-- 创建页面内容表
CREATE TABLE IF NOT EXISTS `fa_ds_page_content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page` varchar(50) NOT NULL COMMENT '页面标识',
  `section` varchar(50) NOT NULL COMMENT '页面区块',
  `key` varchar(50) NOT NULL COMMENT '内容键名',
  `value` text COMMENT '内容值',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `page_section_key` (`page`,`section`,`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面内容';

-- 插入默认全局配置
INSERT INTO `fa_ds_content_config` (`name`, `title`, `value`, `type`, `group`, `tip`) VALUES

('site_name', '网站名称','景区智能助理', 'string', 'basic', '网站名称'),
('site_description', '网站描述','基于国内大模型技术，为游客提供全方位智能服务', 'string', 'basic', '网站描述'),
('copyright','版权信息', '© 2024 景区智能助理 - 基于国内大模型技术', 'string', 'basic', '版权信息'),
('contact_address', '联系地址','景区管理中心', 'string', 'contact', '联系地址'),
('contact_phone', '联系电话','************', 'string', 'contact', '联系电话'),
('contact_email', '联系邮箱','<EMAIL>', 'string', 'contact', '联系邮箱');

-- 插入首页内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('index', 'header', 'title', '景区智能助理', '首页标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'header', 'subtitle', '基于国内大模型技术，为游客提供全方位智能服务', '首页副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'header', 'button_text', '立即咨询', '咨询按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'title', '核心功能', '功能区标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature1_title', '智能问答', '功能1标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature1_desc', '基于国内大模型技术，解答游客各类问题，提供精准信息服务', '功能1描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature2_title', '实时预警', '功能2标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature2_desc', '及时推送人流高峰、极端天气等预警信息，保障游客安全', '功能2描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature3_title', '电子导览', '功能3标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature3_desc', '提供景区电子地图导航，让游客轻松找到目的地', '功能3描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature4_title', '失物招领', '功能4标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'features', 'feature4_desc', '提供失物登记与查询入口，帮助游客找回丢失物品', '功能4描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'title', '多种使用方式', '使用方式区标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage1_title', '微信公众号', '使用方式1标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage1_desc', '关注公众号，随时随地获取景区信息', '使用方式1描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage2_title', '小程序', '使用方式2标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage2_desc', '扫码进入小程序，享受更丰富功能', '使用方式2描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage3_title', '官方网站', '使用方式3标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('index', 'usage', 'usage3_desc', '访问官网，体验全功能服务', '使用方式3描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入智能问答页面内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('chat', 'header', 'title', '智能问答', '页面标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'header', 'subtitle', '有任何问题，随时向我提问', '页面副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'greeting', '您好！我是景区智能助理，很高兴为您服务。', '欢迎语', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'intro', '您可以向我咨询以下内容：', '介绍文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'topic1', '景区开放时间和门票信息', '咨询主题1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'topic2', '景点介绍和游览路线推荐', '咨询主题2', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'topic3', '交通指南和周边住宿', '咨询主题3', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'topic4', '景区活动和特色服务', '咨询主题4', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'welcome', 'closing', '请问有什么可以帮助您的？', '结束语', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'input', 'placeholder', '请输入您的问题...', '输入框提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'input', 'button_text', '发送', '发送按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'status', 'thinking', '助理正在思考', '思考状态文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'status', 'error', '抱歉，出现了一些问题', '错误消息', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('chat', 'status', 'network_error', '网络连接出现问题，请稍后再试', '网络错误消息', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入景点导览页面内容
INSERT INTO `fa_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('guide', 'header', 'title', '景点导览', '页面标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'header', 'subtitle', '探索景区的精彩景点，规划您的完美旅程', '页面副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'map', 'title', '景区地图', '地图标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'map', 'description', '点击地图上的标记查看景点详情，规划您的游览路线', '地图描述', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'map', 'scroll_hint', '向下滑动查看热门景点', '滚动提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spots', 'title', '热门景点', '景点列表标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spots', 'loading', '正在加载景点信息...', '加载提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spots', 'empty', '暂无景点信息', '空数据提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spots', 'error', '加载景点信息失败，请刷新页面重试', '错误提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spot_card', 'badge', '热门景点', '徽章文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spot_card', 'detail_button', '查看详情', '详情按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'spot_card', 'nav_button', '导航', '导航按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'title', '景点详情', '模态框标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'basic_info', '基本信息', '基本信息标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'tips', '游玩提示', '游玩提示标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'address', '地址', '地址标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'hours', '开放时间', '开放时间标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'price', '票价', '票价标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'map_button', '查看地图', '地图按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('guide', 'modal', 'close_button', '关闭', '关闭按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入失物招领页面内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('lostfound', 'header', 'title', '失物招领', '页面标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'header', 'subtitle', '丢失或拾获物品？在这里登记或查询', '页面副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'header', 'submit_button', '登记物品', '登记按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'header', 'search_button', '搜索物品', '搜索按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'tabs', 'all', '全部', '全部标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'tabs', 'lost', '寻物启事', '寻物标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'tabs', 'found', '招领启事', '招领标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'list', 'loading', '正在加载物品信息...', '加载提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'list', 'empty', '暂无数据', '空数据提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'pagination', 'prev', '上一页', '上一页文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'pagination', 'next', '下一页', '下一页文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'card', 'lost_badge', '寻物启事', '寻物徽章文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'card', 'found_badge', '招领启事', '招领徽章文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'card', 'detail_button', '查看详情', '详情按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'title', '登记物品', '登记模态框标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'type_label', '物品类型', '类型标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'title_label', '标题', '标题标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'desc_label', '描述', '描述标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'category_label', '物品类别', '类别标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'location_label', '地点', '地点标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'contact_label', '联系人', '联系人标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'phone_label', '联系电话', '电话标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'image_label', '上传图片', '图片标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'submit_button', '提交', '提交按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'submit_modal', 'cancel_button', '取消', '取消按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'title', '搜索物品', '搜索模态框标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'keyword_label', '关键词', '关键词标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'type_label', '物品类型', '类型标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'category_label', '物品类别', '类别标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'search_button', '搜索', '搜索按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('lostfound', 'search_modal', 'cancel_button', '取消', '取消按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入预警信息页面内容
INSERT INTO `fa_ds_page_content` (`page`, `section`, `key`, `value`, `description`, `createtime`, `updatetime`) VALUES
('warning', 'header', 'title', '预警信息', '页面标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'header', 'subtitle', '及时了解景区最新预警信息，确保您的游览安全', '页面副标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'list', 'loading', '正在加载预警信息...', '加载提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'list', 'empty', '当前没有预警信息', '空数据提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'list', 'error', '加载失败', '错误提示', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'crowd_type', '人流预警', '人流预警文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'weather_type', '天气预警', '天气预警文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'traffic_type', '交通预警', '交通预警文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'other_type', '其他预警', '其他预警文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'info_level', '提示', '提示级别文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'warning_level', '警告', '警告级别文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'danger_level', '危险', '危险级别文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'publish_time', '发布时间', '发布时间标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'valid_period', '有效期', '有效期标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'publisher', '发布人', '发布人标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'card', 'detail_button', '查看详情', '详情按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'title', '预警详情', '模态框标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'basic_info', '基本信息', '基本信息标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'publish_info', '发布信息', '发布信息标题', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'valid_period', '有效期', '有效期标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'publish_time', '发布时间', '发布时间标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'publisher', '发布人', '发布人标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'contact_phone', '联系电话', '联系电话标签', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('warning', 'modal', 'close_button', '关闭', '关闭按钮文本', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
