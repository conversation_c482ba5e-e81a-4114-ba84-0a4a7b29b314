-- ----------------------------
-- 会话状态表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_session_state` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `session_id` varchar(50) NOT NULL COMMENT '会话ID',
  `user_id` varchar(50) DEFAULT '' COMMENT '用户ID',
  `context` text COMMENT '对话上下文',
  `last_question` text COMMENT '最后一个问题',
  `last_answer` text COMMENT '最后一个回答',
  `platform` enum('wechat','miniapp','web') DEFAULT 'web' COMMENT '平台:wechat=微信,miniapp=小程序,web=网页',
  `status` enum('active','closed') DEFAULT 'active' COMMENT '状态:active=活动,closed=关闭',
  `expire_time` int(10) unsigned DEFAULT NULL COMMENT '过期时间',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  KEY `expire_time` (`expire_time`),
  KEY `status` (`status`),
  KEY `platform` (`platform`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='会话状态表';
