CREATE TABLE IF NOT EXISTS `__PREFIX__ds_vector_embeddings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `knowledge_id` int(11) NOT NULL COMMENT '知识库ID',
  `vector` longtext NOT NULL COMMENT '向量数据',
  `createtime` int(11) NOT NULL COMMENT '创建时间',
  `updatetime` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `knowledge_id` (`knowledge_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='向量嵌入表';
