-- ----------------------------
-- LLM模型配置表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `fa_ds_llm_models` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `model_id` varchar(50) NOT NULL COMMENT '模型ID',
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `provider` varchar(50) NOT NULL COMMENT '服务商标识',
  `provider_name` varchar(100) NOT NULL COMMENT '服务商名称',
  `base_url` varchar(255) NOT NULL COMMENT 'API基础URL',
  `api_key` varchar(255) DEFAULT '' COMMENT 'API密钥',
  `supports_functions` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持函数调用',
  `max_tokens` int(11) NOT NULL DEFAULT 4096 COMMENT '最大token数',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认模型',
  `weight` int(10) unsigned DEFAULT 0 COMMENT '排序权重',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `model_id` (`model_id`),
  KEY `provider` (`provider`),
  KEY `enabled` (`enabled`),
  KEY `is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LLM模型配置表';

-- 插入默认模型数据
INSERT INTO `fa_ds_llm_models` (`model_id`, `name`, `provider`, `provider_name`, `base_url`, `api_key`, `supports_functions`, `max_tokens`, `enabled`, `is_default`, `weight`, `createtime`, `updatetime`) VALUES
('deepseek-reasoner', 'DeepSeek Reasoner', 'deepseek', 'DeepSeek', 'https://api.deepseek.com/v1', '', 1, 4096, 1, 1, 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('deepseek-chat', 'DeepSeek Chat', 'deepseek', 'DeepSeek', 'https://api.deepseek.com/v1', '', 0, 4096, 1, 0, 90, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
