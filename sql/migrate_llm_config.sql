-- ----------------------------
-- 将LLM配置从ContentConfig表迁移到ds_llm_models表
-- ----------------------------

-- 从ContentConfig表中获取DeepSeek API密钥
SET @deepseek_api_key = (SELECT `value` FROM `fa_ds_content_config` WHERE `name` = 'deepseek_api_key');

-- 更新已插入的模型记录的API密钥
UPDATE `fa_ds_llm_models` SET `api_key` = @deepseek_api_key WHERE `provider` = 'deepseek';

-- 如果存在llm_config配置项，则解析并迁移到新表
SET @llm_config = (SELECT `value` FROM `fa_ds_content_config` WHERE `name` = 'llm_config');

-- 注意：实际迁移需要在PHP代码中完成，因为SQL无法直接解析JSON并动态生成INSERT语句
-- 这里只是一个占位符，实际迁移逻辑将在PHP代码中实现
