CREATE TABLE `fa_ds_warning_push_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `warning_id` int(11) NOT NULL COMMENT '预警ID',
  `total_users` int(11) DEFAULT '0' COMMENT '总用户数',
  `processed_users` int(11) DEFAULT '0' COMMENT '已处理用户数',
  `success_users` int(11) DEFAULT '0' COMMENT '成功用户数',
  `fail_users` int(11) DEFAULT '0' COMMENT '失败用户数',
  `status` enum('pending','processing','completed','failed') DEFAULT 'pending' COMMENT '状态',
  `last_user_id` int(11) DEFAULT '0' COMMENT '上次处理的用户ID',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `warning_id` (`warning_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警推送任务';

CREATE TABLE `fa_ds_warning_push_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `warning_id` int(11) NOT NULL COMMENT '预警ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信OpenID',
  `status` enum('pending','success','fail') DEFAULT 'pending' COMMENT '状态',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `retry_count` int(11) DEFAULT '0' COMMENT '重试次数',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `warning_id` (`warning_id`),
  KEY `openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警推送记录';