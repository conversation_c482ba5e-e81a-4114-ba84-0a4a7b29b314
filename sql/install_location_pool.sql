CREATE TABLE `fa_ds_user_location_pool` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) NOT NULL COMMENT '微信OpenID',
  `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `in_scenic` tinyint(1) DEFAULT '1' COMMENT '是否在景区内',
  `last_report_time` int(10) DEFAULT NULL COMMENT '最后上报时间',
  `expire_time` int(10) DEFAULT NULL COMMENT '过期时间',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`),
  KEY `expire_time` (`expire_time`),
  KEY `in_scenic` (`in_scenic`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户位置池';

CREATE TABLE `fa_ds_scenic_area_boundary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '区域名称',
  `type` enum('circle','polygon','rectangle') DEFAULT 'circle' COMMENT '区域类型',
  `center_lat` decimal(10,7) DEFAULT NULL COMMENT '中心点纬度',
  `center_lng` decimal(10,7) DEFAULT NULL COMMENT '中心点经度',
  `radius` int(11) DEFAULT NULL COMMENT '半径(米)',
  `points` text COMMENT '多边形顶点坐标',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='景区地理边界';
