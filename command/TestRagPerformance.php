<?php

namespace addons\dsassistant\command;

use app\common\model\DsAssistant;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Log;

class TestRagPerformance extends Command
{
    protected function configure()
    {
        $this->setName('dsassistant:test-rag-performance')
            ->setDescription('测试RAG模式性能');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始测试RAG模式性能...");
        
        // 测试问题
        $testQuestions = [
            "景区门票多少钱？",
            "景区开放时间是几点到几点？",
            "景区有哪些主要景点？",
            "如何到达景区？",
            "景区内有餐厅吗？"
        ];
        
        $dsAssistant = new DsAssistant();
        
        foreach ($testQuestions as $index => $question) {
            $output->writeln("\n测试问题 " . ($index + 1) . ": " . $question);
            
            // 记录开始时间
            $startTime = microtime(true);
            
            // 处理问题
            try {
                $answer = $dsAssistant->handleQuestion($question, 'test_user', 'test_session', 'cli');
                
                // 计算耗时
                $endTime = microtime(true);
                $duration = round(($endTime - $startTime) * 1000, 2); // 毫秒
                
                $output->writeln("回答: " . substr($answer, 0, 100) . "...");
                $output->writeln("处理时间: " . $duration . "ms");
                
                // 记录到日志
                Log::record("问题: {$question}, 处理时间: {$duration}ms", 'info');
            } catch (\Exception $e) {
                $output->writeln("错误: " . $e->getMessage());
                Log::record("处理问题出错: " . $e->getMessage(), 'error');
            }
        }
        
        $output->writeln("\n测试完成！");
    }
}
