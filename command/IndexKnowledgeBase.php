<?php

namespace addons\dsassistant\command;

use addons\dsassistant\library\VectorSearchFactory;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\console\input\Option;
use think\Db;

/**
 * 知识库向量索引命令
 *
 * 该命令用于为知识库条目生成向量表示，并更新知识库表中的向量状态字段。
 * 支持两种知识库添加场景：
 * 1. 单条表单添加：在添加时可以同步生成向量，并更新向量状态字段
 * 2. 批量导入：可以使用本命令批量处理未生成向量的知识条目
 *
 * 使用方法：
 * 1. 处理所有未生成向量的知识条目：
 *    php think dsassistant:index
 *
 * 2. 处理所有知识条目（无论是否已生成向量）：
 *    php think dsassistant:index --all
 *
 * 3. 限制处理的条目数量：
 *    php think dsassistant:index --limit=100
 *
 * 4. 处理单个知识条目：
 *    php think dsassistant:index --id=123
 *
 * 5. 组合使用：
 *    php think dsassistant:index --all --limit=50
 *
 * 注意事项：
 * - 默认情况下，只处理未生成向量的知识条目（has_vector=0）
 * - 生成向量后，会自动更新知识库表中的向量状态字段（has_vector=1, vector_time=当前时间）
 * - 该命令适合在后台任务或计划任务中运行，处理批量导入的知识条目
 *
 * <AUTHOR> <<EMAIL>>
 */
class IndexKnowledgeBase extends Command
{
    /**
     * 配置命令
     *
     * 设置命令名称、描述和可用选项
     * - all: 处理所有知识条目，无论是否已生成向量
     * - limit: 限制处理的条目数量
     * - id: 处理单个知识条目
     */
    protected function configure()
    {
        $this->setName('dsassistant:index')
            ->setDescription('Index knowledge base for vector search')
            ->addOption('all', 'a', Option::VALUE_NONE, 'Index all knowledge items regardless of vector status')
            ->addOption('limit', 'l', Option::VALUE_REQUIRED, 'Limit the number of items to process', 0)
            ->addOption('id', null, Option::VALUE_REQUIRED, 'Index a specific knowledge item by ID')
            ->setDescription('知识库向量索引命令');;
    }

    /**
     * 执行命令
     *
     * 根据命令选项执行相应的操作：
     * 1. 如果指定了id，则处理单个知识条目
     * 2. 否则，根据all和limit选项处理批量知识条目
     *
     * @param Input $input 命令输入对象
     * @param Output $output 命令输出对象
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        // 获取命令选项
        $all = $input->getOption('all');        // 是否处理所有知识条目
        $limit = (int)$input->getOption('limit'); // 限制处理的条目数量
        $id = $input->getOption('id');          // 指定处理的知识条目ID

        // 处理单个知识条目的情况
        if ($id) {
            // 输出开始处理的信息
            $output->writeln("Indexing knowledge item with ID: $id");

            // 查询知识条目是否存在
            $item = Db::name('ds_knowledge')->where('id', $id)->find();

            // 如果知识条目不存在，输出错误信息并返回
            if (!$item) {
                $output->error("Knowledge item with ID $id not found.");
                return;
            }

            // 获取向量搜索实例并为单个知识条目生成向量
            $vectorSearch = VectorSearchFactory::getInstance();
            $result = $vectorSearch->indexSingleItem($id);

            // 根据处理结果输出相应的信息
            if ($result) {
                $output->info("Successfully indexed knowledge item with ID: $id");
            } else {
                $output->error("Failed to index knowledge item with ID: $id");
            }

            return;
        }

        // 处理批量知识条目的情况
        $output->writeln('Starting to index knowledge base...');

        // 构建查询条件，默认只处理状态为正常的知识条目
        $query = Db::name('ds_knowledge')->where('status', 'normal');

        // 如果不是处理所有条目，则只处理未生成向量的条目（has_vector=0）
        if (!$all) {
            $query->where('has_vector', 0);
            $output->info("Only processing knowledge items without vectors (has_vector=0)");
        } else {
            $output->info("Processing all knowledge items regardless of vector status");
        }

        // 如果有限制数量，则应用限制
        if ($limit > 0) {
            $query->limit($limit);
            $output->info("Limiting to $limit items");
        }

        // 获取需要处理的知识条目
        $knowledgeList = $query->select();
        $total = count($knowledgeList);

        // 如果没有需要处理的知识条目，输出信息并返回
        if ($total == 0) {
            $output->info("No knowledge items need to be indexed.");
            return;
        }

        // 输出找到的知识条目数量
        $output->writeln("Found $total knowledge items to index.");

        // 获取向量搜索实例并批量处理知识条目
        // 注意：indexKnowledgeBase方法会自动更新知识库表中的向量状态字段（has_vector=1, vector_time=当前时间）
        $vectorSearch = VectorSearchFactory::getInstance();
        $count = $vectorSearch->indexKnowledgeBase($knowledgeList);

        // 输出处理结果
        $output->writeln("Successfully indexed $count knowledge items.");

        // 如果处理的数量与找到的数量不一致，输出警告信息
        if ($count < $total) {
            $output->warning("Note: " . ($total - $count) . " items failed to be indexed. Check logs for details.");
        }
    }
}
