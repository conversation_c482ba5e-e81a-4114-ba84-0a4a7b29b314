<?php

namespace addons\dsassistant\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 测试数据库命令
 * 用于检查数据库表结构
 */
class TestDb extends Command
{
    protected function configure()
    {
        $this->setName('dsassistant:test-db')
            ->setDescription('测试数据库表结构');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("测试数据库命令开始执行");

        // 检查ds_knowledge表结构
        $this->checkTableStructure('ds_knowledge', $output);

        // 检查ds_knowledge_vectors表结构
        $this->checkTableStructure('ds_knowledge_vectors', $output);

        // 检查百花园相关数据
        $this->checkBaihuayuanData($output);

        $output->writeln("测试数据库命令执行完成");
    }

    /**
     * 检查表结构
     */
    protected function checkTableStructure($tableName, Output $output)
    {
        $output->writeln("\n===== 检查{$tableName}表结构 =====");

        try {
            // 获取表结构
            $fields = Db::query("SHOW COLUMNS FROM {$tableName}");

            $output->writeln("{$tableName}表字段列表:");
            foreach ($fields as $field) {
                $output->writeln("字段名: {$field['Field']}, 类型: {$field['Type']}, 允许NULL: {$field['Null']}, 默认值: {$field['Default']}");
            }
        } catch (\Exception $e) {
            $output->writeln("<error>检查{$tableName}表结构失败: " . $e->getMessage() . "</error>");
        }
    }

    /**
     * 检查百花园相关数据
     */
    protected function checkBaihuayuanData(Output $output)
    {
        $output->writeln("\n===== 检查百花园相关数据 =====");

        try {
            // 检查ds_knowledge表中的百花园数据
            $knowledgeList = Db::name('ds_knowledge')
                ->where('title', 'like', '%百花园%')
                ->whereOr('content', 'like', '%百花园%')
                ->select();

            $output->writeln("知识库中包含'百花园'的数据数量: " . count($knowledgeList));

            if (!empty($knowledgeList)) {
                $output->writeln("知识库中的百花园数据:");
                foreach ($knowledgeList as $item) {
                    $output->writeln("ID: {$item['id']}, 标题: {$item['title']}");
                    $output->writeln("  内容: " . mb_substr($item['content'], 0, 50) . "...");
                    $output->writeln("  标签: " . ($item['tags'] ?? '无标签'));
                    $output->writeln("  分类ID: " . ($item['category_id'] ?? '无分类ID'));
                    $output->writeln("  向量状态: " . ($item['has_vector'] ?? '未知'));
                    $output->writeln("  创建时间: " . date('Y-m-d H:i:s', $item['createtime']));
                    $output->writeln("");
                }

                // 检查这些数据在向量数据库中是否存在
                $output->writeln("\n检查这些数据在向量数据库中是否存在:");

                foreach ($knowledgeList as $item) {
                    $vectorData = Db::name('ds_knowledge_vectors')->where('id', $item['id'])->find();
                    if ($vectorData) {
                        $output->writeln("ID: {$item['id']} 在向量数据库中存在");

                        // 检查向量数据的元数据
                        $metadata = !empty($vectorData['metadata']) ? json_decode($vectorData['metadata'], true) : [];
                        $output->writeln("  元数据: " . json_encode($metadata, JSON_UNESCAPED_UNICODE));
                    } else {
                        $output->writeln("<warning>ID: {$item['id']} 在向量数据库中不存在</warning>");
                    }
                }
            } else {
                $output->writeln("<warning>知识库中没有包含'百花园'的数据</warning>");
            }
        } catch (\Exception $e) {
            $output->writeln("<error>检查百花园数据失败: " . $e->getMessage() . "</error>");
        }
    }
}
