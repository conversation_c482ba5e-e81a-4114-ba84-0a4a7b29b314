<?php

namespace addons\dsassistant\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Exception;
use addons\dsassistant\library\SessionManager;

/**
 * 清理过期会话
 */
class CleanExpiredSessions extends Command
{
    protected function configure()
    {
        $this->setName('dsassistant:clean-expired-sessions')
            ->setDescription('清理过期会话');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始清理过期会话...");
        
        try {
            $count = SessionManager::cleanExpiredSessions();
            
            $output->writeln("清理完成，共清理 {$count} 个过期会话");
        } catch (Exception $e) {
            $output->writeln("<error>清理过期会话失败: " . $e->getMessage() . "</error>");
        }
    }
}
