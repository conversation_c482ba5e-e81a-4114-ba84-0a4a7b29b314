<?php

namespace addons\dsassistant\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use addons\dsassistant\library\vectordb\VectorDBFactory;
use addons\dsassistant\library\EnhancedVectorSearch;
use addons\dsassistant\library\CacheManager;
use addons\dsassistant\library\embedding\EmbeddingFactory;

/**
 * 测试命令
 * 用于检查向量数据库和搜索功能
 */
class Test extends Command
{
    protected function configure()
    {
        $this->setName('dsassistant:test')
            ->setDescription('测试命令');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("测试命令开始执行");

        // 执行测试方法
        $this->testVectorData($output);
        $this->testSearchWithoutFilter($output);
        $this->testCategoryRules($output);
        $this->testBaihuayuanData($output);

        $output->writeln("测试命令执行完成");
    }

    /**
     * 测试向量数据库中的数据
     */
    protected function testVectorData(Output $output)
    {
        $output->writeln("\n===== 测试向量数据库中的数据 =====");

        try {
            // 直接使用数据库查询，因为MySQLVectorDB没有listVectors方法
            $result = Db::name('ds_knowledge_vectors')->limit(10)->select();

            $output->writeln("向量数据库中共有 " . Db::name('ds_knowledge_vectors')->count() . " 条数据");

            if (!empty($result)) {
                $output->writeln("前10条数据示例:");
                foreach ($result as $index => $item) {
                    $metadata = !empty($item['metadata']) ? json_decode($item['metadata'], true) : [];
                    $categoryId = isset($metadata['classification']['category_id']) ? $metadata['classification']['category_id'] : '无分类ID';
                    $tags = isset($metadata['classification']['tags']) ? implode(',', $metadata['classification']['tags']) : '无标签';
                    $title = isset($metadata['title']) ? $metadata['title'] : '无标题';

                    $output->writeln("[{$index}] ID: {$item['id']}, 标题: {$title}, 分类ID: {$categoryId}, 标签: {$tags}");
                }
            } else {
                $output->writeln("向量数据库中没有数据");
            }
        } catch (\Exception $e) {
            $output->writeln("<error>测试向量数据库失败: " . $e->getMessage() . "</error>");
        }
    }

    /**
     * 测试不使用过滤条件的搜索
     */
    protected function testSearchWithoutFilter(Output $output)
    {
        $output->writeln("\n===== 测试不使用过滤条件的搜索 =====");

        try {
            $embedding = EmbeddingFactory::getInstance();

            // 测试查询
            $query = "百花园";
            $output->writeln("测试查询: {$query}");

            // 生成向量
            $vector = $embedding->getEmbedding($query);
            $output->writeln("生成向量成功，维度: " . count($vector));

            // 不使用过滤条件搜索，直接使用数据库查询
            $output->writeln("执行不使用过滤条件的搜索...");

            // 获取所有向量
            $vectors = Db::name('ds_knowledge_vectors')->select();
            $output->writeln("从数据库获取到 " . count($vectors) . " 条向量数据");

            // 计算相似度
            $results = [];
            foreach ($vectors as $item) {
                $vectorData = json_decode($item['vector'], true);
                if (!is_array($vectorData)) {
                    continue;
                }

                // 计算余弦相似度
                $dotProduct = 0;
                $magnitudeA = 0;
                $magnitudeB = 0;

                for ($i = 0; $i < count($vector); $i++) {
                    $dotProduct += $vector[$i] * $vectorData[$i];
                    $magnitudeA += $vector[$i] * $vector[$i];
                    $magnitudeB += $vectorData[$i] * $vectorData[$i];
                }

                $magnitudeA = sqrt($magnitudeA);
                $magnitudeB = sqrt($magnitudeB);

                $similarity = 0;
                if ($magnitudeA > 0 && $magnitudeB > 0) {
                    $similarity = $dotProduct / ($magnitudeA * $magnitudeB);
                }

                $results[] = [
                    'id' => $item['id'],
                    'similarity' => $similarity
                ];
            }

            // 按相似度排序
            usort($results, function($a, $b) {
                return $b['similarity'] <=> $a['similarity'];
            });

            // 返回前10个结果
            $searchResults = array_slice($results, 0, 10);

            $output->writeln("不使用过滤条件搜索结果数量: " . count($searchResults));

            if (!empty($searchResults)) {
                $output->writeln("搜索结果:");
                foreach ($searchResults as $index => $result) {
                    $output->writeln("[{$index}] ID: {$result['id']}, 相似度: {$result['similarity']}");

                    // 获取知识库详细信息
                    $knowledge = Db::name('ds_knowledge')->where('id', $result['id'])->find();
                    if ($knowledge) {
                        $output->writeln("    标题: {$knowledge['title']}");
                        $output->writeln("    分类: {$knowledge['category']}");
                        $output->writeln("    标签: {$knowledge['tags']}");
                    } else {
                        $output->writeln("    知识库中不存在此ID");
                    }
                }
            } else {
                $output->writeln("搜索无结果");
            }
        } catch (\Exception $e) {
            $output->writeln("<error>测试搜索失败: " . $e->getMessage() . "</error>");
        }
    }

    /**
     * 测试分类规则和关键词映射
     */
    protected function testCategoryRules(Output $output)
    {
        $output->writeln("\n===== 测试分类规则和关键词映射 =====");

        try {
            // 获取分类规则
            $categoryRules = CacheManager::getCategoryRule();
            $output->writeln("分类规则数量: " . count($categoryRules));

            if (!empty($categoryRules)) {
                $output->writeln("分类规则列表:");
                foreach ($categoryRules as $category => $rule) {
                    $patterns = isset($rule['patterns']) ? implode(', ', $rule['patterns']) : '无模式';
                    $boost = isset($rule['boost']) ? $rule['boost'] : '0';
                    $output->writeln("分类: {$category}, 提升因子: {$boost}, 模式: {$patterns}");
                }
            } else {
                $output->writeln("没有配置分类规则");
            }

            // 获取关键词映射
            $keywordMap = CacheManager::getKeywordMap();
            $output->writeln("\n关键词映射数量: " . count($keywordMap));

            if (!empty($keywordMap)) {
                $output->writeln("关键词映射列表:");
                foreach ($keywordMap as $keyword => $data) {
                    $relatedKeywords = isset($data['related_keywords']) ? implode(', ', $data['related_keywords']) : '无相关关键词';
                    $output->writeln("关键词: {$keyword}, 相关关键词: {$relatedKeywords}");
                }
            } else {
                $output->writeln("没有配置关键词映射");
            }

            // 测试"百花园"的分类识别
            $testQuery = "百花园";
            $output->writeln("\n测试查询 '{$testQuery}' 的分类识别:");

            $enhancedSearch = new EnhancedVectorSearch();
            $reflection = new \ReflectionClass($enhancedSearch);
            $method = $reflection->getMethod('identifyTitleCategory');
            $method->setAccessible(true);

            $categories = $method->invoke($enhancedSearch, $testQuery);
            if (!empty($categories)) {
                $output->writeln("识别到的分类: " . implode(', ', $categories));
            } else {
                $output->writeln("未识别到分类");
            }

            // 测试"百花园"的关键词提取
            $output->writeln("\n测试查询 '{$testQuery}' 的关键词提取:");

            $method = $reflection->getMethod('extractKeywords');
            $method->setAccessible(true);

            $keywords = $method->invoke($enhancedSearch, $testQuery);
            if (!empty($keywords)) {
                $output->writeln("提取到的关键词: " . implode(', ', $keywords));
            } else {
                $output->writeln("未提取到关键词");
            }
        } catch (\Exception $e) {
            $output->writeln($e->getTraceAsString());
            $output->writeln("<error>测试分类规则失败: " . $e->getMessage() . "</error>");
        }
    }

    /**
     * 专门测试百花园相关数据
     */
    protected function testBaihuayuanData(Output $output)
    {
        $output->writeln("\n===== 测试百花园相关数据 =====");

        try {
            // 1. 检查知识库中的百花园数据
            $knowledgeList = Db::name('ds_knowledge')
                ->where('title', 'like', '%百花园%')
                ->whereOr('content', 'like', '%百花园%')
                ->whereOr('tags', 'like', '%百花园%')
                ->select();

            $output->writeln("知识库中包含'百花园'的数据数量: " . count($knowledgeList));

            if (!empty($knowledgeList)) {
                $output->writeln("知识库中的百花园数据:");
                foreach ($knowledgeList as $item) {
                    $output->writeln("ID: {$item['id']}, 标题: {$item['title']}, 分类ID: {$item['category_id']}, 标签: {$item['tags']}, 向量状态: {$item['has_vector']}");
                }

                // 2. 检查这些数据在向量数据库中是否存在
                $vectorDB = VectorDBFactory::getInstance();
                $output->writeln("\n检查这些数据在向量数据库中是否存在:");

                foreach ($knowledgeList as $item) {
                    try {
                        $vectorData = $vectorDB->getVector('ds_knowledge_vectors', $item['id']);
                        if ($vectorData) {
                            $output->writeln("ID: {$item['id']} 在向量数据库中存在");

                            // 检查向量数据的元数据
                            $metadata = !empty($vectorData['metadata']) && is_string($vectorData['metadata']) ? json_decode($vectorData['metadata'], true) : $vectorData['metadata'] ?? [];
                            $categoryId = isset($metadata['classification']['category_id']) ? $metadata['classification']['category_id'] : '无分类ID';
                            $tags = isset($metadata['classification']['tags']) ? implode(',', $metadata['classification']['tags']) : '无标签';

                            $output->writeln("  向量数据元数据: " . json_encode($metadata, JSON_UNESCAPED_UNICODE));
                        } else {
                            $output->writeln("<warning>ID: {$item['id']} 在向量数据库中不存在</warning>");
                        }
                    } catch (\Exception $e) {
                        $output->writeln("<error>检查ID: {$item['id']} 失败: " . $e->getMessage() . "</error>");
                    }
                }

                // 3. 尝试重新生成向量
                $output->writeln("\n尝试为百花园数据重新生成向量:");

                $enhancedSearch = new EnhancedVectorSearch();
                foreach ($knowledgeList as $item) {
                    if ($item['has_vector'] != 1) {
                        $output->writeln("为ID: {$item['id']} 生成向量...");
                        $result = $enhancedSearch->indexSingleItem($item);
                        $output->writeln("  结果: " . ($result ? '成功' : '失败'));
                    } else {
                        $output->writeln("ID: {$item['id']} 已有向量，跳过");
                    }
                }

                // 4. 测试搜索百花园
                $output->writeln("\n测试搜索'百花园':");

                $searchResults = $enhancedSearch->search("百花园", 5);
                $output->writeln("搜索结果数量: " . count($searchResults));

                if (!empty($searchResults)) {
                    $output->writeln("搜索结果:");
                    foreach ($searchResults as $index => $result) {
                        $output->writeln("[{$index}] ID: {$result['id']}, 标题: {$result['title']}, 得分: {$result['score']}");
                    }
                } else {
                    $output->writeln("<warning>搜索无结果</warning>");

                    // 5. 尝试不使用过滤条件搜索
                    $output->writeln("\n尝试使用反射调用不带过滤条件的搜索:");

                    $reflection = new \ReflectionClass($enhancedSearch);
                    $embeddingProperty = $reflection->getProperty('embedding');
                    $embeddingProperty->setAccessible(true);
                    $embedding = $embeddingProperty->getValue($enhancedSearch);

                    // 不需要获取vectorDB和collectionName，因为我们直接使用数据库查询

                    // 生成向量
                    $vector = $embedding->getEmbedding("百花园");

                    // 不使用过滤条件搜索，直接使用数据库查询
                    $vectors = Db::name('ds_knowledge_vectors')->select();

                    // 计算相似度
                    $results = [];
                    foreach ($vectors as $item) {
                        $vectorData = json_decode($item['vector'], true);
                        if (!is_array($vectorData)) {
                            continue;
                        }

                        // 计算余弦相似度
                        $dotProduct = 0;
                        $magnitudeA = 0;
                        $magnitudeB = 0;

                        for ($i = 0; $i < count($vector); $i++) {
                            $dotProduct += $vector[$i] * $vectorData[$i];
                            $magnitudeA += $vector[$i] * $vector[$i];
                            $magnitudeB += $vectorData[$i] * $vectorData[$i];
                        }

                        $magnitudeA = sqrt($magnitudeA);
                        $magnitudeB = sqrt($magnitudeB);

                        $similarity = 0;
                        if ($magnitudeA > 0 && $magnitudeB > 0) {
                            $similarity = $dotProduct / ($magnitudeA * $magnitudeB);
                        }

                        $results[] = [
                            'id' => $item['id'],
                            'similarity' => $similarity
                        ];
                    }

                    // 按相似度排序
                    usort($results, function($a, $b) {
                        return $b['similarity'] <=> $a['similarity'];
                    });

                    // 返回前5个结果
                    $rawResults = array_slice($results, 0, 5);

                    $output->writeln("不使用过滤条件搜索结果数量: " . count($rawResults));

                    if (!empty($rawResults)) {
                        $output->writeln("原始搜索结果:");
                        foreach ($rawResults as $index => $result) {
                            $output->writeln("[{$index}] ID: {$result['id']}, 相似度: {$result['similarity']}");

                            // 获取知识库详细信息
                            $knowledge = Db::name('ds_knowledge')->where('id', $result['id'])->find();
                            if ($knowledge) {
                                $output->writeln("    标题: {$knowledge['title']}");
                                $output->writeln("    分类ID: {$knowledge['category_id']}");
                                $output->writeln("    标签: {$knowledge['tags']}");
                            } else {
                                $output->writeln("    知识库中不存在此ID");
                            }
                        }
                    } else {
                        $output->writeln("<error>不使用过滤条件搜索仍无结果，可能是向量数据库中确实没有相关数据</error>");
                    }
                }
            } else {
                $output->writeln("<warning>知识库中没有包含'百花园'的数据</warning>");
            }
        } catch (\Exception $e) {
            $output->writeln($e->getTraceAsString());
            $output->writeln("<error>测试百花园数据失败: " . $e->getMessage() . "</error>");
        }
    }
}