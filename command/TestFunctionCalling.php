<?php

namespace addons\dsassistant\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\model\DsAssistant;
use think\Log;

/**
 * 测试Function Calling功能
 */
class TestFunctionCalling extends Command
{
    protected function configure()
    {
        $this->setName('dsassistant:test-function-calling')
            ->setDescription('测试Function Calling功能，验证是否同时返回文本和函数调用');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始测试Function Calling功能...");

        // 测试问题列表
        $testQuestions = [
            "介绍一下天空栈道",
            "天坛公园在哪里",
            "显示颐和园的位置",
            "我想了解故宫的详细信息",
            "帮我查看北海公园的详情"
        ];

        $assistant = new DsAssistant();

        foreach ($testQuestions as $index => $question) {
            $output->writeln("\n" . str_repeat("=", 50));
            $output->writeln("测试问题 " . ($index + 1) . ": {$question}");
            $output->writeln(str_repeat("=", 50));

            try {
                // 生成唯一的会话ID
                $sessionId = 'test_' . time() . '_' . $index;
                $userId = 'test_user';

                // 调用助理处理问题
                $result = $assistant->handleQuestion(
                    $question,
                    $userId,
                    $sessionId,
                    'test',
                    '127.0.0.1'
                );

                // 分析结果
                if (is_array($result)) {
                    // Function Calling模式的结果
                    $answer = $result['answer'] ?? '';
                    $functionCalls = $result['functionCalls'] ?? [];

                    $output->writeln("✅ 返回类型: Function Calling结果");
                    $output->writeln("📝 文本内容: " . ($answer ? "有内容" : "❌ 无内容"));
                    if ($answer) {
                        $output->writeln("   内容: {$answer}");
                    }
                    
                    $output->writeln("🔧 函数调用: " . (count($functionCalls) > 0 ? "有调用" : "无调用"));
                    if (count($functionCalls) > 0) {
                        foreach ($functionCalls as $call) {
                            $output->writeln("   函数: {$call['name']}");
                            $output->writeln("   参数: " . json_encode($call['parameters'], JSON_UNESCAPED_UNICODE));
                        }
                    }

                    // 检查是否同时有文本和函数调用
                    if ($answer && count($functionCalls) > 0) {
                        $output->writeln("✅ 理想状态: 同时包含文本说明和函数调用");
                    } elseif (!$answer && count($functionCalls) > 0) {
                        $output->writeln("⚠️  问题: 只有函数调用，缺少文本说明");
                    } elseif ($answer && count($functionCalls) === 0) {
                        $output->writeln("ℹ️  普通回复: 只有文本，无函数调用");
                    } else {
                        $output->writeln("❌ 异常: 既无文本也无函数调用");
                    }
                } else {
                    // 普通文本结果
                    $output->writeln("✅ 返回类型: 普通文本结果");
                    $output->writeln("📝 文本内容: {$result}");
                }

            } catch (\Exception $e) {
                $output->writeln("❌ 测试失败: " . $e->getMessage());
                Log::error("Function Calling测试失败: " . $e->getMessage());
            }

            // 添加延迟，避免API调用过于频繁
            sleep(2);
        }

        $output->writeln("\n" . str_repeat("=", 50));
        $output->writeln("测试完成！");
        $output->writeln("请检查上述结果，确认Function Calling是否同时返回文本和函数调用。");
        $output->writeln("如果仍然只返回函数调用而无文本，可能需要进一步调整系统提示词。");
        $output->writeln(str_repeat("=", 50));
    }
}
