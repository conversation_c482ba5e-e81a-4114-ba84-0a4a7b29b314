<?php

namespace addons\dsassistant\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\Db;
use think\Exception;
use think\Log;

/**
 * 景区智能助理命令行工具
 */
class WarningPush extends Command
{
    protected function configure()
    {
        $this->setName('dsassistant:warning-push')
            ->addOption('task', null, Option::VALUE_REQUIRED, '要执行的任务: clean_location, push_warning')
            ->addOption('warning_id', null, Option::VALUE_OPTIONAL, '预警ID，用于push_warning任务')
            ->setDescription('景区智能助理定时任务');
    }

    protected function execute(Input $input, Output $output)
    {
        $task = $input->getOption('task');
        
        if (empty($task)) {
            $output->writeln('请指定要执行的任务，例如：php think dsassistant --task=clean_location');
            return;
        }
        
        switch ($task) {
            case 'clean_location':
                $this->cleanExpiredLocationData($output);
                break;
                
            case 'push_warning':
                $warningId = $input->getOption('warning_id');
                if (empty($warningId)) {
                    $output->writeln('推送预警任务需要指定预警ID，例如：php think dsassistant --task=push_warning --warning_id=1');
                    return;
                }
                $this->pushWarningToUsers($warningId, $output);
                break;
                
            default:
                $output->writeln("未知任务: {$task}");
                break;
        }
    }
    
    /**
     * 清理过期的位置数据
     */
    protected function cleanExpiredLocationData(Output $output)
    {
        $now = time();
        
        // 删除过期的位置数据
        $result = Db::name('ds_user_location_pool')
            ->where('expire_time', '<', $now)
            ->delete();
            
        $output->writeln("已清理 {$result} 条过期位置数据");
    }
    
    /**
     * 推送预警信息给在景区内的用户
     */
    protected function pushWarningToUsers($warningId, Output $output)
    {
        if (!$warningId) {
            $output->writeln('预警ID不能为空');
            return;
        }
        
        // 获取预警信息
        $warning = Db::name('ds_warning')
            ->where('id', $warningId)
            ->where('status', 'normal')
            ->find();
            
        if (!$warning) {
            $output->writeln('预警信息不存在或已禁用');
            return;
        }
        
        // 获取在景区内的用户
        $users = Db::name('ds_user_location_pool')
            ->where('in_scenic', 1)
            ->where('expire_time', '>', time())
            ->select();
            
        if (empty($users)) {
            $output->writeln('当前没有用户在景区内');
            return;
        }
        
        $output->writeln("开始向 " . count($users) . " 个用户推送预警信息...");
        
        // 加载微信辅助类
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $wechatConfig = [
            'appid' => $config['wechat_appid'] ?? '',
            'appsecret' => $config['wechat_appsecret'] ?? '',
            'token' => $config['wechat_token'] ?? '',
            'template_id' => $config['wechat_template_id'] ?? ''
        ];
        $wechat = new \addons\dsassistant\library\Wechat($wechatConfig);
        
        $successCount = 0;
        $failCount = 0;
        
        // 向每个用户发送模板消息
        foreach ($users as $user) {
            try {
                $result = $wechat->sendWarningMessage($user['openid'], $warning);
                if ($result) {
                    $successCount++;
                    $output->write('.');
                } else {
                    $failCount++;
                    $output->write('x');
                }
                
                // 添加API调用间隔，避免频率限制
                usleep(100 * 1000); // 100毫秒
            } catch (Exception $e) {
                Log::error("推送预警消息异常: " . $e->getMessage());
                $failCount++;
                $output->write('E');
            }
        }
        
        $output->writeln('');
        $output->writeln("推送完成: 成功 {$successCount} 条, 失败 {$failCount} 条");
        
        // 记录推送日志
        Db::name('ds_warning_log')->insert([
            'warning_id' => $warning['id'],
            'title' => $warning['title'],
            'content' => $warning['content'],
            'type' => $warning['type'],
            'level' => $warning['level'],
            'createtime' => time()
        ]);
        
        $output->writeln("推送日志已记录");
    }
}
