<?php

namespace addons\dsassistant\command;

use app\common\model\DsAssistant;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Log;

/**
 * RAG模式测试命令
 */
class TestRag extends Command
{
    protected function configure()
    {
        $this->setName('dsassistant:test-rag')
            ->setDescription('测试RAG模式功能');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始测试RAG模式功能");
        
        // 测试问题列表
        $testQuestions = [
            '百花园在哪里？',
            '介绍一下百花园',
            '百花园有什么特色？',
            '景区门票多少钱？',
            '这个景区有什么好玩的地方？'
        ];
        
        $assistant = new DsAssistant();
        
        foreach ($testQuestions as $question) {
            $output->writeln("\n测试问题: {$question}");
            
            // 记录开始时间
            $startTime = microtime(true);
            
            // 调用handleQuestion方法
            $answer = $assistant->handleQuestion($question, 'test_user', 'test_session', 'cli', '127.0.0.1');
            
            // 记录结束时间
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            $output->writeln("回答: {$answer}");
            $output->writeln("处理时间: {$duration}ms");
        }
        
        $output->writeln("\nRAG模式测试完成");
    }
}
