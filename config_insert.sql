-- 将config.php配置项转换为fa_ds_content_config表的INSERT语句

-- 清空现有配置（如果需要）
-- TRUNCATE TABLE `fa_ds_content_config`;

-- 插入配置项
INSERT INTO `fa_ds_content_config` (`name`, `title`, `type`, `group`, `visible`, `content`, `value`, `rule`, `msg`, `tip`, `extend`) VALUES
('embedding_provider', '向量嵌入服务提供商', 'select', '向量搜索配置', '', '{\"baidu\":\"百度文心大模型\",\"xunfei\":\"讯飞星火大模型\",\"ali\":\"阿里云通义千问\",\"tencent\":\"腾讯云混元大模型\",\"huawei\":\"华为云盘古大模型\"}', 'tencent', 'required', '请选择向量嵌入服务提供商', '选择用于生成向量嵌入的服务提供商', ''),
('embedding_backup_provider', '备用向量嵌入服务提供商', 'select', '向量搜索配置', '', '{\"baidu\":\"百度文心大模型\",\"xunfei\":\"讯飞星火大模型\",\"ali\":\"阿里云通义千问\",\"tencent\":\"腾讯云混元大模型\",\"huawei\":\"华为云盘古大模型\"}', 'ali', 'required', '请选择备用向量嵌入服务提供商', '当主要服务提供商不可用时，使用备用服务提供商', ''),
('baidu_api_key', '百度API密钥', 'string', '向量搜索配置', '', '[]', '', '', '请填写百度API密钥', '百度文心大模型API密钥', ''),
('baidu_secret_key', '百度密钥', 'string', '向量搜索配置', '', '[]', '', '', '请填写百度密钥', '百度文心大模型密钥', ''),
('xunfei_app_id', '讯飞应用ID', 'string', '向量搜索配置', '', '[]', '', '', '请填写讯飞应用ID', '讯飞星火大模型应用ID', ''),
('xunfei_api_key', '讯飞API密钥', 'string', '向量搜索配置', '', '[]', '', '', '请填写讯飞API密钥', '讯飞星火大模型API密钥', ''),
('xunfei_api_secret', '讯飞API密钥', 'string', '向量搜索配置', '', '[]', '', '', '请填写讯飞API密钥', '讯飞星火大模型API密钥', ''),
('ali_api_key', '阿里API密钥', 'string', '向量搜索配置', '', '[]', '', '', '请填写阿里API密钥', '阿里云通义千问API密钥', ''),
('tencent_secret_id', '腾讯云SecretId', 'string', '向量搜索配置', '', '[]', 'AKIDeT7cw75pZ5OUN5as0xjIenlX6SbgYDgx', '', '请填写腾讯云SecretId', '腾讯云API密钥ID', ''),
('tencent_secret_key', '腾讯云SecretKey', 'string', '向量搜索配置', '', '[]', '8mHZhcLtHSRHuO3cAe4TxRnwsc8RWR9f', '', '请填写腾讯云SecretKey', '腾讯云API密钥', ''),
('tencent_region', '腾讯云区域', 'select', '向量搜索配置', 'embedding_provider == \'tencent\' || embedding_backup_provider == \'tencent\'', '{\"ap-guangzhou\":\"广州\",\"ap-shanghai\":\"上海\",\"ap-beijing\":\"北京\",\"ap-nanjing\":\"南京\",\"ap-hongkong\":\"香港\"}', 'ap-guangzhou', '', '请选择腾讯云区域', '腾讯云API区域', ''),
('huawei_ak', '华为云AK', 'string', '向量搜索配置', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', '[]', '', '', '请填写华为云AK', '华为云API密钥ID', ''),
('huawei_sk', '华为云SK', 'string', '向量搜索配置', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', '[]', '', '', '请填写华为云SK', '华为云API密钥', ''),
('huawei_project_id', '华为云项目ID', 'string', '向量搜索配置', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', '[]', '', '', '请填写华为云项目ID', '华为云项目ID', ''),
('huawei_region', '华为云区域', 'select', '向量搜索配置', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', '{\"cn-north-4\":\"华北-北京四\",\"cn-north-1\":\"华北-北京一\",\"cn-east-3\":\"华东-上海一\",\"cn-south-1\":\"华南-广州\",\"ap-southeast-1\":\"中国-香港\"}', 'cn-north-4', '', '请选择华为云区域', '华为云API区域', ''),
('huawei_embedding_model', '华为云嵌入模型', 'string', '向量搜索配置', 'embedding_provider == \'huawei\' || embedding_backup_provider == \'huawei\'', '[]', 'pangu-embedding', '', '请填写华为云嵌入模型名称', '华为云盘古嵌入模型名称', ''),
('deepseek_provider', 'DeepSeek提供商', 'select', 'DeepSeek配置', '', '{\"official\":\"DeepSeek官方\",\"tencent\":\"腾讯云DeepSeek\",\"baidu\":\"百度智能云DeepSeek\",\"ali\":\"阿里云DeepSeek\",\"huawei\":\"华为云DeepSeek\"}', 'official', 'required', '请选择DeepSeek提供商', '选择DeepSeek API的提供商', ''),
('deepseek_api_key', 'DeepSeek API密钥', 'string', 'DeepSeek配置', 'deepseek_provider == \'official\'', '[]', '***********************************', 'required', '请填写DeepSeek API密钥', '请在DeepSeek官网申请API密钥', ''),
('deepseek_model', 'DeepSeek模型', 'select', 'DeepSeek配置', '', '{\"deepseek-chat\":\"deepseek-chat\",\"deepseek-chat-pro\":\"deepseek-chat-pro\"}', 'deepseek-chat', 'required', '请选择DeepSeek模型', '选择使用的DeepSeek模型', ''),
('system_prompt', '系统提示词', 'text', 'DeepSeek配置', '', '[]', '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。', 'required', '请填写系统提示词', '设置AI助手的角色定位和行为指南', ''),
('cache_time', '缓存时间(秒)', 'number', '性能配置', '', '[]', '3600', 'required', '请填写缓存时间', '设置问答结果缓存时间，单位：秒', ''),
('confidence_threshold', '置信度阈值', 'number', '性能配置', '', '[]', '70', 'required', '请填写置信度阈值', '设置AI回答的置信度阈值，低于此值将转人工客服', ''),
('vectordb_type', '向量数据库类型', 'select', '向量数据库配置', '', '{\"mysql\":\"MySQL (内置)\",\"tencent\":\"腾讯云 VectorDB\",\"aliyun\":\"阿里云 VectorSearch\",\"baidu\":\"百度智能云 VectorDB\",\"huawei\":\"华为云 VSS\"}', 'mysql', 'required', '请选择向量数据库类型', '选择用于存储和检索向量的数据库类型', ''),
('vector_search_type', '向量搜索实现', 'select', '向量搜索配置', '', '{\"enhanced\":\"进阶版 (向量数据库)\",\"jieba\":\"高级版 (Jieba分词+向量数据库)\"}', 'optimized', 'required', '请选择向量搜索实现', '选择向量搜索的实现方式，高级版需要Jieba分词服务支持', ''),
('wechat_appid', '微信公众号AppID', 'string', '微信配置', '', '[]', '', '', '请填写微信公众号AppID', '微信公众号的AppID', ''),
('wechat_appsecret', '微信公众号AppSecret', 'string', '微信配置', '', '[]', '', '', '请填写微信公众号AppSecret', '微信公众号的AppSecret', ''),
('wechat_token', '微信公众号Token', 'string', '微信配置', '', '[]', '', '', '请填写微信公众号Token', '微信公众号的Token', ''),
('tencent_map_key', '腾讯地图Key', 'string', '地图配置', '', '[]', '', '', '请填写腾讯地图Key', '腾讯地图开发者Key，用于网页和小程序地图显示', ''),
('customer_service_phone', '客服电话', 'string', '基础配置', '', '[]', '************', '', '请填写客服电话', '当AI无法回答问题时显示的客服电话', ''),
('group', '参数组', 'array', '', '', '[]', '{\"basic\":\"基础\",\"contact\":\"联系方式\",\"tmap\":\"QQ地图\",\"wechat_mp\":\"微信公众号\",\"wechat_xcx\":\"微信小程序\"}', '', '', '', ''),

-- RAG模式配置（拆分为独立配置项）
('rag_enabled', 'RAG模式启用', 'switch', 'rag_model', '', '[]', '1', '', '请选择是否启用RAG模式', '是否启用检索增强生成模式', ''),
('rag_top_k', '检索结果数量', 'number', 'rag_model', '', '[]', '3', 'required', '请填写检索结果数量', '从本地知识库检索的结果数量', ''),
('rag_min_score', '最低得分要求', 'number', 'rag_model', '', '[]', '0.3', 'required', '请填写最低得分要求', '本地知识库结果的最低得分要求，低于此分数的结果将被过滤', ''),
('rag_context_format', '上下文格式', 'text', 'rag_model', '', '[]', '问题: {question}\n回答: {answer}\n\n', '', '请填写上下文格式', '上下文格式，支持{question}和{answer}占位符', ''),
('rag_prompt_template', '提示模板', 'text', 'rag_model', '', '[]', '以下是关于用户问题的相关信息，请参考这些信息回答问题，但不要直接复制这些内容，而是用自然、流畅的语言回答：\n\n{context}', '', '请填写提示模板', '提供给大模型的提示模板，支持{context}占位符', '');
