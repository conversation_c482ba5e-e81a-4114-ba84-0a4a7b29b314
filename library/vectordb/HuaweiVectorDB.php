<?php

namespace addons\dsassistant\library\vectordb;

// 使用完全限定的命名空间，避免IDE警告
// 这些类在运行时可用，但IDE无法识别
// \think\Log, \think\Exception

/**
 * 华为云向量数据库实现
 * 使用华为云VSS服务
 */
class HuaweiVectorDB implements VectorDBInterface
{
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];

    /**
     * API基础URL
     * @var string
     */
    protected $baseUrl = '';

    /**
     * 初始化向量数据库连接
     *
     * @param array $config 配置信息
     * @return bool 是否成功
     */
    public function initialize(array $config): bool
    {
        // 检查必要的配置
        $requiredKeys = ['ak', 'sk', 'projectId', 'endpoint'];
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key]) || empty($config[$key])) {
                throw new \think\Exception("华为云VSS配置缺少必要参数: {$key}");
            }
        }

        $this->config = $config;
        $this->baseUrl = 'https://' . $config['endpoint'];

        return true;
    }

    /**
     * 创建集合/表
     *
     * @param string $collectionName 集合名称
     * @param int $dimension 向量维度
     * @param array $options 其他选项
     * @return bool 是否成功
     */
    public function createCollection(string $collectionName, int $dimension, array $options = []): bool
    {
        try {
            $params = [
                'name' => $collectionName,
                'dimension' => $dimension,
                'metric_type' => $options['metricType'] ?? 'COSINE',
                'description' => $options['description'] ?? ''
            ];

            // 添加可选参数
            if (isset($options['indexType'])) {
                $params['index_type'] = $options['indexType'];
            }

            $response = $this->sendRequest('POST', '/v1/' . $this->config['projectId'] . '/vss/collections', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            \think\Log::info("成功创建华为云VSS集合: {$collectionName}");
            return true;
        } catch (\Exception $e) {
            \think\Log::error("创建华为云VSS集合失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function insertVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        try {
            // 先获取集合ID
            $collectionId = $this->getCollectionId($collectionName);

            if (!$collectionId) {
                throw new \think\Exception("集合不存在: {$collectionName}");
            }

            // 将知识库数据转换为标准元数据格式
            $standardMetadata = VectorDBFactory::formatMetadata($metadata);

            $document = [
                'id' => $id,
                'vector' => $vector
            ];

            // 添加元数据
            if (!empty($standardMetadata)) {
                $document['metadata'] = $standardMetadata;
            }

            $params = [
                'documents' => [$document]
            ];

            $response = $this->sendRequest('POST', '/v1/' . $this->config['projectId'] . '/vss/collections/' . $collectionId . '/documents', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("插入向量到华为云VSS失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param array $vectors 向量数据数组，每个元素包含id、vector和metadata
     * @return bool 是否成功
     */
    public function batchInsertVectors(string $collectionName, array $vectors): bool
    {
        try {
            // 先获取集合ID
            $collectionId = $this->getCollectionId($collectionName);

            if (!$collectionId) {
                throw new \think\Exception("集合不存在: {$collectionName}");
            }

            $documents = [];

            foreach ($vectors as $item) {
                // 将知识库数据转换为标准元数据格式
                $standardMetadata = VectorDBFactory::formatMetadata($item['metadata'] ?? []);

                $document = [
                    'id' => $item['id'],
                    'vector' => $item['vector']
                ];

                // 添加元数据
                if (!empty($standardMetadata)) {
                    $document['metadata'] = $standardMetadata;
                }

                $documents[] = $document;
            }

            // 分批处理，每批最多100个文档
            $batches = array_chunk($documents, 100);

            foreach ($batches as $batch) {
                $params = [
                    'documents' => $batch
                ];

                $response = $this->sendRequest('POST', '/v1/' . $this->config['projectId'] . '/vss/collections/' . $collectionId . '/documents', $params);

                if (isset($response['error_code'])) {
                    throw new \think\Exception($response['error_msg'] ?? '未知错误');
                }
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("批量插入向量到华为云VSS失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function updateVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        try {
            // 先获取集合ID
            $collectionId = $this->getCollectionId($collectionName);

            if (!$collectionId) {
                throw new \think\Exception("集合不存在: {$collectionName}");
            }

            // 将知识库数据转换为标准元数据格式
            $standardMetadata = VectorDBFactory::formatMetadata($metadata);

            $document = [
                'id' => $id,
                'vector' => $vector
            ];

            // 添加元数据
            if (!empty($standardMetadata)) {
                $document['metadata'] = $standardMetadata;
            }

            $params = [
                'documents' => [$document]
            ];

            $response = $this->sendRequest('PUT', '/v1/' . $this->config['projectId'] . '/vss/collections/' . $collectionId . '/documents', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("更新华为云VSS向量失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return bool 是否成功
     */
    public function deleteVector(string $collectionName, string $id): bool
    {
        try {
            // 先获取集合ID
            $collectionId = $this->getCollectionId($collectionName);

            if (!$collectionId) {
                throw new \think\Exception("集合不存在: {$collectionName}");
            }

            $params = [
                'ids' => [$id]
            ];

            $response = $this->sendRequest('DELETE', '/v1/' . $this->config['projectId'] . '/vss/collections/' . $collectionId . '/documents', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("从华为云VSS删除向量失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 搜索相似向量
     *
     * 使用VectorDBFactory中的convertFilter方法处理过滤条件，
     * 支持对元数据字段的过滤。
     *
     * @param string $collectionName 集合名称
     * @param array $queryVector 查询向量
     * @param int $topK 返回结果数量
     * @param array $filter 过滤条件
     * @return array 搜索结果
     */
    public function search(string $collectionName, array $queryVector, int $topK = 10, array $filter = []): array
    {
        try {
            // 先获取集合ID
            $collectionId = $this->getCollectionId($collectionName);

            if (!$collectionId) {
                throw new \think\Exception("集合不存在: {$collectionName}");
            }

            $params = [
                'vector' => $queryVector,
                'top_k' => $topK
            ];

            // 转换过滤条件为华为云VSS查询条件
            if (!empty($filter)) {
                $convertedFilter = VectorDBFactory::convertFilter($filter, VectorDBFactory::DB_TYPE_HUAWEI);
                $params['filter'] = $this->buildFilter($convertedFilter);
            }

            $response = $this->sendRequest('POST', '/v1/' . $this->config['projectId'] . '/vss/collections/' . $collectionId . '/search', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            $results = [];
            if (isset($response['documents']) && !empty($response['documents'])) {
                foreach ($response['documents'] as $doc) {
                    $results[] = [
                        'id' => $doc['id'],
                        'similarity' => $doc['score'],
                        'metadata' => $doc['metadata'] ?? []
                    ];
                }
            }

            return $results;
        } catch (\Exception $e) {
            \think\Log::error("在华为云VSS中搜索向量失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return array|null 向量数据
     */
    public function getVector(string $collectionName, string $id): ?array
    {
        try {
            // 先获取集合ID
            $collectionId = $this->getCollectionId($collectionName);

            if (!$collectionId) {
                throw new \think\Exception("集合不存在: {$collectionName}");
            }

            $response = $this->sendRequest('GET', '/v1/' . $this->config['projectId'] . '/vss/collections/' . $collectionId . '/documents/' . $id);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            if (empty($response)) {
                return null;
            }

            return [
                'id' => $response['id'],
                'vector' => $response['vector'],
                'metadata' => $response['metadata'] ?? []
            ];
        } catch (\Exception $e) {
            \think\Log::error("从华为云VSS获取向量失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取集合信息
     *
     * @param string $collectionName 集合名称
     * @return array 集合信息
     */
    public function getCollectionInfo(string $collectionName): array
    {
        try {
            // 先获取集合ID
            $collectionId = $this->getCollectionId($collectionName);

            if (!$collectionId) {
                throw new \think\Exception("集合不存在: {$collectionName}");
            }

            $response = $this->sendRequest('GET', '/v1/' . $this->config['projectId'] . '/vss/collections/' . $collectionId);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            return [
                'name' => $response['name'],
                'dimension' => $response['dimension'],
                'count' => $response['document_count'] ?? 0,
                'metricType' => $response['metric_type'] ?? '',
                'indexType' => $response['index_type'] ?? '',
                'engine' => 'HuaweiVSS'
            ];
        } catch (\Exception $e) {
            \think\Log::error("获取华为云VSS集合信息失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 列出所有集合（带详细信息）
     *
     * @return array 集合列表
     */
    protected function listCollectionsWithDetails(): array
    {
        $response = $this->sendRequest('GET', '/v1/' . $this->config['projectId'] . '/vss/collections');

        if (isset($response['error_code'])) {
            throw new \think\Exception($response['error_msg'] ?? '未知错误');
        }

        return $response['collections'] ?? [];
    }

    /**
     * 获取集合ID
     *
     * @param string $collectionName 集合名称
     * @return string|null 集合ID
     */
    protected function getCollectionId(string $collectionName): ?string
    {
        $collections = $this->listCollectionsWithDetails();

        foreach ($collections as $collection) {
            if ($collection['name'] === $collectionName) {
                return $collection['id'];
            }
        }

        return null;
    }

    /**
     * 计算两个向量的相似度
     *
     * 使用VectorDBFactory中的calculateSimilarity方法，
     * 避免代码重复。
     *
     * @param array $vector1 向量1
     * @param array $vector2 向量2
     * @return float 相似度
     */
    public function calculateSimilarity(array $vector1, array $vector2): float
    {
        // 使用VectorDBFactory中的方法
        return VectorDBFactory::calculateSimilarity($vector1, $vector2);
    }

    /**
     * 构建过滤条件
     *
     * @param array $filter 过滤条件
     * @return string 过滤条件字符串
     */
    protected function buildFilter(array $filter): string
    {
        $conditions = [];

        foreach ($filter as $field => $value) {
            if (is_array($value) && isset($value['operator']) && isset($value['value'])) {
                switch ($value['operator']) {
                    case '=':
                        $conditions[] = "{$field}={$this->formatFilterValue($value['value'])}";
                        break;
                    case '>':
                        $conditions[] = "{$field}>{$this->formatFilterValue($value['value'])}";
                        break;
                    case '>=':
                        $conditions[] = "{$field}>={$this->formatFilterValue($value['value'])}";
                        break;
                    case '<':
                        $conditions[] = "{$field}<{$this->formatFilterValue($value['value'])}";
                        break;
                    case '<=':
                        $conditions[] = "{$field}<={$this->formatFilterValue($value['value'])}";
                        break;
                    case 'in':
                        $values = array_map([$this, 'formatFilterValue'], (array)$value['value']);
                        $conditions[] = "{$field} in [" . implode(',', $values) . "]";
                        break;
                    case 'like':
                        $conditions[] = "{$field} like {$this->formatFilterValue($value['value'])}";
                        break;
                }
            } else {
                $conditions[] = "{$field}={$this->formatFilterValue($value)}";
            }
        }

        return implode(' AND ', $conditions);
    }

    /**
     * 格式化过滤值
     *
     * @param mixed $value 值
     * @return string 格式化后的值
     */
    protected function formatFilterValue($value): string
    {
        if (is_string($value)) {
            return "'{$value}'";
        } elseif (is_bool($value)) {
            return $value ? 'true' : 'false';
        } elseif (is_null($value)) {
            return 'null';
        } else {
            return (string)$value;
        }
    }

    /**
     * 发送API请求
     *
     * @param string $method HTTP方法
     * @param string $path 请求路径
     * @param array $params 请求参数
     * @return array 响应结果
     */
    protected function sendRequest(string $method, string $path, array $params = []): array
    {
        $url = $this->baseUrl . $path;
        $ak = $this->config['ak'];
        $sk = $this->config['sk'];

        $headers = [
            'Content-Type: application/json'
        ];

        // 计算签名
        $timestamp = gmdate('Ymd\THis\Z');
        $nonce = uniqid();

        $contentSha256 = '';
        $payload = '';
        if ($method == 'POST' || $method == 'PUT') {
            $payload = json_encode($params);
            $contentSha256 = hash('sha256', $payload);
        }

        $canonicalRequest = $method . "\n"
                          . $path . "\n"
                          . "\n"
                          . "content-type:application/json\n"
                          . "host:" . $this->config['endpoint'] . "\n"
                          . "x-sdk-date:" . $timestamp . "\n"
                          . "\n"
                          . "content-type;host;x-sdk-date\n"
                          . $contentSha256;

        $stringToSign = "SDK-HMAC-SHA256\n"
                      . $timestamp . "\n"
                      . hash('sha256', $canonicalRequest);

        $signature = hash_hmac('sha256', $stringToSign, $sk);

        $headers[] = 'X-Sdk-Date: ' . $timestamp;
        $headers[] = 'Authorization: SDK-HMAC-SHA256 Access=' . $ak . ', SignedHeaders=content-type;host;x-sdk-date, Signature=' . $signature;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        if ($method == 'POST' || $method == 'PUT') {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($httpCode >= 400) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \think\Exception("华为云API请求失败: HTTP {$httpCode}, {$error}");
        }

        curl_close($ch);

        return json_decode($response, true) ?: [];
    }
}
