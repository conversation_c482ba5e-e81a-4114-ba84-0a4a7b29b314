<?php

namespace addons\dsassistant\library\vectordb;

/**
 * 向量数据库工厂类
 * 用于创建不同的向量数据库实例，并定义统一的表结构和数据格式规范
 *
 * 使用示例:
 *
 * 1. 获取向量数据库实例:
 * ```php
 * $vectorDB = VectorDBFactory::getInstance();
 * ```
 *
 * 2. 将知识库数据转换为标准元数据:
 * ```php
 * $knowledgeRecord = Db::name('ds_knowledge')->where('id', 1)->find();
 * $standardMetadata = VectorDBFactory::createStandardMetadata($knowledgeRecord);
 * ```
 *
 * 3. 插入向量数据:
 * ```php
 * $vector = $embedding->getEmbedding($knowledgeRecord['title']);
 * $vectorDB->insertVector(
 *     'ds_knowledge_vectors',
 *     $knowledgeRecord['id'],
 *     $vector,
 *     $standardMetadata
 * );
 * ```
 *
 * 4. 批量处理知识库数据:
 * ```php
 * $knowledgeRecords = Db::name('ds_knowledge')->select();
 * $vectors = [];
 *
 * // 生成向量
 * foreach ($knowledgeRecords as $record) {
 *     $vectors[$record['id']] = $embedding->getEmbedding($record['title']);
 * }
 *
 * // 创建批量数据
 * $batch = VectorDBFactory::createVectorBatch($knowledgeRecords, $vectors);
 *
 * // 批量插入
 * $vectorDB->batchInsertVectors('ds_knowledge_vectors', $batch);
 * ```
 *
 * 5. 搜索向量:
 * ```php
 * $queryVector = $embedding->getEmbedding($title);
 * $queryConditions = [
 *     'category_id' => 5,
 *     'status' => 'normal'
 * ];
 *
 * // 转换查询条件
 * $filter = VectorDBFactory::convertFilter($queryConditions);
 *
 * // 执行搜索
 * $results = $vectorDB->search('ds_knowledge_vectors', $queryVector, 10, $filter);
 * ```
 */
class VectorDBFactory
{
    /**
     * 支持的向量数据库类型
     */
    const DB_TYPE_MYSQL = 'mysql';
    const DB_TYPE_TENCENT = 'tencent';
    const DB_TYPE_ALIYUN = 'aliyun';
    const DB_TYPE_BAIDU = 'baidu';
    const DB_TYPE_HUAWEI = 'huawei';

    /**
     * 元数据字段规范
     * 定义统一的元数据结构，所有VectorDB实现都应遵循此结构
     */
    const METADATA_SCHEMA = [
        // 内容信息
        'content' => [
            'title' => '',      // 问题文本
            'content' => '',        // 答案文本
            'summary' => ''        // 内容摘要
        ],
        // 分类信息
        'classification' => [
            'category_id' => 0,    // 分类ID
            'category' => '',      // 分类名称
            'tags' => []           // 标签/关键词数组
        ],
        // 权重信息
        'relevance' => [
            'weight' => 0,         // 权重值
            'boost_factor' => 1.0, // 提升因子
            'quality_score' => 1.0 // 质量评分
        ],
        // 时间约束
        'time_constraints' => [
            'valid_from' => null,  // 有效期开始
            'valid_until' => null  // 有效期结束
        ],
        // 搜索优化
        'search_optimization' => [
            'segmented_text' => '', // 分词文本
            'synonyms' => []        // 同义词数组
        ],
        // 状态信息
        'status' => 'normal'        // 状态(normal/hidden)
    ];

    /**
     * MySQL表结构定义
     * 用于MySQL实现的表结构，包含冗余字段以优化查询性能
     */
    const MYSQL_TABLE_SCHEMA = "
        id VARCHAR(64) PRIMARY KEY,
        vector LONGTEXT NOT NULL,
        metadata TEXT,
        category_id INT,
        category VARCHAR(50),
        tags TEXT,
        weight INT DEFAULT 0,
        status VARCHAR(20) DEFAULT 'normal',
        valid_from INT,
        valid_until INT,
        createtime INT(10) UNSIGNED NOT NULL,
        updatetime INT(10) UNSIGNED NOT NULL,
        INDEX idx_category_id (category_id),
        INDEX idx_status (status),
        INDEX idx_weight (weight),
        INDEX idx_valid (valid_from, valid_until)
    ";

    /**
     * 向量数据库实例
     * @var VectorDBInterface
     */
    private static $instance = null;

    /**
     * 将知识库数据转换为标准元数据格式
     *
     * 本方法将知识库原始数据转换为标准化的元数据结构，便于在不同的向量数据库实现中统一使用。
     * 支持多种输入格式，包括扁平结构和嵌套结构，并自动进行字段映射和转换。
     *
     * @param array $knowledgeData 知识库数据(如问题、答案、分类等)
     * @return array 标准化的元数据结构
     *
     * @example 基本用法
     * ```php
     * // 从知识库记录创建标准元数据
     * $knowledgeData = [
     *     'title' => '景区门票多少钱？',
     *     'content' => '成人票100元，儿童票50元...',
     *     'category_id' => 5,
     *     'category' => '票务',
     *     'tags' => '门票,价格,优惠',
     *     'weight' => 100,
     *     'status' => 'normal'
     * ];
     *
     * $metadata = VectorDBFactory::formatMetadata($knowledgeData);
     * // 结果:
     * // [
     * //     'content' => [
     * //         'title' => '景区门票多少钱？',
     * //         'content' => '成人票100元，儿童票50元...',
     * //         'summary' => ''
     * //     ],
     * //     'classification' => [
     * //         'category_id' => 5,
     * //         'category' => '票务',
     * //         'tags' => ['门票', '价格', '优惠']
     * //     ],
     * //     'relevance' => [
     * //         'weight' => 100,
     * //         'boost_factor' => 1.0,
     * //         'quality_score' => 1.0
     * //     ],
     * //     'time_constraints' => [
     * //         'valid_from' => null,
     * //         'valid_until' => null
     * //     ],
     * //     'search_optimization' => [
     * //         'segmented_text' => '',
     * //         'synonyms' => []
     * //     ],
     * //     'status' => 'normal'
     * // ]
     * ```
     *
     * @example 使用嵌套结构
     * ```php
     * // 已经部分符合标准结构的数据
     * $knowledgeData = [
     *     'content' => [
     *         'title' => '景区门票多少钱？',
     *         'content' => '成人票100元，儿童票50元...'
     *     ],
     *     'classification' => [
     *         'category' => '票务',
     *         'tags' => ['门票', '价格']
     *     ],
     *     'status' => 'normal'
     * ];
     *
     * $metadata = VectorDBFactory::formatMetadata($knowledgeData);
     * // 结果: 保留原有结构，并补充缺失的字段
     * ```
     *
     * @example 混合使用扁平结构和嵌套结构
     * ```php
     * // 混合使用扁平字段和嵌套字段
     * $knowledgeData = [
     *     'title' => '景区门票多少钱？',  // 扁平字段
     *     'classification' => [             // 嵌套字段
     *         'category_id' => 5,
     *         'category' => '票务'
     *     ],
     *     'tags' => '门票,价格',         // 扁平字段
     *     'status' => 'normal'              // 扁平字段
     * ];
     *
     * $metadata = VectorDBFactory::formatMetadata($knowledgeData);
     * // 结果: 正确处理混合结构，生成完整的标准元数据
     * ```
     */
    public static function formatMetadata(array $knowledgeData): array
    {
        // 创建基于规范的空结构
        $formattedMetadata = self::METADATA_SCHEMA;

        // 处理知识库数据格式
        if (isset($knowledgeData['title'])) {
            $formattedMetadata['content']['title'] = $knowledgeData['title'];
        }
        if (isset($knowledgeData['content'])) {
            $formattedMetadata['content']['content'] = $knowledgeData['content'];
        }
        if (isset($knowledgeData['category_id'])) {
            $formattedMetadata['classification']['category_id'] = $knowledgeData['category_id'];
        }
        if (isset($knowledgeData['category'])) {
            $formattedMetadata['classification']['category'] = $knowledgeData['category'];
        }
        if (isset($knowledgeData['tags'])) {
            // 处理关键词，可能是字符串或数组
            if (is_string($knowledgeData['tags'])) {
                $formattedMetadata['classification']['tags'] = array_map('trim', explode(',', $knowledgeData['tags']));
            } elseif (is_array($knowledgeData['tags'])) {
                $formattedMetadata['classification']['tags'] = $knowledgeData['tags'];
            }
        }
        if (isset($knowledgeData['weight'])) {
            $formattedMetadata['relevance']['weight'] = $knowledgeData['weight'];
        }
        if (isset($knowledgeData['status'])) {
            $formattedMetadata['status'] = $knowledgeData['status'];
        }
        if (isset($knowledgeData['valid_from'])) {
            $formattedMetadata['time_constraints']['valid_from'] = $knowledgeData['valid_from'];
        }
        if (isset($knowledgeData['valid_until'])) {
            $formattedMetadata['time_constraints']['valid_until'] = $knowledgeData['valid_until'];
        }
        if (isset($knowledgeData['segmented_text'])) {
            $formattedMetadata['search_optimization']['segmented_text'] = $knowledgeData['segmented_text'];
        }

        // 处理已经符合新格式的数据（直接覆盖）
        foreach ($knowledgeData as $key => $value) {
            if (isset($formattedMetadata[$key]) && is_array($formattedMetadata[$key]) && is_array($value)) {
                $formattedMetadata[$key] = array_merge($formattedMetadata[$key], $value);
            } elseif (isset($formattedMetadata[$key])) {
                $formattedMetadata[$key] = $value;
            }
        }

        return $formattedMetadata;
    }

    /**
     * 从标准元数据中提取MySQL表的冗余字段
     *
     * @param array $standardMetadata 标准格式的元数据
     * @return array MySQL表的冗余字段数组
     */
    public static function extractMySQLFields(array $standardMetadata): array
    {
        $fields = [
            'category_id' => $standardMetadata['classification']['category_id'] ?? 0,
            'category' => $standardMetadata['classification']['category'] ?? '',
            'tags' => is_array($standardMetadata['classification']['tags'])
                ? implode(',', $standardMetadata['classification']['tags'])
                : $standardMetadata['classification']['tags'],
            'weight' => $standardMetadata['relevance']['weight'] ?? 0,
            'status' => $standardMetadata['status'] ?? 'normal',
            'valid_from' => $standardMetadata['time_constraints']['valid_from'] ?? null,
            'valid_until' => $standardMetadata['time_constraints']['valid_until'] ?? null
        ];

        return $fields;
    }

    /**
     * 转换知识库查询条件为向量数据库过滤条件
     *
     * @param array $queryConditions 知识库查询条件(如分类、关键词等)
     * @param string $dbType 数据库类型
     * @return array 转换后的向量数据库过滤条件
     */
    public static function convertFilter(array $queryConditions, string $dbType = self::DB_TYPE_MYSQL): array
    {
        $convertedFilter = [];

        // 处理MySQL特定的过滤条件转换
        if ($dbType === self::DB_TYPE_MYSQL) {
            foreach ($queryConditions as $field => $value) {
                // 处理分类过滤
                if ($field === 'category') {
                    $convertedFilter['category'] = $value;
                }
                // 处理分类ID过滤
                elseif ($field === 'category_id') {
                    $convertedFilter['category_id'] = $value;
                }
                // 处理标签/关键词过滤
                elseif ( $field === 'tags') {
                    // 如果是数组，则使用LIKE查询
                    if (is_array($value)) {
                        $likeValues = [];
                        foreach ($value as $tag) {
                            if (is_string($tag)) {
                                $likeValues[] = '%' . $tag . '%';
                            } else {
                                // 如果标签不是字符串，记录警告并跳过
                                if (class_exists('\\think\\Log')) {
                                    call_user_func(['\\think\\Log', 'warning'], "标签值不是字符串: " . gettype($tag));
                                }
                            }
                        }
                        if (!empty($likeValues)) {
                            $convertedFilter['tags'] = ['like', $likeValues, 'OR'];
                        } else {
                            // 如果没有有效的标签，则不添加过滤条件
                            if (class_exists('\\think\\Log')) {
                                call_user_func(['\\think\\Log', 'warning'], "没有有效的标签过滤条件");
                            }
                        }
                    } else if (is_string($value)) {
                        // 单个值，使用LIKE查询
                        $convertedFilter['tags'] = ['like', '%' . $value . '%'];
                    } else {
                        // 如果值不是字符串或数组，记录警告并跳过
                        if (class_exists('\\think\\Log')) {
                            call_user_func(['\\think\\Log', 'warning'], "标签值不是字符串或数组: " . gettype($value));
                        }
                    }
                }
                // 处理权重过滤
                elseif ($field === 'weight') {
                    $convertedFilter['weight'] = $value;
                }
                // 处理状态过滤
                elseif ($field === 'status') {
                    $convertedFilter['status'] = $value;
                }
                // 处理有效期过滤
                elseif ($field === 'valid_from') {
                    $convertedFilter['valid_from'] = $value;
                }
                elseif ($field === 'valid_until') {
                    $convertedFilter['valid_until'] = $value;
                }
                // 其他过滤条件保持不变
                else {
                    $convertedFilter[$field] = $value;
                }
            }
        }
        // 其他数据库类型的过滤条件转换可以在这里添加
        else {
            $convertedFilter = $queryConditions;
        }

        return $convertedFilter;
    }

    /**
     * 获取向量数据库实例
     *
     * @param string $type 向量数据库类型，默认从配置中获取
     * @param array $config 配置信息，默认从配置中获取
     * @return VectorDBInterface 向量数据库实例
     * @throws \Exception 如果不支持的向量数据库类型
     */
    public static function getInstance($type = null, $config = null)
    {
        if (self::$instance !== null) {
            return self::$instance;
        }

        // 默认值
        $type = $type ?? self::DB_TYPE_MYSQL;
        $config = $config ?? [];

        // 尝试从配置中获取
        if ($type === null || $config === null) {
            try {
                // 尝试获取插件配置
                $addonConfig = \app\admin\model\dsassistant\Contentconfig::getConfigAll();

                if ($type === null && isset($addonConfig['vectordb_type'])) {
                    $type = $addonConfig['vectordb_type'];
                }

                if ($config === null && isset($addonConfig['vectordb_config'])) {
                    $config = $addonConfig['vectordb_config'];
                }
                
            } catch (\Throwable $e) {
                // 忽略配置获取错误，使用默认值
            }
        }

        // 创建实例
        switch ($type) {
            case self::DB_TYPE_MYSQL:
                self::$instance = new MySQLVectorDB();
                break;

            case self::DB_TYPE_TENCENT:
                self::$instance = new TencentVectorDB();
                break;

            case self::DB_TYPE_ALIYUN:
                self::$instance = new AliyunVectorDB();
                break;

            case self::DB_TYPE_BAIDU:
                self::$instance = new BaiduVectorDB();
                break;

            case self::DB_TYPE_HUAWEI:
                self::$instance = new HuaweiVectorDB();
                break;

            default:
                throw new \Exception("不支持的向量数据库类型: {$type}");
        }

        // 初始化实例
        try {
            self::$instance->initialize($config);
            // 记录日志
            if (class_exists('\\think\\Log')) {
                call_user_func(['\\think\\Log', 'info'], "向量数据库 [{$type}] 初始化成功");
            }
        } catch (\Exception $e) {
            // 记录错误日志
            if (class_exists('\\think\\Log')) {
                call_user_func(['\\think\\Log', 'error'], "向量数据库 [{$type}] 初始化失败: " . $e->getMessage());
            }
            throw $e;
        }

        return self::$instance;
    }

    /**
     * 重置实例
     *
     * @return void
     */
    public static function resetInstance()
    {
        self::$instance = null;
    }

    /**
     * 从知识库记录直接创建标准元数据
     *
     * 本方法是一个便捷方法，用于从数据库查询结果直接创建标准元数据。
     * 它会自动处理字段映射，包括处理不同的字段命名约定（如category_name vs category）。
     *
     * @param array|\think\Model $knowledgeRecord 知识库记录(数据库查询结果)
     * @return array 标准化的元数据
     *
     * @example 基本用法
     * ```php
     * // 从数据库获取知识库记录
     * $record = Db::name('ds_knowledge')->where('id', 1)->find();
     *
     * // 创建标准元数据
     * $metadata = VectorDBFactory::createStandardMetadata($record);
     *
     * // 使用元数据插入向量
     * $vectorDB->insertVector(
     *     'ds_knowledge_vectors',
     *     $record['id'],
     *     $vector,
     *     $metadata
     * );
     * ```
     *
     * @example 处理不同的字段命名
     * ```php
     * // 处理不同的字段命名约定
     * $record = [
     *     'id' => 1,
     *     'title' => '景区门票多少钱？',
     *     'content' => '成人票100元...',
     *     'category_id' => 5,
     *     'category.category' => '票务',  // 注意这里使用category.category而不是category
     *     'tags' => '门票,价格',
     *     'valid_from' => 1672502400,  // 注意这里使用start_time而不是valid_from
     *     'valid_until' => 1704038399,    // 注意这里使用end_time而不是valid_until
     *     'weight' => 100,
     *     'status' => 'normal'
     * ];
     *
     * // 方法会自动处理字段映射
     * $metadata = VectorDBFactory::createStandardMetadata($record);
     * // 结果中会正确映射字段:
     * // - category_name 映射到 classification.category
     * // - start_time 映射到 time_constraints.valid_from
     * // - end_time 映射到 time_constraints.valid_until
     * ```
     */
    public static function createStandardMetadata($knowledgeRecord): array
    {
        // 提取知识库数据
        $knowledgeData = [
            'title' => $knowledgeRecord['title'] ?? '',
            'content' => $knowledgeRecord['content'] ?? '',
            'category_id' => $knowledgeRecord['category_id'] ?? 0,
            'category' => isset($knowledgeRecord['category']) ? $knowledgeRecord['category']['category'] :'',
            'tags' => $knowledgeRecord['tags'] ?? '',
            'weight' => $knowledgeRecord['weight'] ?? 0,
            'status' => $knowledgeRecord['status'] ?? 'normal',
            'valid_from' => $knowledgeRecord['valid_from'] ?? null,
            'valid_until' => $knowledgeRecord['valid_until'] ?? null,
            'segmented_text' => $knowledgeRecord['segmented_text'] ?? ''
        ];

        // 转换为标准元数据
        return self::formatMetadata($knowledgeData);
    }

    /**
     * 从知识库记录批量创建向量数据
     *
     * @param array $knowledgeRecords 知识库记录数组
     * @param array $vectors 对应的向量数组，键为记录ID
     * @return array 向量数据数组，适用于batchInsertVectors方法
     */
    public static function createVectorBatch(array $knowledgeRecords, array $vectors): array
    {
        $batch = [];

        foreach ($knowledgeRecords as $record) {
            $id = $record['id'] ?? '';
            if (empty($id) || !isset($vectors[$id])) {
                continue;
            }

            $metadata = self::createStandardMetadata($record);

            $batch[] = [
                'id' => $id,
                'vector' => $vectors[$id],
                'metadata' => $metadata
            ];
        }

        return $batch;
    }

    /**
     * 从知识库查询条件创建MySQL向量表的查询条件
     *
     * 本方法将知识库的查询条件转换为MySQL向量表的查询条件，支持多种查询方式。
     * 特别注意：category和tags字段默认使用精确匹配，只有明确指定操作符时才使用特殊匹配方式。
     *
     * @param array $queryConditions 知识库查询条件
     * @return array MySQL向量表的查询条件
     *
     * @example 基本用法
     * ```php
     * // 简单的精确匹配
     * $conditions = VectorDBFactory::createMySQLQueryConditions([
     *     'category_id' => 5,
     *     'category' => '景点介绍',
     *     'status' => 'normal'
     * ]);
     * // 结果: ['category_id' => 5, 'category' => '景点介绍', 'status' => 'normal']
     * ```
     *
     * @example 分类的高级查询
     * ```php
     * // 使用LIKE查询分类
     * $conditions = VectorDBFactory::createMySQLQueryConditions([
     *     'category' => [
     *         'operator' => 'like',
     *         'value' => '%景点%'
     *     ]
     * ]);
     * // 结果: ['category' => ['like', '%景点%']]
     *
     * // 使用IN查询多个分类
     * $conditions = VectorDBFactory::createMySQLQueryConditions([
     *     'category' => [
     *         'operator' => 'in',
     *         'value' => ['景点介绍', '交通指南', '票务信息']
     *     ]
     * ]);
     * // 结果: ['category' => ['in', ['景点介绍', '交通指南', '票务信息']]]
     * ```
     *
     * @example 标签/关键词的高级查询
     * ```php
     * // 使用LIKE查询标签
     * $conditions = VectorDBFactory::createMySQLQueryConditions([
     *     'tags' => [
     *         'operator' => 'like',
     *         'value' => '%门票%'
     *     ]
     * ]);
     * // 结果: ['tags' => ['like', '%门票%']]
     *
     * // 使用CONTAINS查询标签（在逗号分隔的标签列表中查找）
     * $conditions = VectorDBFactory::createMySQLQueryConditions([
     *     'tags' => [
     *         'operator' => 'contains',
     *         'value' => '门票'
     *     ]
     * ]);
     * // 结果: ['tags' => ['like', '%门票%']]
     *
     * // 使用IN查询多个可能的标签
     * $conditions = VectorDBFactory::createMySQLQueryConditions([
     *     'tags' => [
     *         'operator' => 'in',
     *         'value' => ['门票', '价格', '优惠']
     *     ]
     * ]);
     * // 结果: ['tags' => ['in', ['门票', '价格', '优惠']]]
     *
     * // 使用标签数组（自动生成OR连接的LIKE条件）
     * $conditions = VectorDBFactory::createMySQLQueryConditions([
     *     'tags' => ['门票', '价格']
     * ]);
     * // 结果: ['tags' => ['like', ['%门票%', '%价格%'], 'OR']]
     * ```
     *
     * @example 时间范围查询
     * ```php
     * // 查询有效期内的数据
     * $conditions = VectorDBFactory::createMySQLQueryConditions([
     *     'valid_from' => ['operator' => '<=', 'value' => time()],
     *     'valid_until' => ['operator' => '>=', 'value' => time()]
     * ]);
     * // 结果: ['valid_from' => ['<=', 当前时间戳], 'valid_until' => ['>=', 当前时间戳]]
     * ```
     */
    public static function createMySQLQueryConditions(array $queryConditions): array
    {
        $conditions = [];

        // 处理分类ID - 通常使用精确匹配或IN查询
        if (isset($queryConditions['category_id'])) {
            // 如果是数组且指定了操作符，使用特殊处理
            if (is_array($queryConditions['category_id']) && isset($queryConditions['category_id']['operator'])) {
                $operator = $queryConditions['category_id']['operator'];
                $value = $queryConditions['category_id']['value'];
                $conditions['category_id'] = [$operator, $value];
            } else {
                // 否则使用默认的精确匹配
                $conditions['category_id'] = $queryConditions['category_id'];
            }
        }

        // 处理分类名称 - 默认使用精确匹配，但支持多种操作符
        if (isset($queryConditions['category'])) {
            if (is_array($queryConditions['category']) && isset($queryConditions['category']['operator'])) {
                // 只有明确指定operator时才使用特殊操作符
                $operator = $queryConditions['category']['operator'];
                $value = $queryConditions['category']['value'];

                if ($operator === 'like') {
                    // 模糊匹配：如 'category' => ['operator' => 'like', 'value' => '%景点%']
                    // 结果：WHERE category LIKE '%景点%'
                    $conditions['category'] = ['like', $value];
                } elseif ($operator === 'in') {
                    // IN查询：如 'category' => ['operator' => 'in', 'value' => ['景点介绍', '交通指南']]
                    // 结果：WHERE category IN ('景点介绍', '交通指南')
                    $conditions['category'] = ['in', $value];
                } else {
                    // 其他操作符直接传递：如 'category' => ['operator' => '!=', 'value' => '景点介绍']
                    // 结果：WHERE category != '景点介绍'
                    $conditions['category'] = [$operator, $value];
                }
            } else {
                // 默认使用精确匹配：如 'category' => '景点介绍'
                // 结果：WHERE category = '景点介绍'
                $conditions['category'] = $queryConditions['category'];
            }
        }

        // 处理标签/关键词 - 支持多种匹配方式
        if (isset($queryConditions['tags'])) {
            // 优先使用keywords，如果没有则使用tags
            $tags = $queryConditions['tags'] ?? '';

            if (is_array($tags) && isset($tags['operator'])) {
                // 明确指定了操作符
                $operator = $tags['operator'];
                $value = $tags['value'];

                if ($operator === 'like') {
                    // 模糊匹配：如 'tags' => ['operator' => 'like', 'value' => '%门票%']
                    // 结果：WHERE tags LIKE '%门票%'
                    $conditions['tags'] = ['like', $value];
                } elseif ($operator === 'contains') {
                    // 包含匹配 - 适用于逗号分隔的标签列表
                    // 如 'tags' => ['operator' => 'contains', 'value' => '门票']
                    // 结果：WHERE tags LIKE '%门票%'
                    $conditions['tags'] = ['like', '%' . $value . '%'];
                } elseif ($operator === 'in') {
                    // IN查询 - 适用于多个可能的标签
                    // 如 'tags' => ['operator' => 'in', 'value' => ['门票', '价格']]
                    // 结果：WHERE tags IN ('门票', '价格')
                    $conditions['tags'] = ['in', $value];
                } else {
                    // 其他操作符直接传递
                    // 如 'tags' => ['operator' => '=', 'value' => '门票']
                    // 结果：WHERE tags = '门票'
                    $conditions['tags'] = [$operator, $value];
                }
            } else if (is_array($tags) && !isset($tags['operator'])) {
                // 数组但没有指定操作符，视为标签列表，使用OR连接的LIKE条件
                // 如 'tags' => ['门票', '价格']
                // 结果：WHERE (tags LIKE '%门票%' OR tags LIKE '%价格%')
                $tagConditions = [];
                foreach ($tags as $tag) {
                    $tagConditions[] = '%' . $tag . '%';
                }

                // 使用多个LIKE条件
                if (count($tagConditions) === 1) {
                    $conditions['tags'] = ['like', $tagConditions[0]];
                } else {
                    // 这里需要根据实际的查询构建器调整
                    // 假设使用的是ThinkPHP的查询构建器
                    $conditions['tags'] = ['like', $tagConditions, 'OR'];
                }
            } else {
                // 字符串值，默认使用精确匹配
                // 如 'tags' => '门票'
                // 结果：WHERE tags = '门票'
                // 如果需要模糊匹配，应该明确指定operator:'like'
                $conditions['tags'] = $tags;
            }
        }

        // 处理状态字段 - 通常使用精确匹配
        if (isset($queryConditions['status'])) {
            if (is_array($queryConditions['status']) && isset($queryConditions['status']['operator'])) {
                $operator = $queryConditions['status']['operator'];
                $value = $queryConditions['status']['value'];
                $conditions['status'] = [$operator, $value];
            } else {
                $conditions['status'] = $queryConditions['status'];
            }
        }

        // 处理有效期开始时间 - 支持多种比较操作符
        if (isset($queryConditions['valid_from']) || isset($queryConditions['start_time'])) {
            $validFrom = $queryConditions['valid_from'] ?? $queryConditions['start_time'] ?? null;
            if ($validFrom !== null) {
                if (is_array($validFrom) && isset($validFrom['operator'])) {
                    // 如 'valid_from' => ['operator' => '>=', 'value' => 1672502400]
                    // 结果：WHERE valid_from >= 1672502400
                    $conditions['valid_from'] = [$validFrom['operator'], $validFrom['value']];
                } else {
                    // 默认使用精确匹配
                    $conditions['valid_from'] = $validFrom;
                }
            }
        }

        // 处理有效期结束时间 - 支持多种比较操作符
        if (isset($queryConditions['valid_until']) || isset($queryConditions['end_time'])) {
            $validUntil = $queryConditions['valid_until'] ?? $queryConditions['end_time'] ?? null;
            if ($validUntil !== null) {
                if (is_array($validUntil) && isset($validUntil['operator'])) {
                    // 如 'valid_until' => ['operator' => '<=', 'value' => 1704038399]
                    // 结果：WHERE valid_until <= 1704038399
                    $conditions['valid_until'] = [$validUntil['operator'], $validUntil['value']];
                } else {
                    // 默认使用精确匹配
                    $conditions['valid_until'] = $validUntil;
                }
            }
        }

        return $conditions;
    }

    /**
     * 合并向量搜索结果与知识库数据
     *
     * @param array $vectorResults 向量搜索结果
     * @param array $knowledgeRecords 知识库记录数组，键为记录ID
     * @return array 合并后的结果
     */
    public static function mergeSearchResults(array $vectorResults, array $knowledgeRecords): array
    {
        $mergedResults = [];

        foreach ($vectorResults as $result) {
            $id = $result['id'] ?? '';
            if (empty($id) || !isset($knowledgeRecords[$id])) {
                continue;
            }

            $record = $knowledgeRecords[$id];

            $mergedResults[] = [
                'id' => $id,
                'title' => $record['title'] ?? '',
                'content' => $record['content'] ?? '',
                'category' => $record['category'] ?? '',
                'category_id' => $record['category_id'] ?? 0,
                'tags' => $record['tags'] ?? '',
                'weight' => $record['weight'] ?? 0,
                'similarity' => $result['similarity'] ?? 0,
                'score' => $result['score'] ?? $result['similarity'] ?? 0,
                'metadata' => $result['metadata'] ?? []
            ];
        }

        return $mergedResults;
    }

    /**
     * 计算向量相似度
     *
     * @param array $vector1 向量1
     * @param array $vector2 向量2
     * @return float 相似度
     */
    public static function calculateSimilarity(array $vector1, array $vector2): float
    {
        // 确保两个向量维度相同
        if (count($vector1) != count($vector2)) {
            throw new \Exception("向量维度不匹配: " . count($vector1) . " vs " . count($vector2));
        }

        // 计算点积
        $dotProduct = 0;
        $magnitude1 = 0;
        $magnitude2 = 0;

        foreach ($vector1 as $i => $value) {
            $dotProduct += $value * $vector2[$i];
            $magnitude1 += $value * $value;
            $magnitude2 += $vector2[$i] * $vector2[$i];
        }

        $magnitude1 = sqrt($magnitude1);
        $magnitude2 = sqrt($magnitude2);

        // 避免除以零
        if ($magnitude1 == 0 || $magnitude2 == 0) {
            return 0;
        }

        // 计算余弦相似度
        return $dotProduct / ($magnitude1 * $magnitude2);
    }

    /**
     * 获取MySQL向量表的创建SQL
     *
     * 在FastAdmin插件中，表的创建应该通过install.sql处理，
     * 此方法仅作为接口实现，实际上不执行任何操作。
     *
     * @param string $tableName 表名(不含前缀)
     * @param string $engine 存储引擎
     * @param string $charset 字符集
     * @return string 创建表的SQL语句
     */
    public static function getMySQLTableCreateSQL(string $tableName, string $engine = 'InnoDB', string $charset = 'utf8mb4'): string
    {
        // 在FastAdmin插件中，表的创建应该通过install.sql处理
        // 此方法仅作为接口实现，返回空SQL
        return '';
    }

    /**
     * 检查MySQL向量表是否需要升级
     *
     * 在FastAdmin插件中，表的升级应该通过update.sql处理，
     * 此方法仅作为接口实现，实际上不执行任何操作。
     *
     * @param string $tableName 表名(不含前缀)
     * @return array 需要添加的字段和索引
     */
    public static function checkMySQLTableUpgrade(string $tableName): array
    {
        // 在FastAdmin插件中，表的升级应该通过update.sql处理
        // 此方法仅作为接口实现，返回空数组
        return [
            'missing_fields' => [],
            'missing_indexes' => []
        ];
    }

    /**
     * 生成MySQL向量表升级SQL
     *
     * 在FastAdmin插件中，表的升级应该通过update.sql处理，
     * 此方法仅作为接口实现，实际上不执行任何操作。
     *
     * @param string $tableName 表名(不含前缀)
     * @param array $upgrade 升级信息
     * @return array 升级SQL语句数组
     */
    public static function generateMySQLUpgradeSQL(string $tableName, array $upgrade): array
    {
        // 在FastAdmin插件中，表的升级应该通过update.sql处理
        // 此方法仅作为接口实现，返回空数组
        return [];
    }
}
