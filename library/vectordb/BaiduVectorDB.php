<?php

namespace addons\dsassistant\library\vectordb;

// 使用完全限定的命名空间，避免IDE警告
// 这些类在运行时可用，但IDE无法识别
// \think\Log, \think\Exception

/**
 * 百度智能云向量数据库实现
 * 使用百度智能云VectorDB服务
 */
class BaiduVectorDB implements VectorDBInterface
{
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];

    /**
     * API基础URL
     * @var string
     */
    protected $baseUrl = '';

    /**
     * 访问令牌
     * @var string
     */
    protected $accessToken = '';

    /**
     * 令牌过期时间
     * @var int
     */
    protected $tokenExpireTime = 0;

    /**
     * 初始化向量数据库连接
     *
     * @param array $config 配置信息
     * @return bool 是否成功
     */
    public function initialize(array $config): bool
    {
        // 检查必要的配置
        $requiredKeys = ['apiKey', 'secretKey', 'endpoint'];
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key]) || empty($config[$key])) {
                throw new \think\Exception("百度智能云VectorDB配置缺少必要参数: {$key}");
            }
        }

        $this->config = $config;
        $this->baseUrl = $config['endpoint'] ?: 'https://aip.baidubce.com';

        // 获取访问令牌
        $this->getAccessToken();

        return true;
    }

    /**
     * 创建集合/表
     *
     * @param string $collectionName 集合名称
     * @param int $dimension 向量维度
     * @param array $options 其他选项
     * @return bool 是否成功
     */
    public function createCollection(string $collectionName, int $dimension, array $options = []): bool
    {
        try {
            $params = [
                'collection_name' => $collectionName,
                'dimension' => $dimension,
                'metric_type' => $options['metricType'] ?? 'COSINE',
                'description' => $options['description'] ?? ''
            ];

            // 添加可选参数
            if (isset($options['indexType'])) {
                $params['index_type'] = $options['indexType'];
            }

            $response = $this->sendRequest('POST', '/rpc/2.0/vector_db/v1/collections/create', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            \think\Log::info("成功创建百度智能云VectorDB集合: {$collectionName}");
            return true;
        } catch (\Exception $e) {
            \think\Log::error("创建百度智能云VectorDB集合失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function insertVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        try {
            // 将知识库数据转换为标准元数据格式
            $standardMetadata = VectorDBFactory::formatMetadata($metadata);

            $document = [
                'id' => $id,
                'vector' => $vector,
                'metadata' => $standardMetadata
            ];

            $params = [
                'collection_name' => $collectionName,
                'documents' => [$document]
            ];

            $response = $this->sendRequest('POST', '/rpc/2.0/vector_db/v1/documents/insert', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("插入向量到百度智能云VectorDB失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param array $vectors 向量数据数组，每个元素包含id、vector和metadata
     * @return bool 是否成功
     */
    public function batchInsertVectors(string $collectionName, array $vectors): bool
    {
        try {
            $documents = [];

            foreach ($vectors as $item) {
                // 将知识库数据转换为标准元数据格式
                $standardMetadata = VectorDBFactory::formatMetadata($item['metadata'] ?? []);

                $documents[] = [
                    'id' => $item['id'],
                    'vector' => $item['vector'],
                    'metadata' => $standardMetadata
                ];
            }

            // 分批处理，每批最多100个文档
            $batches = array_chunk($documents, 100);

            foreach ($batches as $batch) {
                $params = [
                    'collection_name' => $collectionName,
                    'documents' => $batch
                ];

                $response = $this->sendRequest('POST', '/rpc/2.0/vector_db/v1/documents/insert', $params);

                if (isset($response['error_code'])) {
                    throw new \think\Exception($response['error_msg'] ?? '未知错误');
                }
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("批量插入向量到百度智能云VectorDB失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function updateVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        try {
            // 将知识库数据转换为标准元数据格式
            $standardMetadata = VectorDBFactory::formatMetadata($metadata);

            $document = [
                'id' => $id,
                'vector' => $vector,
                'metadata' => $standardMetadata
            ];

            $params = [
                'collection_name' => $collectionName,
                'documents' => [$document]
            ];

            $response = $this->sendRequest('POST', '/rpc/2.0/vector_db/v1/documents/update', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("更新百度智能云VectorDB向量失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return bool 是否成功
     */
    public function deleteVector(string $collectionName, string $id): bool
    {
        try {
            $params = [
                'collection_name' => $collectionName,
                'ids' => [$id]
            ];

            $response = $this->sendRequest('POST', '/rpc/2.0/vector_db/v1/documents/delete', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("从百度智能云VectorDB删除向量失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 搜索相似向量
     *
     * 使用VectorDBFactory中的convertFilter方法处理过滤条件，
     * 支持对元数据字段的过滤。
     *
     * @param string $collectionName 集合名称
     * @param array $queryVector 查询向量
     * @param int $topK 返回结果数量
     * @param array $filter 过滤条件
     * @return array 搜索结果
     */
    public function search(string $collectionName, array $queryVector, int $topK = 10, array $filter = []): array
    {
        try {
            $params = [
                'collection_name' => $collectionName,
                'vector' => $queryVector,
                'topk' => $topK
            ];

            // 转换过滤条件为百度智能云VectorDB查询条件
            if (!empty($filter)) {
                $convertedFilter = VectorDBFactory::convertFilter($filter, VectorDBFactory::DB_TYPE_BAIDU);
                $params['filter'] = $this->buildFilter($convertedFilter);
            }

            $response = $this->sendRequest('POST', '/rpc/2.0/vector_db/v1/search', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            $results = [];
            if (isset($response['documents']) && !empty($response['documents'])) {
                foreach ($response['documents'] as $doc) {
                    $results[] = [
                        'id' => $doc['id'],
                        'similarity' => $doc['score'],
                        'metadata' => $doc['metadata'] ?? []
                    ];
                }
            }

            return $results;
        } catch (\Exception $e) {
            \think\Log::error("在百度智能云VectorDB中搜索向量失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return array|null 向量数据
     */
    public function getVector(string $collectionName, string $id): ?array
    {
        try {
            $params = [
                'collection_name' => $collectionName,
                'ids' => [$id]
            ];

            $response = $this->sendRequest('POST', '/rpc/2.0/vector_db/v1/documents/query', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            if (empty($response['documents'])) {
                return null;
            }

            $document = $response['documents'][0];

            return [
                'id' => $document['id'],
                'vector' => $document['vector'],
                'metadata' => $document['metadata'] ?? []
            ];
        } catch (\Exception $e) {
            \think\Log::error("从百度智能云VectorDB获取向量失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取集合信息
     *
     * @param string $collectionName 集合名称
     * @return array 集合信息
     */
    public function getCollectionInfo(string $collectionName): array
    {
        try {
            $params = [
                'collection_name' => $collectionName
            ];

            $response = $this->sendRequest('POST', '/rpc/2.0/vector_db/v1/collections/get', $params);

            if (isset($response['error_code'])) {
                throw new \think\Exception($response['error_msg'] ?? '未知错误');
            }

            return [
                'name' => $response['collection_name'],
                'dimension' => $response['dimension'],
                'count' => $response['document_count'] ?? 0,
                'metricType' => $response['metric_type'] ?? '',
                'engine' => 'BaiduVectorDB'
            ];
        } catch (\Exception $e) {
            \think\Log::error("获取百度智能云VectorDB集合信息失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 计算两个向量的相似度
     *
     * 使用VectorDBFactory中的calculateSimilarity方法，
     * 避免代码重复。
     *
     * @param array $vector1 向量1
     * @param array $vector2 向量2
     * @return float 相似度
     */
    public function calculateSimilarity(array $vector1, array $vector2): float
    {
        // 使用VectorDBFactory中的方法
        return VectorDBFactory::calculateSimilarity($vector1, $vector2);
    }

    /**
     * 构建过滤条件
     *
     * @param array $filter 过滤条件
     * @return array 过滤条件数组
     */
    protected function buildFilter(array $filter): array
    {
        $result = [];

        foreach ($filter as $field => $value) {
            if (is_array($value) && isset($value['operator']) && isset($value['value'])) {
                switch ($value['operator']) {
                    case '=':
                        $result[$field] = ['eq' => $value['value']];
                        break;
                    case '>':
                        $result[$field] = ['gt' => $value['value']];
                        break;
                    case '>=':
                        $result[$field] = ['gte' => $value['value']];
                        break;
                    case '<':
                        $result[$field] = ['lt' => $value['value']];
                        break;
                    case '<=':
                        $result[$field] = ['lte' => $value['value']];
                        break;
                    case 'in':
                        $result[$field] = ['in' => (array)$value['value']];
                        break;
                    case 'like':
                        $result[$field] = ['like' => $value['value']];
                        break;
                }
            } else {
                $result[$field] = ['eq' => $value];
            }
        }

        return $result;
    }

    /**
     * 获取访问令牌
     *
     * @return string 访问令牌
     */
    protected function getAccessToken(): string
    {
        // 如果令牌未过期，直接返回
        if ($this->accessToken && $this->tokenExpireTime > time()) {
            return $this->accessToken;
        }

        $apiKey = $this->config['apiKey'];
        $secretKey = $this->config['secretKey'];
        $url = 'https://aip.baidubce.com/oauth/2.0/token';

        $params = [
            'grant_type' => 'client_credentials',
            'client_id' => $apiKey,
            'client_secret' => $secretKey
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url . '?' . http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($httpCode != 200) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \think\Exception("获取百度访问令牌失败: HTTP {$httpCode}, {$error}");
        }

        curl_close($ch);

        $result = json_decode($response, true);

        if (isset($result['access_token'])) {
            $this->accessToken = $result['access_token'];
            $this->tokenExpireTime = time() + $result['expires_in'] - 60; // 提前60秒过期
            return $this->accessToken;
        } else {
            throw new \think\Exception("获取百度访问令牌失败: " . ($result['error_description'] ?? '未知错误'));
        }
    }

    /**
     * 发送API请求
     *
     * @param string $method HTTP方法
     * @param string $path 请求路径
     * @param array $params 请求参数
     * @return array 响应结果
     */
    protected function sendRequest(string $method, string $path, array $params = []): array
    {
        $url = $this->baseUrl . $path;
        $accessToken = $this->getAccessToken();

        $url .= (strpos($url, '?') === false ? '?' : '&') . 'access_token=' . $accessToken;

        $headers = [
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        if ($method == 'POST' || $method == 'PUT') {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($httpCode >= 400) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \think\Exception("百度API请求失败: HTTP {$httpCode}, {$error}");
        }

        curl_close($ch);

        return json_decode($response, true) ?: [];
    }
}
