<?php

namespace addons\dsassistant\library\vectordb;

// 使用完全限定的命名空间，避免IDE警告
// 这些类在运行时可用，但IDE无法识别
// \think\Log, \think\Exception

/**
 * 阿里云向量数据库实现
 * 使用阿里云VectorSearch服务
 */
class AliyunVectorDB implements VectorDBInterface
{
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];

    /**
     * API基础URL
     * @var string
     */
    protected $baseUrl = '';

    /**
     * 初始化向量数据库连接
     *
     * @param array $config 配置信息
     * @return bool 是否成功
     */
    public function initialize(array $config): bool
    {
        // 检查必要的配置
        $requiredKeys = ['accessKeyId', 'accessKeySecret', 'endpoint', 'instanceId'];
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key]) || empty($config[$key])) {
                throw new \think\Exception("阿里云VectorSearch配置缺少必要参数: {$key}");
            }
        }

        $this->config = $config;
        $this->baseUrl = 'https://' . $config['instanceId'] . '.' . $config['endpoint'];

        return true;
    }

    /**
     * 创建集合/表
     *
     * @param string $collectionName 集合名称
     * @param int $dimension 向量维度
     * @param array $options 其他选项
     * @return bool 是否成功
     */
    public function createCollection(string $collectionName, int $dimension, array $options = []): bool
    {
        try {
            $params = [
                'collection_name' => $collectionName,
                'dimension' => $dimension,
                'metric_type' => $options['metricType'] ?? 'COSINE'
            ];

            // 添加可选参数
            if (isset($options['indexType'])) {
                $params['index_type'] = $options['indexType'];
            }
            if (isset($options['nprobe'])) {
                $params['nprobe'] = $options['nprobe'];
            }
            if (isset($options['ncentroids'])) {
                $params['ncentroids'] = $options['ncentroids'];
            }

            $response = $this->sendRequest('POST', '/v1/collections', $params);

            if (isset($response['code']) && $response['code'] != 200) {
                throw new \think\Exception($response['message'] ?? '未知错误');
            }

            \think\Log::info("成功创建阿里云VectorSearch集合: {$collectionName}");
            return true;
        } catch (\Exception $e) {
            \think\Log::error("创建阿里云VectorSearch集合失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function insertVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        try {
            // 将知识库数据转换为标准元数据格式
            $standardMetadata = VectorDBFactory::formatMetadata($metadata);

            $document = [
                'id' => $id,
                'vector' => $vector
            ];

            // 添加元数据
            if (!empty($standardMetadata)) {
                foreach ($standardMetadata as $key => $value) {
                    $document[$key] = $value;
                }
            }

            $params = [
                'documents' => [$document]
            ];

            $response = $this->sendRequest('POST', "/v1/collections/{$collectionName}/documents", $params);

            if (isset($response['code']) && $response['code'] != 200) {
                throw new \think\Exception($response['message'] ?? '未知错误');
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("插入向量到阿里云VectorSearch失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param array $vectors 向量数据数组，每个元素包含id、vector和metadata
     * @return bool 是否成功
     */
    public function batchInsertVectors(string $collectionName, array $vectors): bool
    {
        try {
            $documents = [];

            foreach ($vectors as $item) {
                // 将知识库数据转换为标准元数据格式
                $standardMetadata = VectorDBFactory::formatMetadata($item['metadata'] ?? []);

                $document = [
                    'id' => $item['id'],
                    'vector' => $item['vector']
                ];

                // 添加元数据
                if (!empty($standardMetadata)) {
                    foreach ($standardMetadata as $key => $value) {
                        $document[$key] = $value;
                    }
                }

                $documents[] = $document;
            }

            // 分批处理，每批最多100个文档
            $batches = array_chunk($documents, 100);

            foreach ($batches as $batch) {
                $params = [
                    'documents' => $batch
                ];

                $response = $this->sendRequest('POST', "/v1/collections/{$collectionName}/documents", $params);

                if (isset($response['code']) && $response['code'] != 200) {
                    throw new \think\Exception($response['message'] ?? '未知错误');
                }
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("批量插入向量到阿里云VectorSearch失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function updateVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        // 阿里云VectorSearch的插入接口会自动更新已存在的文档
        return $this->insertVector($collectionName, $id, $vector, $metadata);
    }

    /**
     * 删除向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return bool 是否成功
     */
    public function deleteVector(string $collectionName, string $id): bool
    {
        try {
            $params = [
                'ids' => [$id]
            ];

            $response = $this->sendRequest('POST', "/v1/collections/{$collectionName}/documents/delete", $params);

            if (isset($response['code']) && $response['code'] != 200) {
                throw new \think\Exception($response['message'] ?? '未知错误');
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("从阿里云VectorSearch删除向量失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 搜索相似向量
     *
     * 使用VectorDBFactory中的convertFilter方法处理过滤条件，
     * 支持对元数据字段的过滤。
     *
     * @param string $collectionName 集合名称
     * @param array $queryVector 查询向量
     * @param int $topK 返回结果数量
     * @param array $filter 过滤条件
     * @return array 搜索结果
     */
    public function search(string $collectionName, array $queryVector, int $topK = 10, array $filter = []): array
    {
        try {
            $params = [
                'vectors' => [$queryVector],
                'topk' => $topK
            ];

            // 转换过滤条件为阿里云VectorSearch查询条件
            if (!empty($filter)) {
                $convertedFilter = VectorDBFactory::convertFilter($filter, VectorDBFactory::DB_TYPE_ALIYUN);
                $params['filter'] = $this->buildFilter($convertedFilter);
            }

            $response = $this->sendRequest('POST', "/v1/collections/{$collectionName}/vectors/search", $params);

            if (isset($response['code']) && $response['code'] != 200) {
                throw new \think\Exception($response['message'] ?? '未知错误');
            }

            $results = [];
            if (isset($response['results']) && !empty($response['results'])) {
                foreach ($response['results'][0] as $result) {
                    $metadata = [];
                    foreach ($result as $key => $value) {
                        if ($key != 'id' && $key != 'score' && $key != 'vector') {
                            $metadata[$key] = $value;
                        }
                    }

                    $results[] = [
                        'id' => $result['id'],
                        'similarity' => $result['score'],
                        'metadata' => $metadata
                    ];
                }
            }

            return $results;
        } catch (\Exception $e) {
            \think\Log::error("在阿里云VectorSearch中搜索向量失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return array|null 向量数据
     */
    public function getVector(string $collectionName, string $id): ?array
    {
        try {
            $params = [
                'ids' => [$id]
            ];

            $response = $this->sendRequest('POST', "/v1/collections/{$collectionName}/documents/query", $params);

            if (isset($response['code']) && $response['code'] != 200) {
                throw new \think\Exception($response['message'] ?? '未知错误');
            }

            if (empty($response['documents'])) {
                return null;
            }

            $document = $response['documents'][0];
            $metadata = [];

            foreach ($document as $key => $value) {
                if ($key != 'id' && $key != 'vector') {
                    $metadata[$key] = $value;
                }
            }

            return [
                'id' => $document['id'],
                'vector' => $document['vector'],
                'metadata' => $metadata
            ];
        } catch (\Exception $e) {
            \think\Log::error("从阿里云VectorSearch获取向量失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取集合信息
     *
     * @param string $collectionName 集合名称
     * @return array 集合信息
     */
    public function getCollectionInfo(string $collectionName): array
    {
        try {
            $response = $this->sendRequest('GET', "/v1/collections/{$collectionName}");

            if (isset($response['code']) && $response['code'] != 200) {
                throw new \think\Exception($response['message'] ?? '未知错误');
            }

            return [
                'name' => $response['collection_name'],
                'dimension' => $response['dimension'],
                'count' => $response['document_count'] ?? 0,
                'indexType' => $response['index_type'] ?? '',
                'metricType' => $response['metric_type'] ?? '',
                'engine' => 'AliyunVectorSearch'
            ];
        } catch (\Exception $e) {
            \think\Log::error("获取阿里云VectorSearch集合信息失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 计算两个向量的相似度
     *
     * 使用VectorDBFactory中的calculateSimilarity方法，
     * 避免代码重复。
     *
     * @param array $vector1 向量1
     * @param array $vector2 向量2
     * @return float 相似度
     */
    public function calculateSimilarity(array $vector1, array $vector2): float
    {
        // 使用VectorDBFactory中的方法
        return VectorDBFactory::calculateSimilarity($vector1, $vector2);
    }

    /**
     * 构建过滤条件
     *
     * @param array $filter 过滤条件
     * @return string 过滤条件字符串
     */
    protected function buildFilter(array $filter): string
    {
        $conditions = [];

        foreach ($filter as $field => $value) {
            if (is_array($value) && isset($value['operator']) && isset($value['value'])) {
                switch ($value['operator']) {
                    case '=':
                        $conditions[] = "{$field} = " . $this->formatFilterValue($value['value']);
                        break;
                    case '>':
                        $conditions[] = "{$field} > " . $this->formatFilterValue($value['value']);
                        break;
                    case '>=':
                        $conditions[] = "{$field} >= " . $this->formatFilterValue($value['value']);
                        break;
                    case '<':
                        $conditions[] = "{$field} < " . $this->formatFilterValue($value['value']);
                        break;
                    case '<=':
                        $conditions[] = "{$field} <= " . $this->formatFilterValue($value['value']);
                        break;
                    case 'in':
                        $values = array_map([$this, 'formatFilterValue'], (array)$value['value']);
                        $conditions[] = "{$field} in [" . implode(', ', $values) . "]";
                        break;
                    case 'like':
                        $conditions[] = "{$field} like " . $this->formatFilterValue($value['value']);
                        break;
                }
            } else {
                $conditions[] = "{$field} = " . $this->formatFilterValue($value);
            }
        }

        return implode(' AND ', $conditions);
    }

    /**
     * 格式化过滤值
     *
     * @param mixed $value 值
     * @return string 格式化后的值
     */
    protected function formatFilterValue($value): string
    {
        if (is_string($value)) {
            return "'{$value}'";
        } elseif (is_bool($value)) {
            return $value ? 'true' : 'false';
        } elseif (is_null($value)) {
            return 'null';
        } else {
            return (string)$value;
        }
    }

    /**
     * 发送API请求
     *
     * @param string $method HTTP方法
     * @param string $path 请求路径
     * @param array $params 请求参数
     * @return array 响应结果
     */
    protected function sendRequest(string $method, string $path, array $params = []): array
    {
        $accessKeyId = $this->config['accessKeyId'];
        $accessKeySecret = $this->config['accessKeySecret'];
        $url = $this->baseUrl . $path;

        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        // 计算签名
        $timestamp = gmdate('Y-m-d\TH:i:s\Z');
        $nonce = uniqid();

        $contentMd5 = '';
        if ($method == 'POST' || $method == 'PUT') {
            $content = json_encode($params);
            $contentMd5 = base64_encode(md5($content, true));
        }

        $stringToSign = $method . "\n"
                      . $contentMd5 . "\n"
                      . "application/json\n"
                      . $timestamp . "\n"
                      . $nonce . "\n"
                      . $path;

        $signature = base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret, true));

        $headers[] = 'Date: ' . $timestamp;
        $headers[] = 'x-acs-signature-nonce: ' . $nonce;
        $headers[] = 'x-acs-signature-method: HMAC-SHA1';
        $headers[] = 'x-acs-signature-version: 1.0';
        $headers[] = 'Authorization: acs ' . $accessKeyId . ':' . $signature;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        if ($method == 'POST' || $method == 'PUT') {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($httpCode >= 400) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \think\Exception("阿里云API请求失败: HTTP {$httpCode}, {$error}");
        }

        curl_close($ch);

        return json_decode($response, true) ?: [];
    }
}
