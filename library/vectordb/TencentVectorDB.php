<?php

namespace addons\dsassistant\library\vectordb;

// 使用完全限定的命名空间，避免IDE警告
// 这些类在运行时可用，但IDE无法识别
// \think\Log, \think\Exception

/**
 * 腾讯云向量数据库实现
 * 使用腾讯云VectorDB服务
 */
class TencentVectorDB implements VectorDBInterface
{
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];

    /**
     * API基础URL
     * @var string
     */
    protected $baseUrl = 'https://vectordb.tencentcloudapi.com';

    /**
     * 初始化向量数据库连接
     *
     * @param array $config 配置信息
     * @return bool 是否成功
     */
    public function initialize(array $config): bool
    {
        // 检查必要的配置
        $requiredKeys = ['secretId', 'secretKey', 'region'];
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key]) || empty($config[$key])) {
                throw new \think\Exception("腾讯云VectorDB配置缺少必要参数: {$key}");
            }
        }

        $this->config = $config;
        return true;
    }

    /**
     * 创建集合/表
     *
     * @param string $collectionName 集合名称
     * @param int $dimension 向量维度
     * @param array $options 其他选项
     * @return bool 是否成功
     */
    public function createCollection(string $collectionName, int $dimension, array $options = []): bool
    {
        try {
            $params = [
                'CollectionName' => $collectionName,
                'Dimension' => $dimension,
                'IndexType' => $options['indexType'] ?? 'HNSW',
                'MetricType' => $options['metricType'] ?? 'COSINE',
                'DatabaseId' => $options['databaseId'] ?? $this->config['databaseId'] ?? '',
                'Description' => $options['description'] ?? ''
            ];

            // 添加可选参数
            if (isset($options['shardCount'])) {
                $params['ShardCount'] = $options['shardCount'];
            }
            if (isset($options['replicaCount'])) {
                $params['ReplicaCount'] = $options['replicaCount'];
            }

            $response = $this->sendRequest('CreateCollection', $params);

            if (isset($response['Response']['Error'])) {
                throw new \think\Exception($response['Response']['Error']['Message']);
            }

            \think\Log::info("成功创建腾讯云VectorDB集合: {$collectionName}");
            return true;
        } catch (\Exception $e) {
            \think\Log::error("创建腾讯云VectorDB集合失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function insertVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        try {
            // 将知识库数据转换为标准元数据格式
            $standardMetadata = VectorDBFactory::formatMetadata($metadata);

            $params = [
                'CollectionName' => $collectionName,
                'DatabaseId' => $this->config['databaseId'] ?? '',
                'Documents' => [
                    [
                        'Id' => $id,
                        'Vector' => $vector,
                        'Metadata' => $standardMetadata
                    ]
                ]
            ];

            $response = $this->sendRequest('UpsertDocument', $params);

            if (isset($response['Response']['Error'])) {
                throw new \think\Exception($response['Response']['Error']['Message']);
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("插入向量到腾讯云VectorDB失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param array $vectors 向量数据数组，每个元素包含id、vector和metadata
     * @return bool 是否成功
     */
    public function batchInsertVectors(string $collectionName, array $vectors): bool
    {
        try {
            $documents = [];
            foreach ($vectors as $item) {
                // 将知识库数据转换为标准元数据格式
                $standardMetadata = VectorDBFactory::formatMetadata($item['metadata'] ?? []);

                $documents[] = [
                    'Id' => $item['id'],
                    'Vector' => $item['vector'],
                    'Metadata' => $standardMetadata
                ];
            }

            $params = [
                'CollectionName' => $collectionName,
                'DatabaseId' => $this->config['databaseId'] ?? '',
                'Documents' => $documents
            ];

            $response = $this->sendRequest('UpsertDocument', $params);

            if (isset($response['Response']['Error'])) {
                throw new \think\Exception($response['Response']['Error']['Message']);
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("批量插入向量到腾讯云VectorDB失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，
     * 自动转换为标准元数据格式。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function updateVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        // 腾讯云VectorDB的UpsertDocument接口会自动更新已存在的文档
        return $this->insertVector($collectionName, $id, $vector, $metadata);
    }

    /**
     * 删除向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return bool 是否成功
     */
    public function deleteVector(string $collectionName, string $id): bool
    {
        try {
            $params = [
                'CollectionName' => $collectionName,
                'DatabaseId' => $this->config['databaseId'] ?? '',
                'DocumentIds' => [$id]
            ];

            $response = $this->sendRequest('DeleteDocument', $params);

            if (isset($response['Response']['Error'])) {
                throw new \think\Exception($response['Response']['Error']['Message']);
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("从腾讯云VectorDB删除向量失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 搜索相似向量
     *
     * 使用VectorDBFactory中的convertFilter方法处理过滤条件，
     * 支持对元数据字段的过滤。
     *
     * @param string $collectionName 集合名称
     * @param array $queryVector 查询向量
     * @param int $topK 返回结果数量
     * @param array $filter 过滤条件
     * @return array 搜索结果
     */
    public function search(string $collectionName, array $queryVector, int $topK = 10, array $filter = []): array
    {
        try {
            $params = [
                'CollectionName' => $collectionName,
                'DatabaseId' => $this->config['databaseId'] ?? '',
                'Limit' => $topK,
                'Vector' => $queryVector
            ];

            // 转换过滤条件为腾讯云VectorDB查询条件
            if (!empty($filter)) {
                $convertedFilter = VectorDBFactory::convertFilter($filter, VectorDBFactory::DB_TYPE_TENCENT);
                $params['Filter'] = $this->buildFilter($convertedFilter);
            }

            $response = $this->sendRequest('SearchDocument', $params);

            if (isset($response['Response']['Error'])) {
                throw new \think\Exception($response['Response']['Error']['Message']);
            }

            $results = [];
            if (isset($response['Response']['Documents'])) {
                foreach ($response['Response']['Documents'] as $doc) {
                    $results[] = [
                        'id' => $doc['Id'],
                        'similarity' => $doc['Score'],
                        'metadata' => $doc['Metadata'] ?? null
                    ];
                }
            }

            return $results;
        } catch (\Exception $e) {
            \think\Log::error("在腾讯云VectorDB中搜索向量失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return array|null 向量数据
     */
    public function getVector(string $collectionName, string $id): ?array
    {
        try {
            $params = [
                'CollectionName' => $collectionName,
                'DatabaseId' => $this->config['databaseId'] ?? '',
                'DocumentIds' => [$id]
            ];

            $response = $this->sendRequest('GetDocument', $params);

            if (isset($response['Response']['Error'])) {
                throw new \think\Exception($response['Response']['Error']['Message']);
            }

            if (empty($response['Response']['Documents'])) {
                return null;
            }

            $doc = $response['Response']['Documents'][0];
            return [
                'id' => $doc['Id'],
                'vector' => $doc['Vector'],
                'metadata' => $doc['Metadata'] ?? null
            ];
        } catch (\Exception $e) {
            \think\Log::error("从腾讯云VectorDB获取向量失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取集合信息
     *
     * @param string $collectionName 集合名称
     * @return array 集合信息
     */
    public function getCollectionInfo(string $collectionName): array
    {
        try {
            $params = [
                'CollectionName' => $collectionName,
                'DatabaseId' => $this->config['databaseId'] ?? ''
            ];

            $response = $this->sendRequest('DescribeCollection', $params);

            if (isset($response['Response']['Error'])) {
                throw new \think\Exception($response['Response']['Error']['Message']);
            }

            return [
                'name' => $response['Response']['Collection']['CollectionName'],
                'dimension' => $response['Response']['Collection']['Dimension'],
                'indexType' => $response['Response']['Collection']['IndexType'],
                'metricType' => $response['Response']['Collection']['MetricType'],
                'shardCount' => $response['Response']['Collection']['ShardCount'],
                'replicaCount' => $response['Response']['Collection']['ReplicaCount'],
                'count' => $response['Response']['Collection']['DocumentCount'],
                'engine' => 'TencentVectorDB'
            ];
        } catch (\Exception $e) {
            \think\Log::error("获取腾讯云VectorDB集合信息失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 计算两个向量的相似度
     *
     * 使用VectorDBFactory中的calculateSimilarity方法，
     * 避免代码重复。
     *
     * @param array $vector1 向量1
     * @param array $vector2 向量2
     * @return float 相似度
     */
    public function calculateSimilarity(array $vector1, array $vector2): float
    {
        // 使用VectorDBFactory中的方法
        return VectorDBFactory::calculateSimilarity($vector1, $vector2);
    }

    /**
     * 构建过滤条件
     *
     * @param array $filter 过滤条件
     * @return string 过滤条件字符串
     */
    protected function buildFilter(array $filter): string
    {
        $conditions = [];

        foreach ($filter as $field => $value) {
            if (is_array($value) && isset($value['operator']) && isset($value['value'])) {
                switch ($value['operator']) {
                    case '=':
                        $conditions[] = "{$field} = " . $this->formatFilterValue($value['value']);
                        break;
                    case '>':
                        $conditions[] = "{$field} > " . $this->formatFilterValue($value['value']);
                        break;
                    case '>=':
                        $conditions[] = "{$field} >= " . $this->formatFilterValue($value['value']);
                        break;
                    case '<':
                        $conditions[] = "{$field} < " . $this->formatFilterValue($value['value']);
                        break;
                    case '<=':
                        $conditions[] = "{$field} <= " . $this->formatFilterValue($value['value']);
                        break;
                    case 'in':
                        $values = array_map([$this, 'formatFilterValue'], (array)$value['value']);
                        $conditions[] = "{$field} IN (" . implode(', ', $values) . ")";
                        break;
                    case 'like':
                        $conditions[] = "{$field} LIKE " . $this->formatFilterValue($value['value']);
                        break;
                }
            } else {
                $conditions[] = "{$field} = " . $this->formatFilterValue($value);
            }
        }

        return implode(' AND ', $conditions);
    }

    /**
     * 格式化过滤值
     *
     * @param mixed $value 值
     * @return string 格式化后的值
     */
    protected function formatFilterValue($value): string
    {
        if (is_string($value)) {
            return "'{$value}'";
        } elseif (is_bool($value)) {
            return $value ? 'true' : 'false';
        } elseif (is_null($value)) {
            return 'null';
        } else {
            return (string)$value;
        }
    }

    /**
     * 发送API请求
     *
     * @param string $action 操作名称
     * @param array $params 请求参数
     * @return array 响应结果
     */
    protected function sendRequest(string $action, array $params): array
    {
        $secretId = $this->config['secretId'];
        $secretKey = $this->config['secretKey'];
        $region = $this->config['region'];
        $service = 'vectordb';
        $version = '2022-05-13';
        $timestamp = time();
        $date = gmdate('Y-m-d', $timestamp);

        // 1. 拼接规范请求串
        $httpRequestMethod = 'POST';
        $canonicalUri = '/';
        $canonicalQueryString = '';
        $canonicalHeaders = "content-type:application/json\n"
                          . "host:vectordb.tencentcloudapi.com\n"
                          . "x-tc-action:" . strtolower($action) . "\n";
        $signedHeaders = 'content-type;host;x-tc-action';
        $payload = json_encode($params);
        $hashedRequestPayload = hash('SHA256', $payload);
        $canonicalRequest = $httpRequestMethod . "\n"
                         . $canonicalUri . "\n"
                         . $canonicalQueryString . "\n"
                         . $canonicalHeaders . "\n"
                         . $signedHeaders . "\n"
                         . $hashedRequestPayload;

        // 2. 拼接待签名字符串
        $algorithm = 'TC3-HMAC-SHA256';
        $requestTimestamp = $timestamp;
        $credentialScope = $date . '/' . $service . '/tc3_request';
        $hashedCanonicalRequest = hash('SHA256', $canonicalRequest);
        $stringToSign = $algorithm . "\n"
                      . $requestTimestamp . "\n"
                      . $credentialScope . "\n"
                      . $hashedCanonicalRequest;

        // 3. 计算签名
        $secretDate = hash_hmac('SHA256', $date, 'TC3' . $secretKey, true);
        $secretService = hash_hmac('SHA256', $service, $secretDate, true);
        $secretSigning = hash_hmac('SHA256', 'tc3_request', $secretService, true);
        $signature = hash_hmac('SHA256', $stringToSign, $secretSigning);

        // 4. 拼接 Authorization
        $authorization = $algorithm
                       . ' Credential=' . $secretId . '/' . $credentialScope
                       . ', SignedHeaders=' . $signedHeaders
                       . ', Signature=' . $signature;

        // 5. 发送请求
        $headers = [
            'Authorization: ' . $authorization,
            'Content-Type: application/json',
            'Host: vectordb.tencentcloudapi.com',
            'X-TC-Action: ' . $action,
            'X-TC-Timestamp: ' . $timestamp,
            'X-TC-Version: ' . $version,
            'X-TC-Region: ' . $region
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->baseUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($httpCode != 200) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \think\Exception("腾讯云API请求失败: HTTP {$httpCode}, {$error}");
        }

        curl_close($ch);

        return json_decode($response, true);
    }
}
