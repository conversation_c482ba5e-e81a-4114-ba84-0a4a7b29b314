<?php

namespace addons\dsassistant\library\vectordb;

/**
 * 向量数据库接口
 * 定义所有向量数据库实现必须提供的方法
 */
interface VectorDBInterface
{
    /**
     * 初始化向量数据库连接
     *
     * @param array $config 配置信息
     * @return bool 是否成功
     */
    public function initialize(array $config): bool;

    /**
     * 创建集合/表
     *
     * @param string $collectionName 集合名称
     * @param int $dimension 向量维度
     * @param array $options 其他选项
     * @return bool 是否成功
     */
    public function createCollection(string $collectionName, int $dimension, array $options = []): bool;

    /**
     * 插入向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据
     * @return bool 是否成功
     */
    public function insertVector(string $collectionName, string $id, array $vector, array $metadata = []): bool;

    /**
     * 批量插入向量
     *
     * @param string $collectionName 集合名称
     * @param array $vectors 向量数据数组，每个元素包含id、vector和metadata
     * @return bool 是否成功
     */
    public function batchInsertVectors(string $collectionName, array $vectors): bool;

    /**
     * 更新向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据
     * @return bool 是否成功
     */
    public function updateVector(string $collectionName, string $id, array $vector, array $metadata = []): bool;

    /**
     * 删除向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return bool 是否成功
     */
    public function deleteVector(string $collectionName, string $id): bool;

    /**
     * 搜索相似向量
     *
     * @param string $collectionName 集合名称
     * @param array $queryVector 查询向量
     * @param int $topK 返回结果数量
     * @param array $filter 过滤条件
     * @return array 搜索结果
     */
    public function search(string $collectionName, array $queryVector, int $topK = 10, array $filter = []): array;

    /**
     * 获取向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return array|null 向量数据
     */
    public function getVector(string $collectionName, string $id): ?array;

    /**
     * 获取集合信息
     *
     * @param string $collectionName 集合名称
     * @return array 集合信息
     */
    public function getCollectionInfo(string $collectionName): array;

    /**
     * 计算两个向量的相似度
     *
     * @param array $vector1 向量1
     * @param array $vector2 向量2
     * @return float 相似度
     */
    public function calculateSimilarity(array $vector1, array $vector2): float;
}
