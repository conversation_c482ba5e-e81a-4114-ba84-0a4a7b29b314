<?php

namespace addons\dsassistant\library\vectordb;

// 使用完全限定的命名空间，避免IDE警告
// 这些类在运行时可用，但IDE无法识别
// \think\Db, \think\Log, \think\Config, \think\Exception

/**
 * MySQL向量数据库实现
 * 使用MySQL存储和查询向量数据
 */
class MySQLVectorDB implements VectorDBInterface
{
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];

    /**
     * 初始化向量数据库连接
     *
     * @param array $config 配置信息
     * @return bool 是否成功
     */
    public function initialize(array $config): bool
    {
        $this->config = $config;
        return true;
    }

    /**
     * 创建集合/表
     *
     * 在FastAdmin插件中，表的创建和升级应该通过install.sql和update.sql处理，
     * 此方法仅作为接口实现，实际上不执行任何操作。
     *
     * @param string $collectionName 集合名称
     * @param int $dimension 向量维度
     * @param array $options 其他选项
     * @return bool 是否成功
     */
    public function createCollection(string $collectionName, int $dimension, array $options = []): bool
    {
        // 在FastAdmin插件中，表的创建应该通过install.sql处理
        // 此方法仅作为接口实现，实际上不执行任何操作
        return true;
    }

    /**
     * 插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，并提取冗余字段以优化查询性能。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function insertVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        try {
            $now = time();

            // 将知识库数据转换为标准元数据格式
            $standardMetadata = VectorDBFactory::formatMetadata($metadata);

            // 提取MySQL表的冗余字段
            $fields = VectorDBFactory::extractMySQLFields($standardMetadata);

            // 构建数据
            $data = [
                'id' => $id,
                'vector' => json_encode($vector),
                'metadata' => json_encode($standardMetadata),
                'createtime' => $now,
                'updatetime' => $now
            ];

            // 合并冗余字段
            $data = array_merge($data, $fields);

            // 插入数据
            \think\Db::name($collectionName)->insert($data);
            return true;
        } catch (\Exception $e) {
            \think\Log::error("插入向量到 {$collectionName} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量插入向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，并提取冗余字段以优化查询性能。
     *
     * @param string $collectionName 集合名称
     * @param array $vectors 向量数据数组，每个元素包含id、vector和metadata
     * @return bool 是否成功
     */
    public function batchInsertVectors(string $collectionName, array $vectors): bool
    {
        try {
            $now = time();
            $data = [];

            foreach ($vectors as $item) {
                // 将知识库数据转换为标准元数据格式
                $standardMetadata = VectorDBFactory::formatMetadata($item['metadata'] ?? []);

                // 提取MySQL表的冗余字段
                $fields = VectorDBFactory::extractMySQLFields($standardMetadata);

                // 构建基础数据
                $itemData = [
                    'id' => $item['id'],
                    'vector' => json_encode($item['vector']),
                    'metadata' => json_encode($standardMetadata),
                    'createtime' => $now,
                    'updatetime' => $now
                ];

                // 合并冗余字段
                $data[] = array_merge($itemData, $fields);
            }

            \think\Db::name($collectionName)->insertAll($data);
            return true;
        } catch (\Exception $e) {
            \think\Log::error("批量插入向量到 {$collectionName} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新向量
     *
     * 使用VectorDBFactory中的辅助方法处理元数据，并提取冗余字段以优化查询性能。
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @param array $vector 向量数据
     * @param array $metadata 元数据或知识库数据
     * @return bool 是否成功
     */
    public function updateVector(string $collectionName, string $id, array $vector, array $metadata = []): bool
    {
        try {
            $data = [
                'vector' => json_encode($vector),
                'updatetime' => time()
            ];

            if (!empty($metadata)) {
                // 将知识库数据转换为标准元数据格式
                $standardMetadata = VectorDBFactory::formatMetadata($metadata);

                // 提取MySQL表的冗余字段
                $fields = VectorDBFactory::extractMySQLFields($standardMetadata);

                // 设置元数据
                $data['metadata'] = json_encode($standardMetadata);

                // 合并冗余字段
                $data = array_merge($data, $fields);
            }

            \think\Db::name($collectionName)->where('id', $id)->update($data);
            return true;
        } catch (\Exception $e) {
            \think\Log::error("更新向量 {$id} 在 {$collectionName} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return bool 是否成功
     */
    public function deleteVector(string $collectionName, string $id): bool
    {
        try {
            \think\Db::name($collectionName)->where('id', $id)->delete();
            return true;
        } catch (\Exception $e) {
            \think\Log::error("删除向量 {$id} 从 {$collectionName} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 搜索相似向量
     *
     * 使用VectorDBFactory中的convertFilter方法处理过滤条件，
     * 支持对元数据字段和冗余字段的过滤。
     *
     * @param string $collectionName 集合名称
     * @param array $queryVector 查询向量
     * @param int $topK 返回结果数量
     * @param array $filter 过滤条件
     * @return array 搜索结果
     */
    public function search(string $collectionName, array $queryVector, int $topK = 10, array $filter = []): array
    {
        try {
            // 获取所有向量
            $query = \think\Db::name($collectionName);

            // 转换过滤条件为MySQL查询条件
            $convertedFilter = VectorDBFactory::convertFilter($filter, VectorDBFactory::DB_TYPE_MYSQL);

            // 应用过滤条件
            if (!empty($convertedFilter)) {
                foreach ($convertedFilter as $field => $value) {
                    if (is_array($value) && isset($value[0]) && isset($value[1])) {
                        // 处理操作符格式：[$operator, $value]
                        $operator = $value[0];
                        $filterValue = $value[1];

                        switch ($operator) {
                            case '=':
                                $query->where($field, $filterValue);
                                break;
                            case '>':
                                $query->where($field, '>', $filterValue);
                                break;
                            case '>=':
                                $query->where($field, '>=', $filterValue);
                                break;
                            case '<':
                                $query->where($field, '<', $filterValue);
                                break;
                            case '<=':
                                $query->where($field, '<=', $filterValue);
                                break;
                            case 'in':
                                $query->where($field, 'in', $filterValue);
                                break;
                            case 'like':
                                if (is_array($filterValue) && isset($value[2]) && $value[2] === 'OR') {
                                    // 处理OR连接的多个LIKE条件
                                    $query->where(function($q) use ($field, $filterValue) {
                                        foreach ($filterValue as $i => $val) {
                                            if ($i === 0) {
                                                $q->where($field, 'like', $val);
                                            } else {
                                                $q->whereOr($field, 'like', $val);
                                            }
                                        }
                                    });
                                } else {
                                    $query->where($field, 'like', $filterValue);
                                }
                                break;
                        }
                    } else {
                        // 直接值，使用精确匹配
                        $query->where($field, $value);
                    }
                }
            }

            $vectors = $query->select();

            // 计算相似度
            $results = [];
            foreach ($vectors as $vector) {
                $vectorData = json_decode($vector['vector'], true);
                if (!is_array($vectorData)) {
                    continue;
                }

                // 使用VectorDBFactory的calculateSimilarity方法
                $similarity = VectorDBFactory::calculateSimilarity($queryVector, $vectorData);

                $results[] = [
                    'id' => $vector['id'],
                    'similarity' => $similarity,
                    'metadata' => !empty($vector['metadata']) ? json_decode($vector['metadata'], true) : null
                ];
            }

            // 按相似度排序
            usort($results, function($a, $b) {
                return $b['similarity'] <=> $a['similarity'];
            });

            // 返回前topK个结果
            return array_slice($results, 0, $topK);
        } catch (\Exception $e) {
            // 处理异常
            \think\Log::error("搜索向量失败: " . $e->getTraceAsString());
            // 记录错误日志
            \think\Log::error("在 {$collectionName} 中搜索向量失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取向量
     *
     * @param string $collectionName 集合名称
     * @param string $id 向量ID
     * @return array|null 向量数据
     */
    public function getVector(string $collectionName, string $id): ?array
    {
        try {
            $result = \think\Db::name($collectionName)->where('id', $id)->find();

            if (!$result) {
                return null;
            }

            return [
                'id' => $result['id'],
                'vector' => json_decode($result['vector'], true),
                'metadata' => !empty($result['metadata']) ? json_decode($result['metadata'], true) : null,
                'createtime' => $result['createtime'],
                'updatetime' => $result['updatetime']
            ];
        } catch (\Exception $e) {
            \think\Log::error("获取向量 {$id} 从 {$collectionName} 失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取集合信息
     *
     * @param string $collectionName 集合名称
     * @return array 集合信息
     */
    public function getCollectionInfo(string $collectionName): array
    {
        try {
            $count = \think\Db::name($collectionName)->count();

            return [
                'name' => $collectionName,
                'count' => $count,
                'engine' => 'MySQL'
            ];
        } catch (\Exception $e) {
            \think\Log::error("获取集合 {$collectionName} 信息失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 计算两个向量的相似度（余弦相似度）
     *
     * 使用VectorDBFactory中的calculateSimilarity方法，
     * 避免代码重复。
     *
     * @param array $vector1 向量1
     * @param array $vector2 向量2
     * @return float 相似度
     */
    public function calculateSimilarity(array $vector1, array $vector2): float
    {
        // 使用VectorDBFactory中的方法
        return VectorDBFactory::calculateSimilarity($vector1, $vector2);
    }
}
