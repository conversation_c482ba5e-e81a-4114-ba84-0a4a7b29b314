<?php

namespace addons\dsassistant\library;

/**
 * 缓存管理类
 *
 * 用于管理预设关键词和分类规则的缓存，提供缓存刷新、获取和清除功能。
 * 支持缓存版本控制，确保缓存数据的一致性。
 *
 * @package addons\dsassistant\library
 */
class CacheManager
{
    /**
     * 关键词映射缓存键
     */
    const KEYWORD_MAP_CACHE_KEY = 'ds_keyword_map';

    /**
     * 分类规则缓存键
     */
    const CATEGORY_RULE_CACHE_KEY = 'ds_category_rule';

    /**
     * 页面内容缓存前缀
     */
    const PAGE_CONTENT_CACHE_PREFIX = 'page_content';

    /**
     * 页面内容所有缓存前缀
     */
    const PAGE_CONTENT_ALL_CACHE_PREFIX = 'page_content_all';

    /**
     * 页面内容区块缓存前缀
     */
    const PAGE_CONTENT_SECTION_CACHE_PREFIX = 'page_content_section';

    /**
     * 缓存版本键
     */
    const CACHE_VERSION_KEY = 'ds_cache_version';

    /**
     * 缓存有效期（秒）
     */
    const CACHE_TTL = 86400; // 1天

    /**
     * 短期缓存有效期（秒）
     */
    const SHORT_CACHE_TTL = 3600; // 1小时

    /**
     * 刷新关键词映射缓存
     *
     * 从数据库获取最新的关键词映射数据，通过category_id连接分类表获取分类名称，
     * 处理后存入缓存。同时更新缓存版本号，确保数据一致性。
     *
     * @return array 关键词映射数据
     */
    public static function refreshKeywordMapCache()
    {
        try {
            // 从数据库获取关键词映射数据，通过category_id连接分类表获取分类名称
            $keywordMaps = \think\Db::name('ds_keyword_map')
                ->alias('km')
                ->join('ds_category_rule c', 'km.category_id = c.id', 'LEFT')
                ->where('km.status', 'normal')
                ->field('km.id, km.main_keyword, km.related_keywords, km.category_id, c.category, km.weight')
                ->select();

            // 处理数据格式
            $keywordMapData = [];
            foreach ($keywordMaps as $map) {
                // 将相关关键词字符串转换为数组
                $relatedKeywords = !empty($map['related_keywords']) ?
                    explode(',', $map['related_keywords']) : [];

                // 清理数组元素
                $relatedKeywords = array_map('trim', $relatedKeywords);
                $relatedKeywords = array_filter($relatedKeywords);

                // 添加到映射数据
                $keywordMapData[$map['main_keyword']] = [
                    'id' => $map['id'],
                    'related_keywords' => $relatedKeywords,
                    'category_id' => $map['category_id'],
                    'category' => $map['category'] ?: '未分类', // 如果分类名称为空，则使用默认值
                    'weight' => $map['weight']
                ];
            }

            // 保存到缓存
            \think\Cache::set(self::KEYWORD_MAP_CACHE_KEY, $keywordMapData, self::CACHE_TTL);

            // 更新缓存版本
            self::updateCacheVersion();

            \think\Log::info('关键词映射缓存已刷新，共 ' . count($keywordMapData) . ' 条记录');

            return $keywordMapData;
        } catch (\Exception $e) {
            \think\Log::error('刷新关键词映射缓存失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取关键词映射数据
     *
     * 从缓存获取关键词映射数据，如果缓存不存在或强制刷新，则从数据库重新获取。
     * 关键词映射用于扩展搜索匹配范围，将主关键词映射到相关关键词。
     *
     * 返回的数据结构格式为：
     * [
     *     '主关键词1' => [
     *         'id' => 1,                           // 映射记录ID
     *         'related_keywords' => ['相关词1', '相关词2'], // 相关关键词数组
     *         'category_id' => 5,                  // 关联的分类ID
     *         'category' => '分类名称',              // 分类名称
     *         'weight' => 100                      // 权重值
     *     ],
     *     '主关键词2' => [
     *         // 同上结构
     *     ],
     *     // 更多关键词映射...
     * ]
     *
     * 此数据结构用于EnhancedVectorSearch中的extractKeywords方法，
     * 帮助识别用户问题中的关键词，并扩展到相关关键词，提高搜索匹配率。
     *
     * @param bool $forceRefresh 是否强制刷新缓存
     * @return array 关键词映射数据
     */
    public static function getKeywordMap($forceRefresh = false)
    {
        // 如果强制刷新或缓存不存在，则刷新缓存
        if ($forceRefresh || !self::isCacheValid(self::KEYWORD_MAP_CACHE_KEY)) {
            return self::refreshKeywordMapCache();
        }

        // 从缓存获取数据
        $keywordMapData = \think\Cache::get(self::KEYWORD_MAP_CACHE_KEY);

        // 如果缓存数据为空，则刷新缓存
        if (empty($keywordMapData)) {
            return self::refreshKeywordMapCache();
        }

        return $keywordMapData;
    }

    /**
     * 刷新分类规则缓存
     *
     * 从数据库获取最新的分类规则数据，处理后存入缓存。
     * 同时更新缓存版本号，确保数据一致性。
     *
     * @return array 分类规则数据
     */
    public static function refreshCategoryRuleCache()
    {
        try {
            // 从数据库获取分类规则数据
            $categoryRules = \think\Db::name('ds_category_rule')
                ->where('status', 'normal')
                ->field('id, category, pattern, boost')
                ->select();

            // 处理数据格式
            $categoryRuleData = [];
            foreach ($categoryRules as $rule) {
                // 将正则表达式模式字符串转换为数组
                $patterns = !empty($rule['pattern']) ?
                    explode('|', $rule['pattern']) : [];

                // 清理数组元素
                $patterns = array_map('trim', $patterns);
                $patterns = array_filter($patterns);

                // 添加到规则数据
                $categoryRuleData[$rule['category']] = [
                    'id' => $rule['id'],
                    'patterns' => $patterns,
                    'boost' => floatval($rule['boost'])
                ];
            }

            // 保存到缓存
            \think\Cache::set(self::CATEGORY_RULE_CACHE_KEY, $categoryRuleData, self::CACHE_TTL);

            // 更新缓存版本
            self::updateCacheVersion();

            \think\Log::info('分类规则缓存已刷新，共 ' . count($categoryRuleData) . ' 条记录');

            return $categoryRuleData;
        } catch (\Exception $e) {
            \think\Log::error('刷新分类规则缓存失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取分类规则数据
     *
     * 从缓存获取分类规则数据，如果缓存不存在或强制刷新，则从数据库重新获取。
     * 分类规则用于识别用户问题的类别，通过正则表达式模式匹配实现。
     *
     * 返回的数据结构格式为：
     * [
     *     '分类名称1' => [
     *         'id' => 1,                       // 规则记录ID
     *         'patterns' => ['模式1', '模式2'],   // 正则表达式模式数组
     *         'boost' => 0.2                   // 提升因子，用于调整搜索结果排序
     *     ],
     *     '分类名称2' => [
     *         // 同上结构
     *     ],
     *     // 更多分类规则...
     * ]
     *
     * 此数据结构用于EnhancedVectorSearch中的identifyTitleCategory方法，
     * 帮助识别用户问题的类别，用于过滤和提升相关搜索结果。
     * 
     * 注意：分类名称应与向量数据库中的category字段值一致，
     * 以确保正确的过滤和提升效果。
     *
     * @param bool $forceRefresh 是否强制刷新缓存
     * @return array 分类规则数据
     */
    public static function getCategoryRule($forceRefresh = false)
    {
        // 如果强制刷新或缓存不存在，则刷新缓存
        if ($forceRefresh || !self::isCacheValid(self::CATEGORY_RULE_CACHE_KEY)) {
            return self::refreshCategoryRuleCache();
        }

        // 从缓存获取数据
        $categoryRuleData = \think\Cache::get(self::CATEGORY_RULE_CACHE_KEY);

        // 如果缓存数据为空，则刷新缓存
        if (empty($categoryRuleData)) {
            return self::refreshCategoryRuleCache();
        }

        return $categoryRuleData;
    }

    /**
     * 刷新所有缓存
     *
     * 刷新关键词映射、分类规则和页面内容缓存，并更新缓存版本。
     *
     * @return bool 是否成功
     */
    public static function refreshAllCache()
    {
        try {
            self::refreshKeywordMapCache();
            self::refreshCategoryRuleCache();
            self::refreshPageContentCache();

            // 更新缓存版本
            self::updateCacheVersion();

            \think\Log::info('所有缓存已刷新');
            return true;
        } catch (\Exception $e) {
            \think\Log::error('刷新所有缓存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 清除所有缓存
     *
     * 清除关键词映射、分类规则和页面内容缓存，并更新缓存版本。
     *
     * @return bool 是否成功
     */
    public static function clearAllCache()
    {
        try {
            \think\Cache::rm(self::KEYWORD_MAP_CACHE_KEY);
            \think\Cache::rm(self::CATEGORY_RULE_CACHE_KEY);
            self::clearPageContentCache();

            // 更新缓存版本
            self::updateCacheVersion();

            \think\Log::info('所有缓存已清除');
            return true;
        } catch (\Exception $e) {
            \think\Log::error('清除所有缓存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新缓存版本
     *
     * 生成新的缓存版本号并存入缓存，用于确保缓存数据的一致性。
     *
     * @return string 新的缓存版本号
     */
    protected static function updateCacheVersion()
    {
        $version = uniqid('v', true);
        \think\Cache::set(self::CACHE_VERSION_KEY, $version, self::CACHE_TTL);
        return $version;
    }

    /**
     * 获取当前缓存版本
     *
     * @return string|null 当前缓存版本号，如果不存在则返回null
     */
    public static function getCacheVersion()
    {
        return \think\Cache::get(self::CACHE_VERSION_KEY);
    }

    /**
     * 检查缓存是否有效
     *
     * 检查指定的缓存键是否存在于缓存中。
     *
     * @param string $cacheKey 缓存键
     * @return bool 缓存是否有效
     */
    protected static function isCacheValid($cacheKey)
    {
        return \think\Cache::has($cacheKey);
    }

    /**
     * 获取页面内容
     *
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @param string $key 内容键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getPageContent($page, $section, $key, $default = '')
    {
        // 尝试从缓存获取
        $cacheKey = self::PAGE_CONTENT_CACHE_PREFIX . "_{$page}_{$section}_{$key}";
        $value = \think\Cache::get($cacheKey);
        if ($value !== false) {
            return $value;
        }

        // 从数据库获取
        $content = \think\Db::name('ds_page_content')
            ->where('page', $page)
            ->where('section', $section)
            ->where('key', $key)
            ->find();

        if (!$content) {
            return $default;
        }

        // 写入缓存
        \think\Cache::set($cacheKey, $content['value'], self::SHORT_CACHE_TTL);

        return $content['value'];
    }

    /**
     * 设置页面内容
     *
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @param string $key 内容键名
     * @param mixed $value 内容值
     * @param string $description 描述
     * @return boolean
     */
    public static function setPageContent($page, $section, $key, $value, $description = '')
    {
        $content = \think\Db::name('ds_page_content')
            ->where('page', $page)
            ->where('section', $section)
            ->where('key', $key)
            ->find();

        if ($content) {
            $result = \think\Db::name('ds_page_content')
                ->where('id', $content['id'])
                ->update([
                    'value' => $value,
                    'description' => $description ?: $content['description'],
                    'updatetime' => time()
                ]);
        } else {
            $result = \think\Db::name('ds_page_content')->insert([
                'page' => $page,
                'section' => $section,
                'key' => $key,
                'value' => $value,
                'description' => $description ?: '自动创建的内容',
                'createtime' => time(),
                'updatetime' => time()
            ]);
        }

        // 更新缓存
        if ($result) {
            $cacheKey = self::PAGE_CONTENT_CACHE_PREFIX . "_{$page}_{$section}_{$key}";
            \think\Cache::set($cacheKey, $value, self::SHORT_CACHE_TTL);

            // 清除相关的聚合缓存
            \think\Cache::rm(self::PAGE_CONTENT_ALL_CACHE_PREFIX . "_{$page}");
            \think\Cache::rm(self::PAGE_CONTENT_SECTION_CACHE_PREFIX . "_{$page}_{$section}");

            // 更新缓存版本
            self::updateCacheVersion();

            \think\Log::info("页面内容缓存已更新: {$page}/{$section}/{$key}");
        }

        return $result !== false;
    }

    /**
     * 获取页面所有内容
     *
     * @param string $page 页面标识
     * @return array
     */
    public static function getPageContents($page)
    {
        // 尝试从缓存获取
        $cacheKey = self::PAGE_CONTENT_ALL_CACHE_PREFIX . "_{$page}";
        $contents = \think\Cache::get($cacheKey);
        if ($contents !== false) {
            return $contents;
        }

        // 从数据库获取
        $contents = \think\Db::name('ds_page_content')
            ->where('page', $page)
            ->select();

        if (!$contents) {
            return [];
        }

        $result = [];
        foreach ($contents as $content) {
            if (!isset($result[$content['section']])) {
                $result[$content['section']] = [];
            }
            $result[$content['section']][$content['key']] = $content['value'];
        }

        // 写入缓存
        \think\Cache::set($cacheKey, $result, self::SHORT_CACHE_TTL);

        return $result;
    }

    /**
     * 获取页面区块内容
     *
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @return array
     */
    public static function getSectionContents($page, $section)
    {
        // 尝试从缓存获取
        $cacheKey = self::PAGE_CONTENT_SECTION_CACHE_PREFIX . "_{$page}_{$section}";
        $contents = \think\Cache::get($cacheKey);
        if ($contents !== false) {
            return $contents;
        }

        // 从数据库获取
        $contents = \think\Db::name('ds_page_content')
            ->where('page', $page)
            ->where('section', $section)
            ->select();

        if (!$contents) {
            return [];
        }

        $result = [];
        foreach ($contents as $content) {
            $result[$content['key']] = $content['value'];
        }

        // 写入缓存
        \think\Cache::set($cacheKey, $result, self::SHORT_CACHE_TTL);

        return $result;
    }

    /**
     * 清除页面内容缓存
     *
     * @param string $page 页面标识，为空则清除所有页面缓存
     * @return boolean
     */
    public static function clearPageContentCache($page = '')
    {
        try {
            if ($page) {
                // 清除指定页面的缓存
                $contents = \think\Db::name('ds_page_content')
                    ->where('page', $page)
                    ->select();

                foreach ($contents as $content) {
                    $cacheKey = self::PAGE_CONTENT_CACHE_PREFIX . "_{$page}_{$content['section']}_{$content['key']}";
                    \think\Cache::rm($cacheKey);
                }

                // 清除页面所有内容缓存
                \think\Cache::rm(self::PAGE_CONTENT_ALL_CACHE_PREFIX . "_{$page}");

                // 清除页面区块缓存
                $sections = \think\Db::name('ds_page_content')
                    ->where('page', $page)
                    ->group('section')
                    ->column('section');

                foreach ($sections as $section) {
                    \think\Cache::rm(self::PAGE_CONTENT_SECTION_CACHE_PREFIX . "_{$page}_{$section}");
                }

                \think\Log::info("页面内容缓存已清除: {$page}");
            } else {
                // 清除所有页面缓存
                $pages = \think\Db::name('ds_page_content')
                    ->group('page')
                    ->column('page');

                foreach ($pages as $p) {
                    self::clearPageContentCache($p);
                }

                \think\Log::info("所有页面内容缓存已清除");
            }

            // 更新缓存版本
            self::updateCacheVersion();

            return true;
        } catch (\Exception $e) {
            \think\Log::error("清除页面内容缓存失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 刷新页面内容缓存
     *
     * @param string $page 页面标识，为空则刷新所有页面缓存
     * @return boolean
     */
    public static function refreshPageContentCache($page = '')
    {
        try {
            // 先清除缓存
            self::clearPageContentCache($page);

            // 然后重新加载
            if ($page) {
                self::getPageContents($page);

                // 加载页面的所有区块
                $sections = \think\Db::name('ds_page_content')
                    ->where('page', $page)
                    ->group('section')
                    ->column('section');

                foreach ($sections as $section) {
                    self::getSectionContents($page, $section);
                }

                \think\Log::info("页面内容缓存已刷新: {$page}");
            } else {
                // 刷新所有页面缓存
                $pages = \think\Db::name('ds_page_content')
                    ->group('page')
                    ->column('page');

                foreach ($pages as $p) {
                    self::refreshPageContentCache($p);
                }

                \think\Log::info("所有页面内容缓存已刷新");
            }

            // 更新缓存版本
            self::updateCacheVersion();

            return true;
        } catch (\Exception $e) {
            \think\Log::error("刷新页面内容缓存失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取缓存统计信息
     *
     * 返回当前缓存的统计信息，包括缓存键、是否存在、最后更新时间等。
     *
     * @return array 缓存统计信息
     */
    public static function getCacheStats()
    {
        $stats = [];

        // 关键词映射缓存统计
        $keywordMapExists = \think\Cache::has(self::KEYWORD_MAP_CACHE_KEY);
        $keywordMapData = $keywordMapExists ? \think\Cache::get(self::KEYWORD_MAP_CACHE_KEY) : null;
        $stats['keyword_map'] = [
            'exists' => $keywordMapExists,
            'count' => $keywordMapData ? count($keywordMapData) : 0,
            'size' => $keywordMapData ? strlen(serialize($keywordMapData)) : 0
        ];

        // 分类规则缓存统计
        $categoryRuleExists = \think\Cache::has(self::CATEGORY_RULE_CACHE_KEY);
        $categoryRuleData = $categoryRuleExists ? \think\Cache::get(self::CATEGORY_RULE_CACHE_KEY) : null;
        $stats['category_rule'] = [
            'exists' => $categoryRuleExists,
            'count' => $categoryRuleData ? count($categoryRuleData) : 0,
            'size' => $categoryRuleData ? strlen(serialize($categoryRuleData)) : 0
        ];

        // 页面内容缓存统计
        $pageContentCount = 0;
        $pageContentExists = false;

        // 获取所有页面
        try {
            $pages = \think\Db::name('ds_page_content')
                ->group('page')
                ->column('page');

            foreach ($pages as $page) {
                $cacheKey = self::PAGE_CONTENT_ALL_CACHE_PREFIX . "_{$page}";
                if (\think\Cache::has($cacheKey)) {
                    $pageContentExists = true;
                    $pageContentData = \think\Cache::get($cacheKey);
                    if (is_array($pageContentData)) {
                        foreach ($pageContentData as $section) {
                            $pageContentCount += count($section);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            \think\Log::error("获取页面内容缓存统计失败: " . $e->getMessage());
        }

        $stats['page_content'] = [
            'exists' => $pageContentExists,
            'count' => $pageContentCount,
            'size' => 0 // 由于页面内容缓存分散存储，这里不计算大小
        ];

        // 缓存版本信息
        $version = self::getCacheVersion();
        $stats['version'] = [
            'exists' => !empty($version),
            'value' => $version
        ];

        return $stats;
    }
}
