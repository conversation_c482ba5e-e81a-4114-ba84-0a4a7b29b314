<?php

namespace addons\dsassistant\library;

use think\Db;
use think\Exception;
use think\Log;

/**
 * 模板转换器
 * 基于JSON模板将数据源转换为目标数据
 */
class TemplateConverter
{
    /**
     * 执行转换
     * @param int $templateId 模板ID
     * @param bool $force 是否强制全量同步
     * @return array 转换结果
     */
    public static function convert($templateId, $force = false)
    {
        try {
            // 获取模板信息
            $template = Db::name('ds_converter_template')->where('id', $templateId)->find();
            if (!$template) {
                throw new Exception("模板不存在");
            }
            
            // 检查模板状态
            if ($template['status'] != 'normal') {
                throw new Exception("模板状态不正常，无法执行转换");
            }
            
            // 构建查询条件
            $query = Db::name($template['source_table']);
            
            // 应用WHERE条件
            if (!empty($template['where_condition'])) {
                $query->where($template['where_condition']);
            }
            
            // 增量同步
            if ($template['sync_mode'] == 'increment' && !$force) {
                if (!empty($template['sync_field']) && !empty($template['last_sync_time'])) {
                    $query->where($template['sync_field'] . ' > ' . $template['last_sync_time']);
                } elseif (!empty($template['sync_field']) && !empty($template['last_sync_id'])) {
                    $query->where($template['sync_field'] . ' > ' . $template['last_sync_id']);
                }
            }
            
            // 应用排序
            if (!empty($template['order_field'])) {
                $query->order($template['order_field'], $template['order_direction'] ?: 'asc');
            }
            
            // 获取总记录数
            $total = $query->count();
            if ($total == 0) {
                return [
                    'code' => 1,
                    'msg' => '没有需要转换的数据',
                    'data' => [
                        'total' => 0,
                        'success' => 0,
                        'error' => 0
                    ]
                ];
            }
            
            // 分批处理
            $batchSize = $template['batch_size'] ?: 100;
            $successCount = 0;
            $errorCount = 0;
            $lastId = 0;
            $lastTime = 0;
            
            for ($offset = 0; $offset < $total; $offset += $batchSize) {
                $sourceData = $query->limit($offset, $batchSize)->select();
                
                foreach ($sourceData as $data) {
                    try {
                        // 应用模板转换
                        $result = self::applyTemplate($template, $data);
                        
                        // 保存到目标表
                        if ($result) {
                            // 检查是否已存在相同记录
                            $exists = Db::name($template['target_table'])
                                ->where('source_type', $template['source_type'])
                                ->where('source_id', $data['id'])
                                ->where('content_type', $template['content_type'])
                                ->find();
                            
                            if ($exists) {
                                // 更新记录
                                Db::name($template['target_table'])
                                    ->where('id', $exists['id'])
                                    ->update($result);
                            } else {
                                // 插入记录
                                Db::name($template['target_table'])->insert($result);
                            }
                            
                            $successCount++;
                        }
                        
                        // 记录最后处理的ID和时间
                        if (isset($data['id']) && $data['id'] > $lastId) {
                            $lastId = $data['id'];
                        }
                        
                        if (isset($data[$template['sync_field']]) && $data[$template['sync_field']] > $lastTime) {
                            $lastTime = $data[$template['sync_field']];
                        }
                    } catch (Exception $e) {
                        Log::error("转换数据失败: " . $e->getMessage() . ", 数据: " . json_encode($data, JSON_UNESCAPED_UNICODE));
                        $errorCount++;
                    }
                }
            }
            
            // 更新模板同步状态
            $updateData = [
                'updatetime' => time()
            ];
            
            if ($lastId > 0) {
                $updateData['last_sync_id'] = $lastId;
            }
            
            if ($lastTime > 0) {
                $updateData['last_sync_time'] = $lastTime;
            }
            
            Db::name('ds_converter_template')
                ->where('id', $templateId)
                ->update($updateData);
            
            return [
                'code' => 1,
                'msg' => '转换完成',
                'data' => [
                    'total' => $total,
                    'success' => $successCount,
                    'error' => $errorCount
                ]
            ];
        } catch (Exception $e) {
            Log::error("执行转换失败: " . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '执行转换失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 应用模板转换数据
     * @param array $template 模板配置
     * @param array $data 源数据
     * @return array|false 转换后的数据，失败返回false
     */
    protected static function applyTemplate($template, $data)
    {
        try {
            // 解析模板内容
            $templateContent = json_decode($template['template_content'], true);
            if (!$templateContent) {
                throw new Exception("模板内容格式错误");
            }
            
            // 替换模板中的变量
            $result = self::replaceVariables($templateContent, $data);
            
            // 添加必要的字段
            $result['category_id'] = $template['category_id'];
            $result['source_type'] = $template['source_type'];
            $result['content_type'] = $template['content_type'];
            $result['source_id'] = $data['id'];
            
            // 如果没有设置创建时间，添加当前时间
            if (!isset($result['createtime'])) {
                $result['createtime'] = time();
            }
            
            // 如果没有设置更新时间，添加当前时间
            if (!isset($result['updatetime'])) {
                $result['updatetime'] = time();
            }
            
            return $result;
        } catch (Exception $e) {
            Log::error("应用模板失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 替换变量
     * @param mixed $template 模板
     * @param array $data 数据
     * @return mixed 替换后的结果
     */
    protected static function replaceVariables($template, $data)
    {
        if (is_string($template)) {
            // 替换字符串中的变量
            return preg_replace_callback('/{([^}]+)}/', function($matches) use ($data) {
                $key = $matches[1];
                return isset($data[$key]) ? $data[$key] : '';
            }, $template);
        } elseif (is_array($template)) {
            // 递归处理数组
            $result = [];
            foreach ($template as $key => $value) {
                $result[$key] = self::replaceVariables($value, $data);
            }
            return $result;
        } else {
            // 其他类型直接返回
            return $template;
        }
    }
    
    /**
     * 批量执行转换
     * @param array $templateIds 模板ID数组
     * @param bool $force 是否强制全量同步
     * @return array 转换结果
     */
    public static function batchConvert($templateIds, $force = false)
    {
        $results = [];
        foreach ($templateIds as $templateId) {
            $results[$templateId] = self::convert($templateId, $force);
        }
        return $results;
    }
}
