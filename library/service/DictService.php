<?php

namespace addons\dsassistant\library\service;

use app\admin\model\dsassistant\Dict;

/**
 * 字典服务
 */
class DictService {

    /**
     * 获取字典列表
     * 
     * @param string $parent 父级名称
     * @return array
     */
    public static function dicts($parent = 'root')
    {
        $dicts = Dict::where(['parent'=>$parent, 'status'=>'normal'])
            ->order('weight asc')->column('name,title');
        return $dicts;
    }

}