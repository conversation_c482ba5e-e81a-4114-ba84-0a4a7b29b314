<?php
namespace addons\dsassistant\library\service;

use addons\dsassistant\library\VectorSearchFactory;
use app\admin\model\dsassistant\Knowledge;

class KnowledgeService
{
    public static function registerEventAfterInsert()
    {
        Knowledge::event('after_insert', function ($model) {
            // 在插入后执行的逻辑
            self::indexVectorSingleItem($model);
        });
    }

    public static function registerEventAfterUpdate() {
        Knowledge::event('after_update', function ($model) {
            // 在更新后执行的逻辑
            self::indexVectorSingleItem($model);
        });
    }

    public static function registerEventAfterDelete() {
        Knowledge::event('after_delete', function ($model) {
            // 在删除后执行的逻辑
            self::deleteVectorSingleItem($model);
        });
    }

    public static function indexVectorSingleItem($model) {
        $vectorSearch = VectorSearchFactory::getInstance();
        $vectorSearch->indexSingleItem($model);
    }

    public static function deleteVectorSingleItem($model) {
        $vectorSearch = VectorSearchFactory::getInstance();
        $vectorSearch->deleteSingleItem($model['id']);
    }
}