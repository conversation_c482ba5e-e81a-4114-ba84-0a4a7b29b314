<?php

namespace addons\dsassistant\library\service;

use addons\dsassistant\library\llm\LLMModelManager;
use app\admin\model\dsassistant\LlmModels;
use think\Exception;

class LlmModelsService {

    public static function registDefaultChanged(){
        LlmModels::event('after_insert', function ($model) {
            self::recache($model);
        });
        LlmModels::event('after_update', function ($model) {
            self::recache($model);
        });
        LlmModels::event('after_delete', function ($model) {
            if($model['is_default']) {
               throw new Exception('默认的模型不能删除');
            }
        });
    }

    public static function recache($model){
        if($model['is_default']) {
            LlmModels::update(['is_default'=>0],[
                ['id','<>', $model['id']]
            ]);
            LLMModelManager::getDefaultModel(true);
        }
    }
}