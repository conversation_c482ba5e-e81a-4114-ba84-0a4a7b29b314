<?php

namespace addons\dsassistant\library;

use think\Db;
use think\Log;
use think\Cache;
use addons\dsassistant\library\JiebaClient;

/**
 * 使用jieba分词客户端的向量搜索类
 * 增强的向量搜索类
 * 使用向量数据库进行高效搜索
 */
class JiebaVectorSearch extends EnhancedVectorSearch
{
    /**
     * Jieba分词客户端
     * @var JiebaClient
     */
    protected $jiebaClient;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 调用父类构造函数
        parent::__construct();

        // 初始化Jieba分词客户端
        $this->jiebaClient = new JiebaClient();

        Log::info('JiebaVectorSearch初始化完成，使用Jieba分词服务');
    }

    /**
     * 提取问题中的关键词
     * 重写父类方法，使用Jieba分词
     *
     * @param string $title 用户问题
     * @return array 关键词数组
     */
    protected function extractKeywords($title)
    {
        $foundKeywords = [];

        // 1. 直接匹配预定义关键词（保留父类的这部分逻辑）
        if (!empty($this->keywordMap)) {
            foreach ($this->keywordMap as $mainKeyword => $relatedKeywords) {
                if (mb_stripos($title, $mainKeyword) !== false) {
                    $foundKeywords[] = $mainKeyword;
                    continue;
                }

                // 检查相关关键词
                if (is_array($relatedKeywords)) {
                    foreach ($relatedKeywords as $keyword) {
                        if (mb_stripos($title, $keyword) !== false) {
                            $foundKeywords[] = $mainKeyword; // 添加主关键词
                            break;
                        }
                    }
                }
            }
        }

        // 2. 使用Jieba分词服务提取关键词
        try {
            // 检查Jieba服务是否可用
            if ($this->jiebaClient->isServiceAvailable()) {
                // 使用Jieba分词服务
                $segmentedText = $this->jiebaClient->segment($title);
                $words = explode(' ', $segmentedText);

                // 过滤空字符串
                $words = array_filter($words, function($word) {
                    return !empty($word);
                });

                Log::info("使用Jieba分词服务成功提取关键词");

                // 合并结果并去重
                $allKeywords = array_unique(array_merge($foundKeywords, $words));

                // 记录日志
                Log::record('从问题中提取的关键词: ' . implode(', ', $allKeywords), 'info');

                return $allKeywords;
            }
        } catch (\Exception $e) {
            Log::error("Jieba分词异常: " . $e->getMessage());
        }

        // 如果Jieba服务不可用或出现异常，使用父类方法
        return parent::extractKeywords($title);
    }

    /**
     * 搜索知识库
     *
     * @param string $title 用户问题
     * @param int $topK 返回结果数量
     * @return array 匹配结果
     */
    public function search($title, $topK = 5)
    {
        try {
            // 记录开始时间
            $startTime = microtime(true);

            // 1. 使用Jieba分词提取关键词
            $keywords = $this->extractKeywords($title);

            // 2. 识别问题类别
            $categories = $this->identifyTitleCategory($title);

            // 3. 构建过滤条件
            $filter = [];

            // 添加分类过滤
            if (!empty($categories)) {
                $categoryFilter = [];
                foreach ($categories as $category) {
                    $categoryFilter[] = [
                        'operator' => 'like',
                        'value' => "%{$category}%"
                    ];
                }
                $filter['category'] = $categoryFilter;
            }

            // 添加关键词过滤
            if (!empty($keywords)) {
                $keywordFilter = [];
                foreach ($keywords as $keyword) {
                    if (mb_strlen($keyword, 'UTF-8') >= 2) {
                        $keywordFilter[] = [
                            'operator' => 'like',
                            'value' => "%{$keyword}%"
                        ];
                    }
                }
                if (!empty($keywordFilter)) {
                    $filter['tags'] = $keywordFilter;
                }
            }

            // 4. 生成问题的向量表示（使用缓存减少API调用）
            $cacheKey = $this->cachePrefix . md5($title);
            $questionVector = Cache::get($cacheKey);

            if (!$questionVector) {
                $questionVector = $this->embedding->getEmbedding($title);
                Cache::set($cacheKey, $questionVector, $this->cacheTTL);
            }

            // 5. 使用向量数据库搜索
            $searchResults = $this->vectorDB->search(
                $this->collectionName,
                $questionVector,
                $topK * 3, // 获取更多结果，以便后续处理
                $filter
            );

            // 记录向量搜索时间
            $vectorTime = microtime(true);
            Log::record("向量搜索耗时: " . round(($vectorTime - $startTime) * 1000, 2) . "ms, 获取到 " . count($searchResults) . " 条结果", 'info');

            if (empty($searchResults)) {
                return [];
            }

            // 6. 获取知识库详细信息
            $knowledgeIds = array_column($searchResults, 'id');
            $knowledgeItems = Db::name('ds_knowledge')
                ->where('id', 'in', $knowledgeIds)
                ->select();

            // 构建ID到知识条目的映射
            $knowledgeMap = [];
            foreach ($knowledgeItems as $item) {
                $knowledgeMap[$item['id']] = $item;
            }

            // 7. 计算最终得分
            $results = [];
            foreach ($searchResults as $result) {
                $id = $result['id'];
                if (isset($knowledgeMap[$id])) {
                    $item = $knowledgeMap[$id];

                    // 基础相似度
                    $similarity = $result['similarity'];

                    // 权重调整
                    $weightFactor = min(0.2, $item['weight'] / 500);

                    // 分类匹配加分
                    $categoryBoost = 0;
                    if (!empty($categories) && !empty($item['category'])) {
                        foreach ($categories as $category) {
                            if (strpos($item['category'], $category) !== false) {
                                // 使用缓存的分类规则中的boost值
                                if (isset($this->categoryRules[$category]) && isset($this->categoryRules[$category]['boost'])) {
                                    $categoryBoost = floatval($this->categoryRules[$category]['boost']);
                                } else {
                                    $categoryBoost = 0.1; // 默认加分
                                }
                                break;
                            }
                        }
                    }

                    // 关键词匹配加分 - 使用Jieba分词结果提高精度
                    $keywordBoost = 0;
                    if (!empty($keywords) && !empty($item['tags'])) {
                        // 使用Jieba分词处理知识库条目的关键词
                        $itemKeywordsText = $item['tags'];
                        $segmentedItemKeywords = '';

                        if ($this->jiebaClient->isServiceAvailable()) {
                            $segmentedItemKeywords = $this->jiebaClient->segment($itemKeywordsText);
                            $itemKeywords = explode(' ', $segmentedItemKeywords);
                        } else {
                            $itemKeywords = explode(',', $item['tags']);
                        }

                        $matchCount = 0;
                        foreach ($keywords as $keyword) {
                            foreach ($itemKeywords as $itemKeyword) {
                                if (mb_stripos($itemKeyword, $keyword) !== false || mb_stripos($keyword, $itemKeyword) !== false) {
                                    $matchCount++;
                                    break;
                                }
                            }
                        }
                        $keywordBoost = min(0.3, $matchCount * 0.05); // 提高关键词匹配的权重
                    }

                    // 计算最终得分
                    $finalScore = $similarity + $weightFactor + $categoryBoost + $keywordBoost;

                    // 记录详细的相似度计算信息
                    Log::debug("知识库ID {$id}, 问题: \"{$item['title']}\", 相似度: {$similarity}, 权重因子: {$weightFactor}, 分类加分: {$categoryBoost}, 关键词加分: {$keywordBoost}, 最终得分: {$finalScore}");

                    $results[] = [
                        'id' => $id,
                        'title' => $item['title'],
                        'content' => $item['content'],
                        'score' => $finalScore,
                        'similarity' => $similarity,
                        'weight_factor' => $weightFactor,
                        'category_boost' => $categoryBoost,
                        'keyword_boost' => $keywordBoost
                    ];
                }
            }

            // 8. 排序并返回结果
            usort($results, function($a, $b) {
                return $b['score'] <=> $a['score'];
            });

            $topResults = array_slice($results, 0, $topK);

            // 记录总耗时
            $endTime = microtime(true);
            Log::record("向量搜索总耗时: " . round(($endTime - $startTime) * 1000, 2) . "ms", 'info');

            // 记录最佳匹配
            if (!empty($topResults)) {
                Log::record("最佳匹配: \"{$topResults[0]['title']}\", 得分: {$topResults[0]['score']}", 'info');
            } else {
                Log::record("未找到匹配项", 'info');
            }

            return $topResults;
        } catch (\Exception $e) {
            Log::error("向量搜索失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return [];
        }
    }

    /**
     * 为知识库生成向量索引
     *
     * @param array|null $knowledgeList 知识库条目列表，如果为null则处理所有正常状态的知识库条目
     * @param bool $updateVectorStatus 是否更新知识库表中的向量状态字段
     * @return int 索引的条目数
     */
    public function indexKnowledgeBase($knowledgeList = null, $updateVectorStatus = true)
    {
        // 如果没有提供知识库列表，则获取所有正常状态的知识库条目
        if ($knowledgeList === null) {
            $knowledgeList = Db::name('ds_knowledge')
                ->with(['category'])
                ->where('status', 'normal')
                ->select();
        }

        $count = 0;
        $errors = 0;
        $startTime = microtime(true);

        Log::record("开始为 " . count($knowledgeList) . " 条知识库条目生成向量索引", 'info');

        // 分批处理，每批10条
        $batches = array_chunk($knowledgeList, 10);

        foreach ($batches as $batchIndex => $batch) {
            try {
                $batchVectors = [];

                foreach ($batch as $item) {
                    try {
                        // 使用Jieba分词处理问题文本
                        $segmentedText = '';
                        if ($this->jiebaClient->isServiceAvailable()) {
                            $segmentedText = $this->jiebaClient->segment($item['title']);
                            Log::info("使用Jieba分词服务处理知识库ID {$item['id']} 的问题文本");
                        }

                        // 生成问题的向量表示
                        $vector = $this->embedding->getEmbedding($item['title']);

                        // 准备元数据项
                        $itemData = [
                            'title' => $item['title'],
                            'tags' => $item['tags'],
                            'category' => $item['category'],
                            'weight' => $item['weight']
                        ];

                        // 如果有分词结果，添加到元数据项
                        if (!empty($segmentedText)) {
                            $itemData['segmented_text'] = $segmentedText;
                        }

                        // 使用VectorDBFactory创建标准元数据
                        $metadata = \addons\dsassistant\library\vectordb\VectorDBFactory::createStandardMetadata($itemData);

                        // 如果有分词结果，更新知识库表的分词字段
                        if (!empty($segmentedText)) {
                            // 同时更新知识库表的分词字段
                            $updateData = [
                                'segmented_text' => $segmentedText,
                                'updatetime' => time()
                            ];

                            // 如果需要更新向量状态
                            if ($updateVectorStatus) {
                                $updateData['has_vector'] = 1;
                                $updateData['vector_time'] = time();
                            }

                            Db::name('ds_knowledge')
                                ->where('id', $item['id'])
                                ->update($updateData);
                        } else if ($updateVectorStatus) {
                            // 如果没有分词结果但需要更新向量状态
                            Db::name('ds_knowledge')
                                ->where('id', $item['id'])
                                ->update([
                                    'has_vector' => 1,
                                    'vector_time' => time(),
                                    'updatetime' => time()
                                ]);
                        }

                        $batchVectors[] = [
                            'id' => $item['id'],
                            'vector' => $vector,
                            'metadata' => $metadata
                        ];

                        $count++;
                    } catch (\Exception $e) {
                        Log::error("为知识库ID {$item['id']} 生成向量失败: " . $e->getMessage());
                        $errors++;
                    }
                }

                // 批量插入向量
                if (!empty($batchVectors)) {
                    $this->vectorDB->batchInsertVectors($this->collectionName, $batchVectors);
                }

                // 每批处理完后休息1秒，避免API限制
                if ($batchIndex < count($batches) - 1) {
                    sleep(1);
                }

                // 记录进度
                $progress = round(($batchIndex + 1) / count($batches) * 100, 2);
                Log::info("向量索引进度: {$progress}%, 已处理: {$count}条, 错误: {$errors}条");

            } catch (\Exception $e) {
                Log::error("处理批次 {$batchIndex} 时出错: " . $e->getMessage());
                // 如果批处理失败，暂停一段时间后继续
                sleep(5);
            }
        }

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);

        Log::record("向量索引完成，共处理 {$count} 条记录，失败 {$errors} 条，耗时 {$duration} 秒", 'info');

        return $count;
    }

    /**
     * 为单个知识条目生成向量
     *
     * @param \app\admin\model\dsassistant\Knowledge|array $knowledge 知识库对象
     * @param bool $updateVectorStatus 是否更新知识库表中的向量状态字段
     * @return bool 是否成功
     */
    public function indexSingleItem($knowledge, $updateVectorStatus = true)
    {
        try {
            $item = $knowledge;
            $knowledgeId = $item['id'];

            if (!$item) {
                Log::error("知识库ID {$knowledgeId} 不存在");
                return false;
            }

            // 使用Jieba分词处理问题文本
            $segmentedText = '';
            if ($this->jiebaClient->isServiceAvailable()) {
                $segmentedText = $this->jiebaClient->segment($item['title']);
                Log::info("使用Jieba分词服务处理知识库ID {$knowledgeId} 的问题文本");
            }

            // 生成问题的向量表示
            $vector = $this->embedding->getEmbedding($item['title']);

            // 准备元数据项
            $itemData = [
                'title' => $item['title'],
                'tags' => $item['tags'],
                'category' => $item['category'],
                'weight' => $item['weight']
            ];

            // 如果有分词结果，添加到元数据项
            if (!empty($segmentedText)) {
                $itemData['segmented_text'] = $segmentedText;
            }

            // 使用VectorDBFactory创建标准元数据
            $metadata = \addons\dsassistant\library\vectordb\VectorDBFactory::createStandardMetadata($itemData);

            // 如果有分词结果，更新知识库表的分词字段
            if (!empty($segmentedText)) {
                // 同时更新知识库表的分词字段
                $updateData = [
                    'segmented_text' => $segmentedText,
                    'updatetime' => time()
                ];

                // 如果需要更新向量状态
                if ($updateVectorStatus) {
                    $updateData['has_vector'] = 1;
                    $updateData['vector_time'] = time();
                }

                Db::name('ds_knowledge')
                    ->where('id', $item['id'])
                    ->update($updateData);
            } else if ($updateVectorStatus) {
                // 如果没有分词结果但需要更新向量状态
                Db::name('ds_knowledge')
                    ->where('id', $item['id'])
                    ->update([
                        'has_vector' => 1,
                        'vector_time' => time(),
                        'updatetime' => time()
                    ]);
            }

            // 更新或插入向量
            $this->vectorDB->updateVector($this->collectionName, $item['id'], $vector, $metadata);

            Log::info("成功为知识库ID {$knowledgeId} 生成向量");
            return true;
        } catch (\Exception $e) {
            Log::error("为知识库ID {$knowledgeId} 生成向量失败: " . $e->getMessage());
            return false;
        }
    }
}
