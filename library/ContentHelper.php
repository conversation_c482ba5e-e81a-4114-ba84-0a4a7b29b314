<?php

namespace addons\dsassistant\library;

use app\admin\model\dsassistant\Contentconfig;
use app\admin\model\dsassistant\Pagecontent;

/**
 * 内容辅助类
 */
class ContentHelper
{
    /**
     * 获取配置值
     * 
     * @param string $name 配置名称
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function config($name, $default = '')
    {
        return Contentconfig::getConfig($name, $default);
    }
    
    /**
     * 获取分组配置
     * 
     * @param string $group 分组名称
     * @return array
     */
    public static function groupConfig($group)
    {
        return Contentconfig::getGroupConfig($group);
    }
    
    /**
     * 获取页面内容
     * 
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @param string $key 内容键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function content($page, $section, $key, $default = '')
    {
        return Pagecontent::getContent($page, $section, $key, $default);
    }
    
    /**
     * 获取页面所有内容
     * 
     * @param string $page 页面标识
     * @return array
     */
    public static function pageContents($page)
    {
        return Pagecontent::getPagecontents($page);
    }
    
    /**
     * 获取页面区块内容
     * 
     * @param string $page 页面标识
     * @param string $section 页面区块
     * @return array
     */
    public static function sectionContents($page, $section)
    {
        return Pagecontent::getSectionContents($page, $section);
    }
}
