<?php

namespace addons\dsassistant\library;

use think\template\TagLib;

/**
 * 景区智能助理标签库
 */
class Ds extends TagLib
{
    /**
     * 定义标签列表
     */
    protected $tags = [
        // 标签定义： attr 属性列表 close 是否闭合（0 或者1） alias 标签别名 level 嵌套层次
        'content' => ['attr' => 'page,section,key,default', 'close' => 0], // 获取内容
        'config'  => ['attr' => 'name,default', 'close' => 0], // 获取配置
    ];

    /**
     * 获取内容标签
     * 
     * @param array $tag 标签属性
     * @param string $content 标签内容
     * @return string
     */
    public function tagContent($tag, $content)
    {
        $page = isset($tag['page']) ? $tag['page'] : '';
        $section = isset($tag['section']) ? $tag['section'] : '';
        $key = isset($tag['key']) ? $tag['key'] : '';
        $default = isset($tag['default']) ? $tag['default'] : '';

        $parseStr = '<?php echo \\addons\\dsassistant\\library\\ContentHelper::content(\'' . $page . '\', \'' . $section . '\', \'' . $key . '\', \'' . $default . '\'); ?>';
        
        return $parseStr;
    }

    /**
     * 获取配置标签
     * 
     * @param array $tag 标签属性
     * @param string $content 标签内容
     * @return string
     */
    public function tagConfig($tag, $content)
    {
        $name = isset($tag['name']) ? $tag['name'] : '';
        $default = isset($tag['default']) ? $tag['default'] : '';

        $parseStr = '<?php echo \\addons\\dsassistant\\library\\ContentHelper::config(\'' . $name . '\', \'' . $default . '\'); ?>';
        
        return $parseStr;
    }
}
