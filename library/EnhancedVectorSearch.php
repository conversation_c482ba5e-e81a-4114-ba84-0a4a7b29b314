<?php

namespace addons\dsassistant\library;

// 使用完全限定的命名空间，避免IDE警告
// 这些类在运行时可用，但IDE无法识别
// \think\Db, \think\Log, \think\Cache
use addons\dsassistant\library\embedding\EmbeddingFactory;
use addons\dsassistant\library\CacheManager;
use addons\dsassistant\library\vectordb\VectorDBFactory;

/**
 * 增强的向量搜索类
 * 使用向量数据库进行高效搜索
 */
class EnhancedVectorSearch
{
    /**
     * Embedding实例
     * @var object
     */
    protected $embedding;

    /**
     * 向量数据库实例
     * @var \addons\dsassistant\library\vectordb\VectorDBInterface
     */
    protected $vectorDB;

    /**
     * 缓存前缀
     * @var string
     */
    protected $cachePrefix = 'vector_embedding_';

    /**
     * 缓存时间（秒）
     * @var int
     */
    protected $cacheTTL = 86400; // 缓存1天

    /**
     * 向量集合名称
     * @var string
     */
    protected $collectionName = 'ds_knowledge_vectors';

    /**
     * 向量维度
     * @var int
     */
    protected $dimension = 1536; // 默认维度，根据实际使用的模型调整

    /**
     * 关键词映射
     * @var array
     */
    protected $keywordMap = [];

    /**
     * 分类规则
     * @var array
     */
    protected $categoryRules = [];

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->embedding = EmbeddingFactory::getInstance();
        $this->vectorDB = VectorDBFactory::getInstance();

        // 从缓存加载关键词映射
        $keywordMapData = CacheManager::getKeywordMap();
        if (!empty($keywordMapData)) {
            foreach ($keywordMapData as $mainKeyword => $data) {
                $this->keywordMap[$mainKeyword] = $data['related_keywords'];
            }
        }

        // 从缓存加载分类规则
        $this->categoryRules = CacheManager::getCategoryRule();

        \think\Log::info('EnhancedVectorSearch初始化完成，加载了 ' . count($this->keywordMap) . ' 个关键词映射和 ' . count($this->categoryRules) . ' 个分类规则');
    }

    /**
     * 提取问题中的关键词
     *
     * @param string $title 用户问题
     * @return array 关键词数组
     */
    protected function extractKeywords($title)
    {
        $foundKeywords = [];

        // 1. 直接匹配预定义关键词
        if (!empty($this->keywordMap)) {
            foreach ($this->keywordMap as $mainKeyword => $relatedKeywords) {
                if (mb_stripos($title, $mainKeyword) !== false) {
                    $foundKeywords[] = $mainKeyword;
                    continue;
                }

                // 检查相关关键词
                if (is_array($relatedKeywords)) {
                    foreach ($relatedKeywords as $keyword) {
                        if (mb_stripos($title, $keyword) !== false) {
                            $foundKeywords[] = $mainKeyword; // 添加主关键词
                            break;
                        }
                    }
                }
            }
        }

        // 2. 使用简单的分词（可以替换为更复杂的分词算法）
        $words = preg_split('/[,，.。?？!！:：;；\s]+/u', $title);
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word, 'UTF-8') >= 2 && !in_array($word, $foundKeywords)) {
                $foundKeywords[] = $word;
            }
        }

        return $foundKeywords;
    }

    /**
     * 识别问题类别
     *
     * 使用缓存的分类规则，通过正则表达式匹配用户问题中的模式，
     * 识别问题可能属于的分类。一个问题可能匹配多个分类。
     *
     * 分类规则数据结构：
     * [
     *     '分类名称1' => [
     *         'id' => 1,
     *         'patterns' => ['模式1', '模式2'], // 正则表达式模式数组
     *         'boost' => 0.2                  // 提升因子
     *     ],
     *     // 更多分类...
     * ]
     *
     * 返回的分类数组将用于：
     * 1. 构建向量搜索的过滤条件（queryConditions['category']）
     * 2. 计算搜索结果的分类匹配加分（categoryBoost）
     *
     * 注意：返回的分类名称应与向量数据库中的category字段值一致，
     * 以确保正确的过滤和提升效果。
     *
     * @param string $title 用户问题
     * @return array 匹配的分类名称数组
     */
    protected function identifyTitleCategory($title)
    {
        $categories = [];

        // 使用缓存的分类规则
        if (!empty($this->categoryRules)) {
            foreach ($this->categoryRules as $category => $ruleData) {
                if (!empty($ruleData['patterns'])) {
                    $pattern = implode('|', $ruleData['patterns']);
                    if (preg_match("/{$pattern}/u", $title)) {
                        $categories[] = $category;
                    }
                }
            }
        }
        return $categories;
    }

    /**
     * 搜索最相似的问题
     *
     * @param string $title 用户问题
     * @param int $topK 返回结果数量
     * @return array 匹配结果
     */
    public function search($title, $topK = 5)
    {
        try {
            // 记录开始时间，用于性能分析
            $startTime = microtime(true);
            \think\Log::info("========== 开始向量搜索 ==========");
            \think\Log::info("搜索问题: \"{$title}\", 请求结果数量: {$topK}");

            // 1. 提取关键词
            $keywordStartTime = microtime(true);
            $keywords = $this->extractKeywords($title);
            $keywordEndTime = microtime(true);

            \think\Log::info("提取关键词耗时: " . round(($keywordEndTime - $keywordStartTime) * 1000, 2) . "ms");
            \think\Log::info("提取到的关键词: " . json_encode($keywords, JSON_UNESCAPED_UNICODE));

            // 2. 识别问题类别
            $categoryStartTime = microtime(true);
            $categories = $this->identifyTitleCategory($title);
            $categoryEndTime = microtime(true);

            \think\Log::info("识别问题类别耗时: " . round(($categoryEndTime - $categoryStartTime) * 1000, 2) . "ms");
            \think\Log::info("识别到的问题类别: " . json_encode($categories, JSON_UNESCAPED_UNICODE));

            // 3. 构建过滤条件
            $queryConditions = [];

            // 添加分类过滤
            if (!empty($categories)) {
                // 使用精确匹配，而不是模糊匹配
                if (count($categories) === 1) {
                    // 单个分类使用精确匹配
                    // 注意：我们使用category_id而不是category
                    // 这里需要根据分类名称查找对应的category_id
                    $categoryId = $this->getCategoryIdByName($categories[0]);
                    if ($categoryId) {
                        $queryConditions['category_id'] = $categoryId;
                        \think\Log::info("添加单个分类过滤条件: category_id = {$categoryId} (分类名称: {$categories[0]})");
                    } else {
                        // 如果找不到对应的category_id，则使用标签过滤
                        $queryConditions['tags'] = $categories[0];
                        \think\Log::info("未找到分类ID，使用标签过滤: tags LIKE '%{$categories[0]}%'");
                    }
                } else {
                    // 多个分类，使用标签过滤更灵活
                    $queryConditions['tags'] = $categories;
                    \think\Log::info("添加多个分类过滤条件，使用标签过滤: tags LIKE '%" . implode("%' OR tags LIKE '%", $categories) . "%'");
                }
            } else {
                \think\Log::info("未添加分类过滤条件");
            }

            // 添加关键词过滤
            if (!empty($keywords)) {
                $filteredKeywords = [];
                foreach ($keywords as $keyword) {
                    if (mb_strlen($keyword, 'UTF-8') >= 2) {
                        $filteredKeywords[] = $keyword;
                    }
                }

                if (!empty($filteredKeywords)) {
                    // 使用数组形式，VectorDBFactory会自动处理为OR连接的LIKE条件
                    $queryConditions['tags'] = $filteredKeywords;
                    \think\Log::info("添加关键词过滤条件: tags LIKE '%" . implode("%' OR tags LIKE '%", $filteredKeywords) . "%'");
                } else {
                    \think\Log::info("关键词长度不足，未添加关键词过滤条件");
                }
            } else {
                \think\Log::info("未提取到关键词，未添加关键词过滤条件");
            }

            // 使用VectorDBFactory转换查询条件
            $filterStartTime = microtime(true);
            $filter = \addons\dsassistant\library\vectordb\VectorDBFactory::convertFilter($queryConditions);
            $filterEndTime = microtime(true);

            \think\Log::info("转换查询条件耗时: " . round(($filterEndTime - $filterStartTime) * 1000, 2) . "ms");
            \think\Log::info("最终查询条件: " . json_encode($filter, JSON_UNESCAPED_UNICODE));

            // 4. 生成问题的向量表示（使用缓存减少API调用）
            $embeddingStartTime = microtime(true);
            $cacheKey = $this->cachePrefix . md5($title);
            $questionVector = \think\Cache::get($cacheKey);

            if (!$questionVector) {
                \think\Log::info("向量缓存未命中，调用Embedding API生成向量");
                $questionVector = $this->embedding->getEmbedding($title);
                \think\Cache::set($cacheKey, $questionVector, $this->cacheTTL);
                \think\Log::info("向量生成完成，已缓存，有效期: {$this->cacheTTL}秒");
            } else {
                \think\Log::info("向量缓存命中，使用缓存的向量表示");
            }
            $embeddingEndTime = microtime(true);

            \think\Log::info("生成向量表示耗时: " . round(($embeddingEndTime - $embeddingStartTime) * 1000, 2) . "ms");
            \think\Log::info("向量维度: " . count($questionVector));

            // 5. 使用向量数据库搜索
            $searchStartTime = microtime(true);
            \think\Log::info("开始向量数据库搜索，集合名称: {$this->collectionName}, 请求数量: " . ($topK * 3));

            $searchResults = $this->vectorDB->search(
                $this->collectionName,
                $questionVector,
                $topK * 3, // 获取更多结果，以便后续处理
                $filter
            );

            $searchEndTime = microtime(true);
            \think\Log::info("向量数据库搜索耗时: " . round(($searchEndTime - $searchStartTime) * 1000, 2) . "ms, 获取到 " . count($searchResults) . " 条结果");

            // 记录向量搜索时间
            $vectorTime = microtime(true);
            \think\Log::record("向量搜索阶段总耗时: " . round(($vectorTime - $startTime) * 1000, 2) . "ms", 'info');

            if (empty($searchResults)) {
                \think\Log::info("向量搜索未返回结果，尝试不使用过滤条件搜索");

                // 尝试不使用过滤条件搜索
                $noFilterResults = $this->vectorDB->search(
                    $this->collectionName,
                    $questionVector,
                    $topK * 3,
                    [] // 空过滤条件
                );

                if (empty($noFilterResults)) {
                    \think\Log::info("不使用过滤条件搜索仍未返回结果，可能是向量数据库中没有相关数据");
                    \think\Log::info("========== 向量搜索结束 ==========");
                    return [];
                }

                \think\Log::info("不使用过滤条件搜索返回了 " . count($noFilterResults) . " 条结果");
                $searchResults = $noFilterResults;
            }

            // 记录前几条原始搜索结果
            $logLimit = min(5, count($searchResults));
            \think\Log::info("前{$logLimit}条原始搜索结果:");
            for ($i = 0; $i < $logLimit; $i++) {
                \think\Log::info("  [{$i}] ID: {$searchResults[$i]['id']}, 相似度: {$searchResults[$i]['similarity']}");
            }

            // 6. 获取知识库详细信息
            $dbStartTime = microtime(true);
            $knowledgeIds = array_column($searchResults, 'id');
            \think\Log::info("从数据库获取知识库详细信息，ID列表: " . implode(", ", $knowledgeIds));

            $knowledgeItems = \think\Db::name('ds_knowledge')
                ->where('id', 'in', $knowledgeIds)
                ->select();

            \think\Log::info("从数据库获取到 " . count($knowledgeItems) . " 条知识库记录");

            // 构建ID到知识条目的映射
            $knowledgeMap = [];
            foreach ($knowledgeItems as $item) {
                $knowledgeMap[$item['id']] = $item;
            }
            $dbEndTime = microtime(true);

            \think\Log::info("数据库查询和映射构建耗时: " . round(($dbEndTime - $dbStartTime) * 1000, 2) . "ms");

            // 7. 计算最终得分
            \think\Log::info("开始计算最终得分");
            $scoringStartTime = microtime(true);
            $results = [];
            $processedCount = 0;
            $missingCount = 0;

            foreach ($searchResults as $result) {
                $id = $result['id'];
                if (isset($knowledgeMap[$id])) {
                    $item = $knowledgeMap[$id];
                    $processedCount++;

                    // 基础相似度
                    $similarity = $result['similarity'];

                    // 权重调整
                    $weightFactor = min(0.2, $item['weight'] / 500);

                    // 分类匹配加分
                    $categoryBoost = 0;
                    $matchedCategory = '';
                    if (!empty($categories) && isset($item['category_id'])) {
                        foreach ($categories as $category) {
                            // 获取分类ID
                            $categoryId = $this->getCategoryIdByName($category);

                            // 如果分类ID匹配
                            if ($categoryId && $item['category_id'] == $categoryId) {
                                // 使用缓存的分类规则中的boost值
                                if (isset($this->categoryRules[$category]) && isset($this->categoryRules[$category]['boost'])) {
                                    $categoryBoost = floatval($this->categoryRules[$category]['boost']);
                                    $matchedCategory = $category;
                                } else {
                                    $categoryBoost = 0.1; // 默认加分
                                    $matchedCategory = $category . '(默认加分)';
                                }
                                break;
                            }

                            // 如果在标签中匹配
                            if (!empty($item['tags']) && strpos($item['tags'], $category) !== false) {
                                // 使用缓存的分类规则中的boost值
                                if (isset($this->categoryRules[$category]) && isset($this->categoryRules[$category]['boost'])) {
                                    $categoryBoost = floatval($this->categoryRules[$category]['boost']) * 0.8; // 标签匹配给予80%的加分
                                    $matchedCategory = $category . '(标签匹配)';
                                } else {
                                    $categoryBoost = 0.08; // 默认加分
                                    $matchedCategory = $category . '(标签匹配,默认加分)';
                                }
                                break;
                            }
                        }
                    }

                    // 关键词匹配加分
                    $keywordBoost = 0;
                    $matchedKeywords = [];
                    if (!empty($keywords) && !empty($item['tags'])) {
                        $itemKeywords = explode(',', $item['tags']);
                        $matchCount = 0;
                        foreach ($keywords as $keyword) {
                            foreach ($itemKeywords as $itemKeyword) {
                                if (mb_stripos($itemKeyword, $keyword) !== false) {
                                    $matchCount++;
                                    $matchedKeywords[] = $keyword . '→' . $itemKeyword;
                                    break;
                                }
                            }
                        }
                        $keywordBoost = min(0.2, $matchCount * 0.05);
                    }

                    // 计算最终得分
                    $finalScore = $similarity + $weightFactor + $categoryBoost + $keywordBoost;

                    // 记录详细的相似度计算信息
                    \think\Log::info("知识库ID {$id}, 标题: \"{$item['title']}\", 相似度: {$similarity}, 权重因子: {$weightFactor}, 分类加分: {$categoryBoost}" .
                        ($matchedCategory ? " (匹配分类: {$matchedCategory})" : "") .
                        ", 关键词加分: {$keywordBoost}" .
                        (count($matchedKeywords) > 0 ? " (匹配关键词: " . implode(', ', $matchedKeywords) . ")" : "") .
                        ", 最终得分: {$finalScore}");

                    $results[] = [
                        'id' => $id,
                        'title' => $item['title'],
                        'content' => $item['content'],
                        'score' => $finalScore,
                        'similarity' => $similarity,
                        'weight_factor' => $weightFactor,
                        'category_boost' => $categoryBoost,
                        'keyword_boost' => $keywordBoost,
                        'matched_category' => $matchedCategory,
                        'matched_keywords' => $matchedKeywords
                    ];
                } else {
                    $missingCount++;
                    \think\Log::warning("知识库ID {$id} 在数据库中不存在，可能已被删除");
                }
            }
            $scoringEndTime = microtime(true);

            \think\Log::info("得分计算耗时: " . round(($scoringEndTime - $scoringStartTime) * 1000, 2) . "ms, 处理了 {$processedCount} 条记录, {$missingCount} 条记录在数据库中不存在");

            // 8. 排序并返回结果
            $sortStartTime = microtime(true);
            usort($results, function($a, $b) {
                return $b['score'] <=> $a['score'];
            });

            $topResults = array_slice($results, 0, $topK);
            $sortEndTime = microtime(true);

            \think\Log::info("排序和截取耗时: " . round(($sortEndTime - $sortStartTime) * 1000, 2) . "ms, 返回前 {$topK} 条结果");

            // 记录总耗时
            $endTime = microtime(true);
            \think\Log::record("向量搜索总耗时: " . round(($endTime - $startTime) * 1000, 2) . "ms", 'info');

            // 记录最佳匹配
            if (!empty($topResults)) {
                \think\Log::info("========== 搜索结果 ==========");
                \think\Log::info("最佳匹配: \"{$topResults[0]['title']}\", 得分: {$topResults[0]['score']}");

                // 记录所有返回结果
                foreach ($topResults as $index => $result) {
                    \think\Log::info("结果 #{$index}: ID: {$result['id']}, 标题: \"{$result['title']}\", 得分: {$result['score']}");
                }
            } else {
                \think\Log::info("未找到匹配项");
            }

            \think\Log::info("========== 向量搜索结束 ==========");

            return $topResults;
        } catch (\Exception $e) {
            \think\Log::error("向量搜索失败: " . $e->getMessage());
            \think\Log::error("异常堆栈: " . $e->getTraceAsString());
            \think\Log::info("========== 向量搜索异常结束 ==========");
            return [];
        }
    }

    /**
     * 为知识库生成向量索引
     *
     * 使用VectorDBFactory中的辅助方法处理知识库数据，
     * 自动转换为标准元数据格式，并批量处理向量数据。
     *
     * @param array|null $knowledgeList 知识库条目列表，如果为null则处理所有正常状态的知识库条目
     * @param bool $updateVectorStatus 是否更新知识库表中的向量状态字段
     * @return int 索引的条目数
     */
    public function indexKnowledgeBase($knowledgeList = null, $updateVectorStatus = true)
    {
        // 如果没有提供知识库列表，则获取所有正常状态的知识库条目
        if ($knowledgeList === null) {
            $knowledgeList = \think\Db::name('ds_knowledge')
                ->where('status', 'normal')
                ->select();
        }

        $count = 0;
        $errors = 0;
        $startTime = microtime(true);

        \think\Log::record("开始为 " . count($knowledgeList) . " 条知识库条目生成向量索引", 'info');

        // 分批处理，每批10条
        $batches = array_chunk($knowledgeList, 10);

        foreach ($batches as $batchIndex => $batch) {
            try {
                $batchVectors = [];
                $batchItems = [];
                $batchEmbeddings = [];

                foreach ($batch as $item) {
                    try {
                        // 生成问题的向量表示
                        $vector = $this->embedding->getEmbedding($item['title']);

                        // 保存向量和知识库条目，用于后续批量处理
                        $batchItems[$item['id']] = $item;
                        $batchEmbeddings[$item['id']] = $vector;

                        // 如果需要更新向量状态
                        if ($updateVectorStatus) {
                            \think\Db::name('ds_knowledge')
                                ->where('id', $item['id'])
                                ->update([
                                    'has_vector' => 1,
                                    'vector_time' => time()
                                ]);
                        }

                        $count++;
                    } catch (\Exception $e) {
                        \think\Log::error("为知识库ID {$item['id']} 生成向量失败: " . $e->getMessage());
                        $errors++;
                    }
                }

                // 使用VectorDBFactory创建批量向量数据
                if (!empty($batchItems) && !empty($batchEmbeddings)) {
                    $batchVectors = \addons\dsassistant\library\vectordb\VectorDBFactory::createVectorBatch(
                        $batchItems,
                        $batchEmbeddings
                    );

                    // 批量插入向量
                    $this->vectorDB->batchInsertVectors($this->collectionName, $batchVectors);
                }

                // 每批处理完后休息1秒，避免API限制
                if ($batchIndex < count($batches) - 1) {
                    sleep(1);
                }

                // 记录进度
                $progress = round(($batchIndex + 1) / count($batches) * 100, 2);
                \think\Log::info("向量索引进度: {$progress}%, 已处理: {$count}条, 错误: {$errors}条");

            } catch (\Exception $e) {
                \think\Log::error("处理批次 {$batchIndex} 时出错: " . $e->getMessage());
                // 如果批处理失败，暂停一段时间后继续
                sleep(5);
            }
        }

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);

        \think\Log::record("向量索引完成，共处理 {$count} 条记录，失败 {$errors} 条，耗时 {$duration} 秒", 'info');

        return $count;
    }

    /**
     * 为单个知识条目生成向量
     *
     * @param \app\admin\model\dsassistant\Knowledge|array $knowledge 知识库对象
     * @param bool $updateVectorStatus 是否更新知识库表中的向量状态字段
     * @return bool 是否成功
     */
    public function indexSingleItem($knowledge, $updateVectorStatus = true)
    {
        try {
            $item = $knowledge;
            $knowledgeId = $item['id'];

            if (!$item) {
                \think\Log::error("知识库ID {$knowledgeId} 不存在");
                return false;
            }

            // 生成问题的向量表示
            $vector = $this->embedding->getEmbedding($item['title']);


            // 使用VectorDBFactory创建标准元数据
            $standardMetadata = \addons\dsassistant\library\vectordb\VectorDBFactory::createStandardMetadata($item);

            // 更新或插入向量
            $this->vectorDB->updateVector($this->collectionName, $item['id'], $vector, $standardMetadata);

            // 如果需要更新向量状态
            if ($updateVectorStatus) {
                \think\Db::name('ds_knowledge')
                    ->where('id', $knowledgeId)
                    ->update([
                        'has_vector' => 1,
                        'vector_time' => time()
                    ]);
            }

            \think\Log::info("成功为知识库ID {$knowledgeId} 生成向量");
            return true;
        } catch (\Exception $e) {
            \think\Log::error("为知识库ID {$knowledgeId} 生成向量失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除单个知识条目的向量
     *
     * 当知识库条目被删除时，应调用此方法删除对应的向量数据，
     * 以保持知识库和向量数据库的一致性。
     *
     * @param int|string $knowledgeId 知识库条目ID
     * @return bool 是否成功删除
     */
    public function deleteSingleItem($knowledgeId)
    {
        try {
            // 从向量数据库中删除向量
            $result = $this->vectorDB->deleteVector($this->collectionName, $knowledgeId);

            if ($result) {
                \think\Log::info("成功删除知识库ID {$knowledgeId} 的向量数据");
            } else {
                \think\Log::warning("删除知识库ID {$knowledgeId} 的向量数据失败，可能不存在");
            }

            return $result;
        } catch (\Exception $e) {
            \think\Log::error("删除知识库ID {$knowledgeId} 的向量数据异常: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 计算两个向量的余弦相似度
     *
     * @param array $vec1 向量1
     * @param array $vec2 向量2
     * @return float 相似度
     */
    public function cosineSimilarity($vec1, $vec2)
    {
        return $this->vectorDB->calculateSimilarity($vec1, $vec2);
    }

    /**
     * 根据分类名称获取分类ID
     *
     * 使用缓存的分类规则，查找分类名称对应的ID
     *
     * @param string $categoryName 分类名称
     * @return int|null 分类ID，如果未找到则返回null
     */
    protected function getCategoryIdByName($categoryName)
    {
        // 使用缓存的分类规则
        if (!empty($this->categoryRules) && isset($this->categoryRules[$categoryName])) {
            return isset($this->categoryRules[$categoryName]['id']) ?
                   intval($this->categoryRules[$categoryName]['id']) : null;
        }

        // 如果在缓存中找不到，尝试从数据库查询
        try {
            // 分类表名为ds_category，字段为id和name
            $category = \think\Db::name('ds_category')
                ->where('name', $categoryName)
                ->find();

            if ($category) {
                return intval($category['id']);
            }
        } catch (\Exception $e) {
            \think\Log::error("根据分类名称查询分类ID失败: " . $e->getMessage());
        }

        // 特殊处理一些常见分类
        $specialCategories = [
            '百花园' => 3,
            '青山瀑布' => 1,
            '翠谷幽林' => 2,
            '天空栈道' => 4,
            '古镇风情街' => 5
        ];

        if (isset($specialCategories[$categoryName])) {
            return $specialCategories[$categoryName];
        }

        return null;
    }
}
