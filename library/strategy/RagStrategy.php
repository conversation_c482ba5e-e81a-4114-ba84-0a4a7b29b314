<?php

namespace addons\dsassistant\library\strategy;

use think\Log;
use addons\dsassistant\library\VectorSearchFactory;
use addons\dsassistant\library\SessionManager;
use addons\dsassistant\library\llm\LLMClient;

/**
 * RAG回答策略
 *
 * 实现检索增强生成(RAG)模式，将本地知识库的检索结果作为上下文提供给DeepSeek大模型
 */
class RagStrategy implements AnswerStrategyInterface
{
    /**
     * 向量搜索实例
     * @var \addons\dsassistant\library\EnhancedVectorSearch|\addons\dsassistant\library\JiebaVectorSearch
     */
    protected $vectorSearch = null;

    /**
     * 生成回答
     *
     * @param string $question 用户问题
     * @param string $sessionId 会话ID
     * @param array $config 配置信息
     * @return array 包含回答内容、来源和得分的数组
     */
    public function generateAnswer($question, $sessionId, $config)
    {
        // 获取RAG配置
        $topK = isset($config['rag_top_k']) ? $config['rag_top_k'] : 3;
        $minScore = isset($config['rag_min_score']) ? floatval($config['rag_min_score']) : 0.3;

        // 从本地知识库检索相关信息
        $localResults = $this->searchFAQ($question, $topK);

        // 记录日志
        Log::record("RAG模式：从本地知识库检索到 " . count($localResults) . " 条相关信息", 'info');

        // 准备提供给DeepSeek的上下文信息
        $contextInfo = "";
        $hasValidContext = false;

        if (!empty($localResults)) {
            // 按得分排序
            usort($localResults, function($a, $b) {
                return $b['score'] <=> $a['score'];
            });

            // 只使用得分高于阈值的结果
            $filteredResults = array_filter($localResults, function($result) use ($minScore) {
                return $result['score'] >= $minScore;
            });

            // 使用配置中的上下文格式，如果没有则使用默认格式
            $contextFormat = isset($config['rag_context_format']) ?
                $config['rag_context_format'] :
                "问题: {question}\n回答: {answer}\n\n";

            // 去除重复信息
            $usedContent = [];
            foreach ($filteredResults as $result) {
                $content = $result['answer'];
                if (!in_array($content, $usedContent)) {
                    $formattedContext = str_replace(
                        ['{question}', '{answer}'],
                        [$result['question'], $content],
                        $contextFormat
                    );
                    $contextInfo .= $formattedContext;
                    $usedContent[] = $content;
                    $hasValidContext = true;
                }
            }
        }

        // 调用DeepSeek API，将本地知识库信息作为上下文
        try {
            $aiResponse = $this->callDeepSeekWithContext($question, $contextInfo, $sessionId, $config);
            if ($this->confidenceCheck($aiResponse)) {
                return [
                    'answer' => $aiResponse,
                    'source' => 'ai',
                    'score' => 0.9, // AI生成的默认得分
                    'extra' => json_encode([
                        'strategy' => 'rag',
                        'has_local_context' => $hasValidContext,
                        'context_count' => count($localResults)
                    ])
                ];
            }
        } catch (\Exception $e) {
            Log::error('LLM API调用失败: ' . $e->getMessage());
        }

        // 兜底策略
        $customerServicePhone = $config['customer_service_phone'] ?: '400-xxx-xxxx';
        return [
            'answer' => "抱歉，我无法回答这个问题。请致电客服：{$customerServicePhone}",
            'source' => 'local',
            'score' => 0,
            'extra' => json_encode(['strategy' => 'rag', 'fallback' => true])
        ];
    }

    /**
     * 在本地知识库中搜索问题
     *
     * @param string $question 用户问题
     * @param int $topK 返回的结果数量
     * @return array 匹配结果数组
     */
    protected function searchFAQ($question, $topK = 3)
    {
        // 初始化向量搜索实例（如果尚未初始化）
        if ($this->vectorSearch === null) {
            $this->vectorSearch = VectorSearchFactory::getInstance();
            Log::info('使用VectorSearchFactory初始化向量搜索实例');
        }

        // 使用向量搜索
        $vectorResults = $this->vectorSearch->search($question, max(5, $topK));

        // 记录日志，用于调试
        Log::record('处理问题: ' . $question, 'info');
        Log::record('向量搜索结果数量: ' . count($vectorResults), 'info');

        if (!empty($vectorResults)) {
            $results = [];

            // 处理所有结果
            foreach ($vectorResults as $index => $result) {
                Log::record("匹配结果 #{$index}: 标题=\"{$result['title']}\", 得分={$result['score']}", 'info');

                if ($index < $topK) {
                    $results[] = [
                        'question' => $result['title'],   // 使用title字段作为question
                        'answer' => $result['content'],   // 使用content字段作为answer
                        'score' => $result['score']
                    ];
                }
            }

            return $results;
        } else {
            Log::record("未找到匹配项", 'info');
            return [];
        }
    }

    /**
     * 使用本地知识库上下文调用DeepSeek API
     *
     * @param string $question 用户问题
     * @param string $contextInfo 本地知识库提供的上下文信息
     * @param string $sessionId 会话ID
     * @param array $config 配置信息
     * @return string AI生成的回答
     */
    protected function callDeepSeekWithContext($question, $contextInfo, $sessionId, $config)
    {
        $model = $config['deepseek_model'] ?: 'deepseek-chat';

        // 基础系统提示
        $basePrompt = $config['system_prompt'] ?: '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。';

        // 增强系统提示，加入本地知识库信息
        $systemPrompt = $basePrompt;
        if (!empty($contextInfo)) {
            // 使用配置中的提示模板，如果没有则使用默认模板
            $promptTemplate = isset($config['rag_prompt_template']) ?
                $config['rag_prompt_template'] :
                "以下是关于用户问题的相关信息，请参考这些信息回答问题，但不要直接复制这些内容，而是用自然、流畅的语言回答：\n\n{context}";

            $systemPrompt .= "\n\n" . str_replace('{context}', $contextInfo, $promptTemplate);

            Log::record("使用RAG模式，添加了本地知识库上下文", 'info');
        }

        // 准备消息
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt]
        ];

        // 如果有会话ID，获取会话上下文
        if (!empty($sessionId)) {
            $sessionState = SessionManager::getSessionState($sessionId);
            if ($sessionState && !empty($sessionState['context'])) {
                // 使用会话上下文
                $context = $sessionState['context'];

                // 如果上下文太长，只保留最近的几轮对话
                if (count($context) > SessionManager::MAX_HISTORY_SIZE * 2) {
                    $context = array_slice($context, -SessionManager::MAX_HISTORY_SIZE * 2);
                }

                // 合并上下文和系统提示
                $messages = array_merge($messages, $context);

                // 添加当前问题
                $messages[] = ['role' => 'user', 'content' => $question];

                Log::record("使用会话上下文，共 " . count($context) . " 条消息", 'info');
            } else {
                // 没有上下文，只添加当前问题
                $messages[] = ['role' => 'user', 'content' => $question];
            }
        } else {
            // 没有会话ID，只添加当前问题
            $messages[] = ['role' => 'user', 'content' => $question];
        }

        try {
            // 创建LLMClient实例，使用指定的模型或默认模型
            $llmClient = new LLMClient($model);

            // 调用LLM API
            return $llmClient->chatCompletion($messages);
        } catch (\Exception $e) {
            Log::error('LLM API调用失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检查AI回答的置信度
     *
     * @param string $answer AI回答
     * @return bool 是否通过置信度检查
     */
    protected function confidenceCheck($answer)
    {
        // 简单实现，可以根据实际需求调整
        if (empty($answer)) {
            return false;
        }

        if (strpos($answer, '我不知道') !== false ||
            strpos($answer, '无法回答') !== false ||
            strpos($answer, '没有相关信息') !== false) {
            return false;
        }

        return true;
    }
}
