<?php

namespace addons\dsassistant\library\strategy;

use think\Log;
use addons\dsassistant\library\VectorSearchFactory;
use addons\dsassistant\library\SessionManager;
use addons\dsassistant\library\llm\LLMClient;

/**
 * 传统回答策略
 *
 * 先尝试从本地知识库找答案，如果本地知识库能提供高置信度的答案，就使用本地答案
 * 只有当本地知识库无法提供满意答案时，才调用DeepSeek大模型
 */
class TraditionalStrategy implements AnswerStrategyInterface
{
    /**
     * 向量搜索实例
     * @var \addons\dsassistant\library\EnhancedVectorSearch|\addons\dsassistant\library\JiebaVectorSearch
     */
    protected $vectorSearch = null;

    /**
     * 生成回答
     *
     * @param string $question 用户问题
     * @param string $sessionId 会话ID
     * @param array $config 配置信息
     * @return array 包含回答内容、来源和得分的数组
     */
    public function generateAnswer($question, $sessionId, $config)
    {
        // 1. 本地知识库查询
        $localAnswer = $this->searchFAQ($question);
        $confidenceThreshold = $config['confidence_threshold'] ?: 0.3; // 默认阈值，降低为0.3使更容易匹配

        if ($localAnswer && $localAnswer['score'] > $confidenceThreshold) {
            return [
                'answer' => $localAnswer['answer'],
                'source' => 'local',
                'score' => $localAnswer['score'],
                'extra' => json_encode(['strategy' => 'traditional'])
            ];
        }

        // 2. 调用AI生成（使用会话上下文）
        try {
            $aiResponse = $this->callDeepSeek($question, $sessionId, $config);
            if ($this->confidenceCheck($aiResponse)) {
                return [
                    'answer' => $aiResponse,
                    'source' => 'ai',
                    'score' => 0.9, // AI生成的默认得分
                    'extra' => json_encode(['strategy' => 'traditional'])
                ];
            }
        } catch (\Exception $e) {
            Log::error('LLM API调用失败: ' . $e->getMessage());
        }

        // 3. 兜底策略
        $customerServicePhone = $config['customer_service_phone'] ?: '400-xxx-xxxx';
        return [
            'answer' => "抱歉，我无法回答这个问题。请致电客服：{$customerServicePhone}",
            'source' => 'local',
            'score' => 0,
            'extra' => json_encode(['strategy' => 'traditional', 'fallback' => true])
        ];
    }

    /**
     * 在本地知识库中搜索问题
     *
     * @param string $question 用户问题
     * @return array|null 匹配结果
     */
    protected function searchFAQ($question)
    {
        // 初始化向量搜索实例（如果尚未初始化）
        if ($this->vectorSearch === null) {
            $this->vectorSearch = VectorSearchFactory::getInstance();
            Log::info('使用VectorSearchFactory初始化向量搜索实例');
        }

        // 使用向量搜索
        $vectorResults = $this->vectorSearch->search($question, 5);

        // 记录日志，用于调试
        Log::record('处理问题: ' . $question, 'info');
        Log::record('向量搜索结果数量: ' . count($vectorResults), 'info');

        if (!empty($vectorResults)) {
            $bestMatch = $vectorResults[0];

            // 记录最佳匹配结果
            Log::record("最佳匹配: {$bestMatch['title']}, 得分: {$bestMatch['score']}", 'info');

            return [
                'question' => $bestMatch['title'],   // 使用title字段作为question
                'answer' => $bestMatch['content'],   // 使用content字段作为answer
                'score' => $bestMatch['score']
            ];
        } else {
            Log::record("未找到匹配项", 'info');
            return null;
        }
    }

    /**
     * 调用DeepSeek API
     *
     * @param string $question 用户问题
     * @param string $sessionId 会话ID
     * @param array $config 配置信息
     * @return string AI生成的回答
     */
    protected function callDeepSeek($question, $sessionId, $config)
    {
        $model = $config['deepseek_model'] ?: 'deepseek-chat';
        $systemPrompt = $config['system_prompt'] ?: '你是一个专业的景区导游，负责回答游客关于景区的各种问题，包括门票价格、开放时间、交通路线、景点介绍等。请用简洁、专业、友好的语气回答问题。';

        // 准备消息
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt]
        ];

        // 如果有会话ID，获取会话上下文
        if (!empty($sessionId)) {
            $sessionState = SessionManager::getSessionState($sessionId);
            if ($sessionState && !empty($sessionState['context'])) {
                // 使用会话上下文
                $context = $sessionState['context'];

                // 如果上下文太长，只保留最近的几轮对话
                if (count($context) > SessionManager::MAX_HISTORY_SIZE * 2) {
                    $context = array_slice($context, -SessionManager::MAX_HISTORY_SIZE * 2);
                }

                // 合并上下文和系统提示
                $messages = array_merge($messages, $context);

                // 添加当前问题
                $messages[] = ['role' => 'user', 'content' => $question];

                Log::record("使用会话上下文，共 " . count($context) . " 条消息", 'info');
            } else {
                // 没有上下文，只添加当前问题
                $messages[] = ['role' => 'user', 'content' => $question];
            }
        } else {
            // 没有会话ID，只添加当前问题
            $messages[] = ['role' => 'user', 'content' => $question];
        }

        try {
            // 创建LLMClient实例，使用指定的模型或默认模型
            $llmClient = new LLMClient($model);

            // 调用LLM API
            return $llmClient->chatCompletion($messages);
        } catch (\Exception $e) {
            Log::error('LLM API调用失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检查AI回答的置信度
     *
     * @param string $answer AI回答
     * @return bool 是否通过置信度检查
     */
    protected function confidenceCheck($answer)
    {
        // 简单实现，可以根据实际需求调整
        if (empty($answer)) {
            return false;
        }

        if (strpos($answer, '我不知道') !== false ||
            strpos($answer, '无法回答') !== false ||
            strpos($answer, '没有相关信息') !== false) {
            return false;
        }

        return true;
    }
}
