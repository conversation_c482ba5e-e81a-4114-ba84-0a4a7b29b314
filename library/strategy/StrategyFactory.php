<?php

namespace addons\dsassistant\library\strategy;

use think\Log;

/**
 * 策略工厂类
 * 
 * 负责创建和管理不同的回答策略
 */
class StrategyFactory
{
    /**
     * 策略实例缓存
     * @var array
     */
    protected static $instances = [];
    
    /**
     * 获取回答策略实例
     *
     * @param string $strategyType 策略类型，可选值：'traditional', 'rag'
     * @param array $config 配置信息
     * @return AnswerStrategyInterface 回答策略实例
     */
    public static function getStrategy($strategyType = null, $config = [])
    {
        // 如果未指定策略类型，根据配置决定
        if ($strategyType === null) {
            // 检查RAG模式是否启用
            $ragEnabled = isset($config['rag_enabled']) ? (bool)$config['rag_enabled'] : true;
            $strategyType = $ragEnabled ? 'rag' : 'traditional';
        }
        
        // 如果已经有缓存的实例，直接返回
        if (isset(self::$instances[$strategyType])) {
            return self::$instances[$strategyType];
        }
        
        // 创建新的策略实例
        switch ($strategyType) {
            case 'rag':
                Log::record("使用RAG回答策略", 'info');
                self::$instances[$strategyType] = new RagStrategy();
                break;
            case 'traditional':
            default:
                Log::record("使用传统回答策略", 'info');
                self::$instances[$strategyType] = new TraditionalStrategy();
                break;
        }
        
        return self::$instances[$strategyType];
    }
}
