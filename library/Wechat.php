<?php

namespace addons\dsassistant\library;

use think\Cache;
use think\Log;
use think\Config;

/**
 * 微信公众号操作类
 */
class Wechat
{
    protected $appid;
    protected $appsecret;
    protected $token;
    protected $templateId;

    public function __construct($config = [])
    {
        $this->appid = $config['appid'] ?? '';
        $this->appsecret = $config['appsecret'] ?? '';
        $this->token = $config['token'] ?? '';
        $this->templateId = $config['template_id'] ?? '';
    }

    /**
     * 验证签名
     */
    public function checkSignature()
    {
        $signature = $_GET['signature'] ?? '';
        $timestamp = $_GET['timestamp'] ?? '';
        $nonce = $_GET['nonce'] ?? '';

        if (empty($signature) || empty($timestamp) || empty($nonce)) {
            return false;
        }

        $tmpArr = [$this->token, $timestamp, $nonce];
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);

        return $tmpStr === $signature;
    }

    /**
     * 解析微信消息
     */
    public function parseMessage($postStr)
    {
        if (empty($postStr)) {
            throw new \Exception('Empty message');
        }

        // 安全解析XML，PHP 8.0+ 不再需要 libxml_disable_entity_loader
        if (function_exists('libxml_disable_entity_loader')) {
            libxml_disable_entity_loader(true);
        }
        $postObj = simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);

        if (empty($postObj)) {
            throw new \Exception('Invalid XML');
        }

        $message = [];
        foreach ($postObj as $key => $value) {
            $message[$key] = strval($value);
        }

        return $message;
    }

    /**
     * 构建文本消息
     */
    public function buildTextMessage($toUser, $content)
    {
        $time = time();

        // 在微信公众号的被动回复消息中，FromUserName应该是公众号的原始ID
        // 这里我们从请求中获取，如果没有则使用配置的appid
        $fromUser = isset($_GET['openid']) ? $_GET['openid'] : $this->appid;

        return "<xml>
                <ToUserName><![CDATA[{$toUser}]]></ToUserName>
                <FromUserName><![CDATA[{$fromUser}]]></FromUserName>
                <CreateTime>{$time}</CreateTime>
                <MsgType><![CDATA[text]]></MsgType>
                <Content><![CDATA[{$content}]]></Content>
                </xml>";
    }

    /**
     * 构建图文消息
     */
    public function buildNewsMessage($toUser, $articles)
    {
        $time = time();
        $count = count($articles);
        $itemStr = '';

        foreach ($articles as $item) {
            $itemStr .= "<item>
                        <Title><![CDATA[{$item['title']}]]></Title>
                        <Description><![CDATA[{$item['description']}]]></Description>
                        <PicUrl><![CDATA[{$item['picurl']}]]></PicUrl>
                        <Url><![CDATA[{$item['url']}]]></Url>
                        </item>";
        }

        // 在微信公众号的被动回复消息中，FromUserName应该是公众号的原始ID
        // 这里我们从请求中获取，如果没有则使用配置的appid
        $fromUser = isset($_GET['openid']) ? $_GET['openid'] : $this->appid;

        return "<xml>
                <ToUserName><![CDATA[{$toUser}]]></ToUserName>
                <FromUserName><![CDATA[{$fromUser}]]></FromUserName>
                <CreateTime>{$time}</CreateTime>
                <MsgType><![CDATA[news]]></MsgType>
                <ArticleCount>{$count}</ArticleCount>
                <Articles>{$itemStr}</Articles>
                </xml>";
    }

    /**
     * 获取微信访问令牌
     */
    public function getAccessToken()
    {
        // 先从缓存获取
        $accessToken = Cache::get('wechat_access_token');
        if ($accessToken) {
            return $accessToken;
        }

        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appid}&secret={$this->appsecret}";
        $result = $this->httpGet($url);
        $json = json_decode($result, true);

        if (isset($json['access_token'])) {
            // 将令牌存入缓存，有效期比微信返回的少300秒，避免临界点问题
            $expiresIn = isset($json['expires_in']) ? $json['expires_in'] - 300 : 7200 - 300;
            Cache::set('wechat_access_token', $json['access_token'], $expiresIn);
            return $json['access_token'];
        }

        throw new \Exception('Failed to get access token: ' . $result);
    }

    /**
     * 创建自定义菜单
     */
    public function createMenu($menuData)
    {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token={$accessToken}";
        $result = $this->httpPost($url, json_encode($menuData, JSON_UNESCAPED_UNICODE));
        $json = json_decode($result, true);

        if (isset($json['errcode']) && $json['errcode'] === 0) {
            return true;
        }

        throw new \Exception('Failed to create menu: ' . $result);
    }


    /**
     * 获取用户信息
     *
     * @param string $openid 用户OpenID
     * @return array|bool 用户信息或失败
     */
    public function getUserInfo($openid)
    {
        $accessToken = $this->getAccessToken();
        if (!$accessToken) {
            return false;
        }

        $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$accessToken}&openid={$openid}&lang=zh_CN";
        $response = $this->httpGet($url);
        $result = json_decode($response, true);

        if (isset($result['errcode']) && $result['errcode'] != 0) {
            Log::error('获取微信用户信息失败: ' . $response);
            return false;
        }

        return $result;
    }

    /**
     * 发送模板消息
     *
     * @param string $openid 用户OpenID
     * @param array $data 模板数据
     * @param string $url 点击跳转的URL
     * @return bool 是否发送成功
     */
    public function sendTemplateMessage($openid, $data, $url = '')
    {
        $accessToken = $this->getAccessToken();
        if (!$accessToken) {
            return false;
        }

        $apiUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$accessToken}";

        $message = [
            'touser' => $openid,
            'template_id' => $this->templateId,
            'url' => $url,
            'data' => $data
        ];

        $response = $this->httpPost($apiUrl, json_encode($message, JSON_UNESCAPED_UNICODE));
        $result = json_decode($response, true);

        if (isset($result['errcode']) && $result['errcode'] == 0) {
            return true;
        } else {
            Log::error('发送微信模板消息失败: ' . $response);
            return false;
        }
    }

    /**
     * 发送预警模板消息
     *
     * @param string $openid 用户OpenID
     * @param array $warning 预警信息
     * @return bool 是否发送成功
     */
    public function sendWarningMessage($openid, $warning)
    {
        // 构建模板消息数据
        // 注意：这里的字段需要与你在微信公众平台申请的模板一致
        $data = [
            'first' => [
                'value' => '景区预警通知',
                'color' => '#FF0000'
            ],
            'keyword1' => [
                'value' => $warning['title'],
                'color' => '#000000'
            ],
            'keyword2' => [
                'value' => $this->getTypeText($warning['type']),
                'color' => '#000000'
            ],
            'keyword3' => [
                'value' => $this->getLevelText($warning['level']),
                'color' => $this->getLevelColor($warning['level'])
            ],
            'keyword4' => [
                'value' => date('Y-m-d H:i:s', $warning['createtime']),
                'color' => '#000000'
            ],
            'remark' => [
                'value' => $warning['content'],
                'color' => '#888888'
            ]
        ];

        // 构建跳转URL
        $url = Config::get('site.url') . '/addons/dsassistant/index/warning';

        // 发送模板消息
        return $this->sendTemplateMessage($openid, $data, $url);
    }

    /**
     * 获取类型文本
     */
    protected function getTypeText($type)
    {
        $types = [
            'crowd' => '人流预警',
            'weather' => '天气预警',
            'traffic' => '交通预警',
            'other' => '其他预警'
        ];

        return isset($types[$type]) ? $types[$type] : '未知类型';
    }

    /**
     * 获取级别文本
     */
    protected function getLevelText($level)
    {
        $levels = [
            'info' => '提示',
            'warning' => '警告',
            'danger' => '危险'
        ];

        return isset($levels[$level]) ? $levels[$level] : '未知级别';
    }

    /**
     * 获取级别颜色
     */
    protected function getLevelColor($level)
    {
        $colors = [
            'info' => '#2F9688',
            'warning' => '#FF9800',
            'danger' => '#FF0000'
        ];

        return isset($colors[$level]) ? $colors[$level] : '#000000';
    }


    /**
     * 发送HTTP GET请求
     */
    protected function httpGet($url)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        $result = curl_exec($curl);
        curl_close($curl);

        return $result;
    }

    /**
     * 发送HTTP POST请求
     */
    protected function httpPost($url, $data)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        $result = curl_exec($curl);
        curl_close($curl);

        return $result;
    }

    /**
     * 获取微信JS SDK配置
     *
     * @param string $url 当前页面URL
     * @return array JS SDK配置
     */
    public function getJsConfig($url)
    {
        $accessToken = $this->getAccessToken();
        if (!$accessToken) {
            throw new \Exception('Failed to get access token');
        }

        // 获取jsapi_ticket
        $jsapiTicket = $this->getJsapiTicket($accessToken);

        // 生成签名
        $nonceStr = $this->createNonceStr();
        $timestamp = time();
        $string = "jsapi_ticket={$jsapiTicket}&noncestr={$nonceStr}&timestamp={$timestamp}&url={$url}";
        $signature = sha1($string);

        // 返回配置
        return [
            'appId' => $this->appid,
            'timestamp' => $timestamp,
            'nonceStr' => $nonceStr,
            'signature' => $signature
        ];
    }

    /**
     * 获取jsapi_ticket
     *
     * @param string $accessToken 访问令牌
     * @return string jsapi_ticket
     */
    protected function getJsapiTicket($accessToken)
    {
        // 先从缓存获取
        $jsapiTicket = Cache::get('wechat_jsapi_ticket');
        if ($jsapiTicket) {
            return $jsapiTicket;
        }

        $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token={$accessToken}&type=jsapi";
        $result = $this->httpGet($url);
        $json = json_decode($result, true);

        if (isset($json['ticket'])) {
            // 将ticket存入缓存，有效期比微信返回的少300秒，避免临界点问题
            $expiresIn = isset($json['expires_in']) ? $json['expires_in'] - 300 : 7200 - 300;
            Cache::set('wechat_jsapi_ticket', $json['ticket'], $expiresIn);
            return $json['ticket'];
        }

        throw new \Exception('Failed to get jsapi_ticket: ' . $result);
    }

    /**
     * 生成随机字符串
     *
     * @param int $length 长度
     * @return string 随机字符串
     */
    protected function createNonceStr($length = 16)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }
}
