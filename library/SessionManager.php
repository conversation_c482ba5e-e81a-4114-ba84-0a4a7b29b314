<?php

namespace addons\dsassistant\library;

use think\Db;
use think\Log;
use think\Cache;

/**
 * 会话管理类
 * 用于管理聊天会话状态
 */
class SessionManager
{
    /**
     * 会话缓存前缀
     */
    const SESSION_CACHE_PREFIX = 'ds_session_';

    /**
     * 会话过期时间（秒）
     */
    const SESSION_EXPIRE_TIME = 1800; // 30分钟

    /**
     * 最大对话历史记录数
     */
    const MAX_HISTORY_SIZE = 10;

    /**
     * 获取会话状态
     *
     * @param string $sessionId 会话ID
     * @return array|null 会话状态
     */
    public static function getSessionState($sessionId)
    {
        // 先从缓存获取
        $cacheKey = self::SESSION_CACHE_PREFIX . $sessionId;
        $sessionState = Cache::get($cacheKey);

        if ($sessionState) {
            return $sessionState;
        }

        // 从数据库获取
        $sessionState = Db::name('ds_session_state')
            ->where('session_id', $sessionId)
            ->where('status', 'active')
            ->where('expire_time', '>', time())
            ->find();

        if ($sessionState) {
            // 解码上下文
            if (!empty($sessionState['context'])) {
                $sessionState['context'] = json_decode($sessionState['context'], true);
            } else {
                $sessionState['context'] = [];
            }

            // 缓存会话状态
            Cache::set($cacheKey, $sessionState, self::SESSION_EXPIRE_TIME);

            return $sessionState;
        }

        return null;
    }

    /**
     * 创建或更新会话状态
     *
     * @param string $sessionId 会话ID
     * @param string $userId 用户ID
     * @param string $question 问题
     * @param string $answer 回答
     * @param string $platform 平台
     * @return bool 是否成功
     */
    public static function updateSessionState($sessionId, $userId, $question, $answer, $platform = 'web')
    {
        try {
            // 获取当前会话状态
            $sessionState = self::getSessionState($sessionId);

            // 准备上下文数据
            $context = [];
            if ($sessionState && !empty($sessionState['context'])) {
                $context = $sessionState['context'];
            }

            // 定义"无法回答"的模式
            $cannotAnswerPatterns = [
                '抱歉，我无法回答这个问题',
                '对不起，我不知道',
                '很遗憾，我没有相关信息',
                '我无法提供这方面的信息',
                '我不清楚'
            ];

            // 检查当前回答是否包含"无法回答"模式
            $isCannotAnswer = false;
            foreach ($cannotAnswerPatterns as $pattern) {
                if (mb_stripos($answer, $pattern) !== false) {
                    $isCannotAnswer = true;
                    break;
                }
            }

            // 只有当回答不是"无法回答"时，才将对话添加到上下文
            if (!$isCannotAnswer) {
                // 添加新的对话到上下文
                $context[] = [
                    'role' => 'user',
                    'content' => $question
                ];

                $context[] = [
                    'role' => 'assistant',
                    'content' => $answer
                ];

                Log::record("将对话添加到上下文", 'info');
            } else {
                Log::record("检测到'无法回答'模式，不将此对话添加到上下文", 'info');
            }

            // 限制上下文大小
            if (count($context) > self::MAX_HISTORY_SIZE * 2) {
                // 保留最近的对话，移除最早的对话（成对移除用户和助手的消息）
                $context = array_slice($context, -self::MAX_HISTORY_SIZE * 2);
            }

            $now = time();
            $expireTime = $now + self::SESSION_EXPIRE_TIME;

            // 准备数据
            $data = [
                'user_id' => $userId,
                'context' => json_encode($context, JSON_UNESCAPED_UNICODE),
                'last_question' => $question,
                'last_answer' => $answer,
                'platform' => $platform,
                'status' => 'active',
                'expire_time' => $expireTime,
                'updatetime' => $now
            ];

            if ($sessionState) {
                // 更新现有会话
                Db::name('ds_session_state')
                    ->where('session_id', $sessionId)
                    ->update($data);
            } else {
                // 创建新会话
                $data['session_id'] = $sessionId;
                $data['createtime'] = $now;

                Db::name('ds_session_state')->insert($data);
            }

            // 更新缓存
            $data['context'] = $context;
            Cache::set(self::SESSION_CACHE_PREFIX . $sessionId, $data, self::SESSION_EXPIRE_TIME);

            return true;
        } catch (\Exception $e) {
            Log::error('更新会话状态失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取会话历史记录
     *
     * @param string $sessionId 会话ID
     * @param int $limit 限制数量
     * @return array 历史记录
     */
    public static function getSessionHistory($sessionId, $limit = 5)
    {
        return Db::name('ds_chat_log')
            ->where('session_id', $sessionId)
            ->order('id DESC')
            ->limit($limit)
            ->select();
    }

    /**
     * 关闭会话
     *
     * @param string $sessionId 会话ID
     * @return bool 是否成功
     */
    public static function closeSession($sessionId)
    {
        try {
            Db::name('ds_session_state')
                ->where('session_id', $sessionId)
                ->update([
                    'status' => 'closed',
                    'updatetime' => time()
                ]);

            // 删除缓存
            Cache::rm(self::SESSION_CACHE_PREFIX . $sessionId);

            return true;
        } catch (\Exception $e) {
            Log::error('关闭会话失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 清理过期会话
     *
     * @return int 清理的会话数量
     */
    public static function cleanExpiredSessions()
    {
        try {
            $count = Db::name('ds_session_state')
                ->where('status', 'active')
                ->where('expire_time', '<', time())
                ->update([
                    'status' => 'closed',
                    'updatetime' => time()
                ]);

            return $count;
        } catch (\Exception $e) {
            Log::error('清理过期会话失败: ' . $e->getMessage());
            return 0;
        }
    }
}
