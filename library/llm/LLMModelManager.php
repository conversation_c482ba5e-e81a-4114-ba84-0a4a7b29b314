<?php

namespace addons\dsassistant\library\llm;

use think\Log;
use think\Db;
use think\Cache;

/**
 * LLM模型管理类
 *
 * 管理LLM模型配置，使用独立的数据库表存储
 */
class LLMModelManager
{
    /**
     * 缓存前缀
     */
    const CACHE_PREFIX = 'llm_models_';

    /**
     * 表名
     */
    const TABLE_NAME = 'ds_llm_models';




    /**
     * 获取默认模型
     * @param boolean $forcedb 强制db查询
     * @return array|null 默认模型信息，如果不存在则返回null
     */
    public static function getDefaultModel($forcedb=false)
    {
        // 尝试从缓存获取
        $cacheKey = self::CACHE_PREFIX . 'default';
        $model = Cache::get($cacheKey);
        if (!$forcedb && $model !== false) {
            return $model;
        }

        // 从数据库获取
        $model = Db::name(self::TABLE_NAME)
            ->where('is_default', 1)
            ->where('enabled', 1)
            ->find();

        // 如果没有设置默认模型，则返回第一个启用的模型
        if (!$model) {
            $model = Db::name(self::TABLE_NAME)
                ->where('enabled', 1)
                ->order('weight DESC, id ASC')
                ->find();
        }

        if (!$model) {
            return null;
        }

        // 写入缓存
        Cache::set($cacheKey, $model, 3600);

        return $model;
    }

    /**
     * 获取指定模型
     * @param string $modelId 模型ID
     * @param boolean $forcedb 强制db查询
     * @return array|null 模型信息，如果不存在则返回null
     */
    public static function getModel($modelId, $forcedb = false)
    {
        // 尝试从缓存获取
        $cacheKey = self::CACHE_PREFIX . 'model_' . $modelId;
        $model = Cache::get($cacheKey);
        if (!$forcedb && $model !== false) {
            return $model;
        }

        // 从数据库获取
        $model = Db::name(self::TABLE_NAME)
            ->where('model_id', $modelId)
            ->where('enabled', 1)
            ->find();

        if (!$model) {
            return null;
        }

        // 写入缓存
        Cache::set($cacheKey, $model, 3600);

        return $model;
    }

    /**
     * 按服务商获取模型
     *
     * @param string $provider 服务商标识
     * @param bool $onlyEnabled 是否只返回已启用的模型
     * @return array 模型列表
     */
    public static function getModelsByProvider($provider, $onlyEnabled = false)
    {
        // 尝试从缓存获取
        $cacheKey = self::CACHE_PREFIX . 'provider_' . $provider . ($onlyEnabled ? '_enabled' : '');
        $models = Cache::get($cacheKey);
        if ($models !== false) {
            return $models;
        }

        // 从数据库获取
        $query = Db::name(self::TABLE_NAME)->where('provider', $provider);
        if ($onlyEnabled) {
            $query->where('enabled', 1);
        }
        $models = $query->order('weight DESC, id ASC')->select();

        // 写入缓存
        Cache::set($cacheKey, $models, 3600);

        return $models;
    }



    /**
     * 清除缓存
     *
     * @return void
     */
    public static function clearCache()
    {
        // 清除所有相关缓存
        Cache::rm(self::CACHE_PREFIX . 'default');

        // 清除模型缓存
        $models = Db::name(self::TABLE_NAME)->column('model_id');
        foreach ($models as $modelId) {
            Cache::rm(self::CACHE_PREFIX . 'model_' . $modelId);
        }

        // 清除服务商缓存
        $providers = Db::name(self::TABLE_NAME)->group('provider')->column('provider');
        foreach ($providers as $provider) {
            Cache::rm(self::CACHE_PREFIX . 'provider_' . $provider);
            Cache::rm(self::CACHE_PREFIX . 'provider_' . $provider . '_enabled');
        }
    }

}
