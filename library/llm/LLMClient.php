<?php

namespace addons\dsassistant\library\llm;

use think\Log;

/**
 * LLM客户端类
 *
 * 统一处理各种LLM服务商的API调用
 */
class LLMClient
{
    /**
     * 当前模型信息
     * @var array
     */
    protected $model;

    /**
     * 构造函数
     * @throws \Exception 如果模型不存在
     */
    public function __construct()
    {
        $this->model = LLMModelManager::getDefaultModel();
        if (!$this->model) {
            throw new \Exception("没有可用的默认模型");
        }

    }

    /**
     * 聊天补全
     *
     * @param array $messages 消息数组
     * @param array $options 选项
     * @return string|array 生成的回答
     * @throws \Exception 如果API调用失败
     */
    public function chatCompletion($messages, $options = [])
    {
        // 优先尝试OpenAI兼容的通用方法
        if ($this->isOpenAICompatible()) {
            return $this->callOpenAICompatibleAPI($messages, $options);
        }

        // 回退到特定厂商的实现
        $provider = $this->model['provider'];
        $method = 'call' . ucfirst($provider) . 'API';
        if (method_exists($this, $method)) {
            return $this->$method($messages, $options);
        }

        throw new \Exception("不支持的服务商: {$provider}");
    }

    /**
     * 检查是否支持OpenAI兼容API
     *
     * @return bool
     */
    protected function isOpenAICompatible()
    {
        // 检查模型配置中是否标记为OpenAI兼容
        return isset($this->model['openai_compatible']) && $this->model['openai_compatible'];
    }

    /**
     * 调用OpenAI兼容的API
     *
     * @param array $messages 消息数组
     * @param array $options 选项
     * @return string|array 生成的回答
     * @throws \Exception 如果API调用失败
     */
    protected function callOpenAICompatibleAPI($messages, $options = [])
    {
        // 检查API密钥是否设置
        if (empty($this->model['api_key'])) {
            Log::error('API密钥未设置，请在后台配置API密钥');
            throw new \Exception('API密钥未设置，请在后台配置API密钥');
        }

        $url = $this->model['base_url'] . '/chat/completions';

        Log::info("调用OpenAI兼容API，模型: {$this->model['model_id']}, URL: {$url}");

        $data = [
            'model' => $this->model['model_id'],
            'messages' => $messages
        ];

        // 处理函数调用 - 根据厂商适配格式
        if (isset($options['functions'])) {
            $data = $this->adaptFunctionCallFormat($data, $options['functions']);
        }

        // 添加其他选项
        foreach ($options as $key => $value) {
            if ($key !== 'functions') {
                $data[$key] = $value;
            }
        }

        // 发送请求
        $response = $this->sendHTTPRequest($url, $data);

        // 处理响应
        if (isset($options['functions'])) {
            return $this->processFunctionCallResponse($response);
        }

        // 处理普通回答
        if (!isset($response['choices'][0]['message']['content'])) {
            Log::error('API返回格式错误: ' . json_encode($response));
            throw new \Exception('API返回格式错误');
        }

        return $response['choices'][0]['message']['content'];
    }

    /**
     * 适配函数调用格式
     *
     * @param array $data 请求数据
     * @param array $functions 函数定义
     * @return array 适配后的数据
     */
    protected function adaptFunctionCallFormat($data, $functions)
    {
        $provider = $this->model['provider'];

        switch ($provider) {
            case 'deepseek':
                // DeepSeek使用tools格式
                $data['tools'] = $functions;
                break;
            case 'openai':
            case 'azure':
                // OpenAI标准格式
                $data['functions'] = $functions;
                break;
            default:
                // 默认使用tools格式（大多数新API都支持）
                $data['tools'] = $functions;
                break;
        }

        return $data;
    }

    /**
     * 发送HTTP请求
     *
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @return array 响应数据
     * @throws \Exception 如果请求失败
     */
    protected function sendHTTPRequest($url, $data)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->model['api_key'],
                'Content-Type: application/json'
            ],
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);

        // 检查是否有curl错误
        if ($error) {
            Log::error('API请求失败，CURL错误: ' . $error);
            throw new \Exception('API请求失败，CURL错误: ' . $error);
        }

        if ($httpCode !== 200) {
            Log::error('API请求失败: ' . $response . ', HTTP状态码: ' . $httpCode);
            throw new \Exception('API请求失败，HTTP状态码: ' . $httpCode);
        }

        // 解析JSON响应
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('API返回的JSON解析失败: ' . json_last_error_msg());
            throw new \Exception('API返回的JSON解析失败: ' . json_last_error_msg());
        }

        return $result;
    }

    /**
     * 处理函数调用响应
     *
     * @param array $result API返回结果
     * @return array 处理后的结果
     */
    protected function processFunctionCallResponse($result)
    {
        if (!isset($result['choices'][0]['message'])) {
            Log::error('API返回格式错误: ' . json_encode($result));
            throw new \Exception('API返回格式错误');
        }

        $message = $result['choices'][0]['message'];
        $content = $message['content'] ?? '';

        // 处理函数调用
        $functionCalls = [];

        // 处理新版API的tool_calls字段（大多数厂商都支持）
        if (isset($message['tool_calls']) && is_array($message['tool_calls'])) {
            foreach ($message['tool_calls'] as $call) {
                if (isset($call['function'])) {
                    $function = $call['function'];
                    if (isset($call['id'])) {
                        $function['id'] = $call['id'];
                    }
                    $functionCalls[] = $this->processFunctionCall($function);
                }
            }
        }
        // 兼容旧版API的function_call字段
        else if (isset($message['function_call'])) {
            $functionCalls[] = $this->processFunctionCall($message['function_call']);
        }

        return [
            'content' => $content,
            'function_calls' => $functionCalls
        ];
    }

    /**
     * 带函数调用的聊天补全
     *
     * @param array $messages 消息数组
     * @param array $functions 函数定义
     * @param array $options 选项
     * @return array 包含回答和函数调用的结果
     * @throws \Exception 如果模型不支持函数调用或API调用失败
     */
    public function chatCompletionWithFunctions($messages, $functions, $options = [])
    {
        if (!$this->model['supports_functions']) {
            throw new \Exception("模型 {$this->model['model_id']} 不支持函数调用");
        }

        $options['functions'] = $functions;
        return $this->chatCompletion($messages, $options);
    }

    /**
     * 调用DeepSeek API
     *
     * @param array $messages 消息数组
     * @param array $options 选项
     * @return string|array 生成的回答
     * @throws \Exception 如果API调用失败
     */
    protected function callDeepseekAPI($messages, $options = [])
    {
        // 检查API密钥是否设置
        if (empty($this->model['api_key'])) {
            Log::error('DeepSeek API密钥未设置，请在后台配置API密钥');
            throw new \Exception('DeepSeek API密钥未设置，请在后台配置API密钥');
        }

        $url = $this->model['base_url'] . '/chat/completions';

        Log::info("调用DeepSeek API，模型: {$this->model['model_id']}, URL: {$url}");

        $data = [
            'model' => $this->model['model_id'],
            'messages' => $messages
        ];

        // 添加函数调用
        if (isset($options['functions'])) {
            // DeepSeek API需要使用tools参数，每个工具需要包含type和function字段
            // FunctionLoader已经正确格式化了函数定义，直接使用
            $data['tools'] = $options['functions'];
            Log::info("启用函数调用，函数数量: " . count($options['functions']));
        }

        // 添加其他选项
        foreach ($options as $key => $value) {
            if ($key !== 'functions') {
                $data[$key] = $value;
            }
        }

        // 记录请求数据（不包含敏感信息）
        $logData = $data;
        if (isset($logData['messages']) && is_array($logData['messages'])) {
            // 只记录消息数量，但保留原始消息结构用于API调用
            $logData['messages'] = '包含 ' . count($logData['messages']) . ' 条消息';
        }
        Log::info("DeepSeek API请求数据: " . json_encode($logData,JSON_UNESCAPED_UNICODE));

        // 记录完整的消息内容，用于调试
        if (isset($data['messages']) && is_array($data['messages'])) {
            Log::debug("DeepSeek API消息内容: " . json_encode($data['messages'], JSON_UNESCAPED_UNICODE));
        }
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->model['api_key'],
                'Content-Type: application/json'
            ],
            CURLOPT_TIMEOUT => 30, // 设置超时时间为30秒
            CURLOPT_SSL_VERIFYPEER => false, // 不验证SSL证书
            CURLOPT_SSL_VERIFYHOST => false // 不验证主机名
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);

        // 检查是否有curl错误
        if ($error) {
            Log::error('DeepSeek API请求失败，CURL错误: ' . $error);
            throw new \Exception('DeepSeek API请求失败，CURL错误: ' . $error);
        }

        if ($httpCode !== 200) {
            Log::error('DeepSeek API请求失败: ' . $response . ', HTTP状态码: ' . $httpCode);
            throw new \Exception('DeepSeek API请求失败，HTTP状态码: ' . $httpCode);
        }

        // 解析JSON响应
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('DeepSeek API返回的JSON解析失败: ' . json_last_error_msg());
            throw new \Exception('DeepSeek API返回的JSON解析失败: ' . json_last_error_msg());
        }

        // 处理函数调用
        if (isset($options['functions'])) {
            return $this->processDeepseekFunctionCalls($result);
        }

        // 处理普通回答
        if (!isset($result['choices'][0]['message']['content'])) {
            Log::error('DeepSeek API返回格式错误: ' . $response);
            throw new \Exception('DeepSeek API返回格式错误');
        }

        Log::info('DeepSeek API请求结果RAW: ' . $response);

        return $result['choices'][0]['message']['content'];
    }

    /**
     * 处理DeepSeek函数调用结果
     *
     * @param array $result API返回结果
     * @return array 处理后的结果
     */
    protected function processDeepseekFunctionCalls($result)
    {
        // 记录完整的API响应结果，用于调试函数调用问题
        Log::info('DeepSeek API函数调用响应RAW: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

        if (!isset($result['choices'][0]['message'])) {
            Log::error('DeepSeek API返回格式错误: ' . json_encode($result));
            throw new \Exception('DeepSeek API返回格式错误');
        }

        $message = $result['choices'][0]['message'];
        $content = $message['content'] ?? '';

        // 记录content字段的值，检查是否为空
        Log::info('DeepSeek API函数调用content字段: ' . ($content === '' ? '空字符串' : $content));

        // 处理函数调用
        $functionCalls = [];

        // 处理新版API的tool_calls字段
        if (isset($message['tool_calls']) && is_array($message['tool_calls'])) {
            // 处理工具调用
            foreach ($message['tool_calls'] as $call) {
                if (isset($call['function'])) {
                    // 将tool_call_id传递给函数处理
                    $function = $call['function'];
                    if (isset($call['id'])) {
                        $function['id'] = $call['id']; // 添加tool_call_id
                    }
                    $functionCalls[] = $this->processFunctionCall($function);
                }
            }
        }
        // 兼容旧版API
        else if (isset($message['function_call'])) {
            // 单个函数调用
            $functionCalls[] = $this->processFunctionCall($message['function_call']);
        } else if (isset($message['function_calls']) && is_array($message['function_calls'])) {
            // 多个函数调用
            foreach ($message['function_calls'] as $call) {
                $functionCalls[] = $this->processFunctionCall($call);
            }
        }

        // 构建返回结果
        $returnResult = [
            'content' => $content,
            'function_calls' => $functionCalls
        ];

        // 记录最终处理后的结果，用于调试函数调用后的返回内容
        Log::info('DeepSeek API函数调用处理后的结果: ' . json_encode($returnResult, JSON_UNESCAPED_UNICODE));

        return $returnResult;
    }

    /**
     * 处理函数调用数据
     *
     * @param array $functionCall 函数调用数据
     * @return array 处理后的函数调用数据
     */
    protected function processFunctionCall($functionCall)
    {
        $name = $functionCall['name'] ?? '';
        $arguments = $functionCall['arguments'] ?? '{}';
        $id = $functionCall['id'] ?? null; // 获取tool_call_id，用于后续响应

        // 尝试解析参数JSON
        $parameters = [];
        try {
            if (is_string($arguments)) {
                $parameters = json_decode($arguments, true);
            } else if (is_array($arguments)) {
                $parameters = $arguments;
            }

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('函数调用参数解析错误: ' . json_last_error_msg());
                $parameters = [];
            }
        } catch (\Exception $e) {
            Log::error('函数调用参数解析异常: ' . $e->getMessage());
            $parameters = [];
        }

        $result = [
            'name' => $name,
            'parameters' => $parameters
        ];

        // 如果有tool_call_id，添加到结果中
        if ($id) {
            $result['id'] = $id;
        }

        return $result;
    }
}
