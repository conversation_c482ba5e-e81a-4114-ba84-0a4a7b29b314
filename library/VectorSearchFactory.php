<?php

namespace addons\dsassistant\library;

use think\Log;

/**
 * 向量搜索工厂类
 * 用于创建不同版本的向量搜索实现
 */
class VectorSearchFactory
{
    /**
     * 单例实例
     * @var object
     */
    private static $instance = null;
    
    /**
     * 向量搜索实现
     * @var object
     */
    private $vectorSearch = null;
    
    /**
     * 私有构造函数，防止直接创建对象
     */
    private function __construct()
    {
        // 初始化向量搜索实现
        $this->initVectorSearch();
    }
    
    /**
     * 获取单例实例
     * 
     * @return object 向量搜索实例
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance->getVectorSearch();
    }
    
    /**
     * 初始化向量搜索实现
     */
    private function initVectorSearch()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        // 从配置获取向量搜索类型
        $type = $config['vector_search_type'] ?? 'enhanced';
        
        // 根据类型创建相应的实现
        switch ($type) {
            case 'jieba':
                // 检查Jieba服务是否可用
                $jiebaClient = new JiebaClient();
                if ($jiebaClient->isServiceAvailable()) {
                    $this->vectorSearch = new JiebaVectorSearch();
                    Log::info('使用JiebaVectorSearch实现');
                } else {
                    // 如果Jieba服务不可用，降级到增强版本
                    $this->vectorSearch = new EnhancedVectorSearch();
                    Log::warning('Jieba服务不可用，降级到EnhancedVectorSearch实现');
                }
                break;
                
            case 'enhanced':
            default:
                $this->vectorSearch = new EnhancedVectorSearch();
                Log::info('使用EnhancedVectorSearch实现');
                break;
        }
    }
    
    /**
     * 获取向量搜索实现
     * 
     * @return object 向量搜索实例
     */
    public function getVectorSearch()
    {
        return $this->vectorSearch;
    }
    
    /**
     * 创建指定类型的向量搜索实现
     * 
     * @param string $type 向量搜索类型：optimized, enhanced, jieba
     * @return object 向量搜索实例
     */
    public static function create($type = 'enhanced')
    {
        switch ($type) {
            case 'jieba':
                return new JiebaVectorSearch();
                
            case 'enhanced':
            default:
                return new EnhancedVectorSearch();
        }
    }
}
