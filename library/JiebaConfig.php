<?php

namespace addons\dsassistant\library;

/**
 * Jieba分词服务配置类
 */
class JiebaConfig
{
    /**
     * 配置数据
     * @var array
     */
    protected static $config = null;
    
    /**
     * 获取配置
     * 
     * @param string $key 配置键名，使用点号分隔多级配置，如 'service.port'
     * @param mixed $default 默认值
     * @return mixed 配置值
     */
    public static function get($key, $default = null)
    {
        // 确保配置已加载
        if (self::$config === null) {
            self::load();
        }
        
        // 解析键名
        $keys = explode('.', $key);
        $value = self::$config;
        
        // 逐级查找配置
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * 加载配置
     * 
     * @return array 配置数据
     */
    public static function load()
    {
        // 配置文件路径
        $configFile = ADDON_PATH . 'dsassistant/server/config.php';
        
        // 默认配置
        $defaultConfig = [
            'service' => [
                'host' => 'localhost',
                'port' => 8883,
            ],
            'cache' => [
                'enabled' => true,
                'ttl' => 86400,
                'prefix' => 'jieba_',
            ],
        ];
        
        // 加载配置文件
        if (file_exists($configFile)) {
            $fileConfig = include $configFile;
            self::$config = array_merge($defaultConfig, $fileConfig);
        } else {
            self::$config = $defaultConfig;
        }
        
        return self::$config;
    }
}
