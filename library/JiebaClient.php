<?php

namespace addons\dsassistant\library;

use think\Log;
use think\Cache;
use addons\dsassistant\library\JiebaConfig;

/**
 * Jieba分词客户端
 * 用于调用本地Jieba分词服务
 */
class JiebaClient
{
    /**
     * 分词服务URL
     * @var string
     */
    protected $serviceUrl;

    /**
     * 健康检查URL
     * @var string
     */
    protected $healthUrl;

    /**
     * 默认端口
     * @var int
     */
    protected $defaultPort = 8883;

    /**
     * 缓存前缀
     * @var string
     */
    protected $cachePrefix = 'jieba_segment_';

    /**
     * 缓存时间（秒）
     * @var int
     */
    protected $cacheTTL = 86400; // 缓存1天

    /**
     * 服务状态缓存键
     * @var string
     */
    protected $serviceStatusCacheKey = 'jieba_service_status';

    /**
     * 服务状态缓存时间（秒）
     * @var int
     */
    protected $serviceStatusCacheTTL = 60; // 缓存1分钟

    /**
     * 构造函数
     *
     * @param string $host 服务主机地址，默认从配置获取
     * @param int $port 服务端口，默认从配置获取
     * @param int $cacheTTL 缓存时间（秒），默认从配置获取
     */
    public function __construct($host = null, $port = null, $cacheTTL = null)
    {
        // 从配置获取主机和端口
        $host = $host ?: JiebaConfig::get('service.host', 'localhost');
        $port = $port ?: JiebaConfig::get('service.port', $this->defaultPort);

        // 设置服务URL
        $this->serviceUrl = "http://{$host}:{$port}/jieba";
        $this->healthUrl = "http://{$host}:{$port}/health";

        // 设置缓存时间
        if ($cacheTTL) {
            $this->cacheTTL = $cacheTTL;
        } else {
            $this->cacheTTL = JiebaConfig::get('cache.ttl', $this->cacheTTL);
        }

        // 设置缓存前缀
        $this->cachePrefix = JiebaConfig::get('cache.prefix', $this->cachePrefix);
    }

    /**
     * 分词处理
     *
     * @param string $text 需要分词的文本
     * @param bool $useCache 是否使用缓存
     * @return string 分词结果（空格分隔）
     */
    public function segment($text, $useCache = true)
    {
        // 空文本直接返回
        if (empty($text)) {
            return '';
        }

        // 检查缓存
        if ($useCache) {
            $cacheKey = $this->cachePrefix . md5($text);
            $cached = Cache::get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        // 检查服务是否可用
        if (!$this->isServiceAvailable()) {
            Log::warning("Jieba分词服务不可用，使用备用分词方法");
            return $this->fallbackSegment($text);
        }

        try {
            // 调用分词服务
            $ch = curl_init($this->serviceUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['text' => $text]));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5); // 5秒超时

            $response = curl_exec($ch);
            $error = curl_error($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($error || $httpCode != 200) {
                Log::error("分词服务调用失败: " . ($error ?: "HTTP状态码: {$httpCode}"));
                // 服务失败时使用备用分词方法
                return $this->fallbackSegment($text);
            }

            $result = json_decode($response, true);
            if (isset($result['code']) && $result['code'] == 200 && isset($result['data']['segmented'])) {
                $segmented = $result['data']['segmented'];

                // 缓存结果
                if ($useCache) {
                    Cache::set($this->cachePrefix . md5($text), $segmented, $this->cacheTTL);
                }

                return $segmented;
            } else {
                Log::error("分词服务返回异常: " . $response);
                return $this->fallbackSegment($text);
            }
        } catch (\Exception $e) {
            Log::error("分词服务异常: " . $e->getMessage());
            return $this->fallbackSegment($text);
        }
    }

    /**
     * 批量分词处理
     *
     * @param array $texts 需要分词的文本数组
     * @param bool $useCache 是否使用缓存
     * @return array 分词结果数组
     */
    public function batchSegment($texts, $useCache = true)
    {
        $results = [];

        foreach ($texts as $key => $text) {
            $results[$key] = $this->segment($text, $useCache);
        }

        return $results;
    }

    /**
     * 备用分词方法（当服务不可用时）
     *
     * @param string $text 需要分词的文本
     * @return string 分词结果
     */
    protected function fallbackSegment($text)
    {
        // 简单的N-gram分词作为备用
        $bigrams = [];
        $length = mb_strlen($text, 'UTF-8');

        // 生成2-gram
        for ($i = 0; $i < $length - 1; $i++) {
            $bigram = mb_substr($text, $i, 2, 'UTF-8');
            if (mb_strlen($bigram, 'UTF-8') == 2) {
                $bigrams[] = $bigram;
            }
        }

        // 生成3-gram（如果文本长度足够）
        if ($length >= 3) {
            for ($i = 0; $i < $length - 2; $i++) {
                $trigram = mb_substr($text, $i, 3, 'UTF-8');
                if (mb_strlen($trigram, 'UTF-8') == 3) {
                    $bigrams[] = $trigram;
                }
            }
        }

        // 简单的关键词提取
        $keywords = $this->extractSimpleKeywords($text);

        // 合并结果并去重
        $allSegments = array_unique(array_merge($bigrams, $keywords));

        return implode(' ', $allSegments);
    }

    /**
     * 简单的关键词提取
     *
     * @param string $text 文本
     * @return array 关键词数组
     */
    protected function extractSimpleKeywords($text)
    {
        $keywords = [];

        // 常见关键词列表
        $commonKeywords = [
            '景区', '门票', '导游', '旅游', '酒店', '住宿', '餐厅', '美食',
            '交通', '天气', '开放', '关闭', '价格', '费用', '时间', '路线',
            '攻略', '推荐', '特色', '活动', '表演', '购物', '纪念品', '厕所',
            '停车', '电话', '网络', '信号', '充电', '医疗', '安全', '紧急'
        ];

        // 检查文本中是否包含常见关键词
        foreach ($commonKeywords as $keyword) {
            if (mb_stripos($text, $keyword) !== false) {
                $keywords[] = $keyword;
            }
        }

        return $keywords;
    }

    /**
     * 检查分词服务是否可用
     *
     * @param bool $forceCheck 是否强制检查（忽略缓存）
     * @return bool 服务是否可用
     */
    public function isServiceAvailable($forceCheck = false)
    {
        // 检查缓存的服务状态
        if (!$forceCheck) {
            $status = Cache::get($this->serviceStatusCacheKey);
            if ($status !== false) {
                return (bool)$status;
            }
        }

        try {
            $ch = curl_init($this->healthUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 2); // 2秒超时

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $available = ($httpCode == 200);

            // 缓存服务状态
            Cache::set($this->serviceStatusCacheKey, $available ? 1 : 0, $this->serviceStatusCacheTTL);

            return $available;
        } catch (\Exception $e) {
            // 缓存服务不可用状态
            Cache::set($this->serviceStatusCacheKey, 0, $this->serviceStatusCacheTTL);
            return false;
        }
    }

    /**
     * 获取服务状态信息
     *
     * @return array|false 服务状态信息或失败时返回false
     */
    public function getServiceStatus()
    {
        try {
            $ch = curl_init($this->healthUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 2); // 2秒超时

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200) {
                $status = json_decode($response, true);
                return $status;
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 清除分词缓存
     *
     * @param string $text 特定文本的缓存，为空则清除所有分词缓存
     * @return bool 是否成功
     */
    public function clearCache($text = null)
    {
        if ($text) {
            $cacheKey = $this->cachePrefix . md5($text);
            return Cache::rm($cacheKey);
        } else {
            // 清除所有分词缓存需要遍历缓存键，这里简化处理
            // 实际实现可能需要根据缓存系统的特性来优化
            return Cache::clear($this->cachePrefix);
        }
    }
}
