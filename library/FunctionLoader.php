<?php

namespace addons\dsassistant\library;

use think\Log;
use app\admin\model\dsassistant\Contentconfig;

/**
 * 函数加载器
 *
 * 负责扫描目录并加载函数定义
 */
class FunctionLoader
{
    /**
     * 函数定义核心目录
     * @var string
     */
    protected $coreDir = '';

    /**
     * 函数定义自定义目录
     * @var string
     */
    protected $customDir = '';

    /**
     * 缓存的函数定义
     * @var array|null
     */
    protected $functions = null;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->coreDir = 'core';
        $this->customDir = 'custom';
    }

    /**
     * 获取所有可用的函数定义
     *
     * @param bool $forceReload 是否强制重新加载
     * @return array 函数定义数组
     */
    public function getAllFunctions($forceReload = false)
    {
        if ($this->functions === null || $forceReload) {
            $this->loadFunctions();
        }

        return $this->functions;
    }

    /**
     * 加载所有函数定义
     */
    protected function loadFunctions()
    {
        $this->functions = [];

        // 1. 加载核心函数定义
        $coreFunctions = $this->loadFromDirectory($this->coreDir);

        // 2. 加载用户自定义函数定义
        $customFunctions = $this->loadFromDirectory($this->customDir);

        // 3. 合并函数定义（用户定义优先）
        $this->functions = array_merge($coreFunctions, $customFunctions);

        // 4. 根据配置过滤和排序
        $this->filterAndSortFunctions();
    }

    /**
     * 加载所有函数定义，但不过滤禁用的函数
     */
    protected function loadAllFunctions()
    {
        $this->functions = [];

        // 1. 加载核心函数定义
        $coreFunctions = $this->loadFromDirectory($this->coreDir);

        // 2. 加载用户自定义函数定义
        $customFunctions = $this->loadFromDirectory($this->customDir);

        // 3. 合并函数定义（用户定义优先）
        $this->functions = array_merge($coreFunctions, $customFunctions);

        // 4. 只排序，不过滤
        $this->sortFunctions();
    }

    /**
     * 从目录加载函数定义
     *
     * @param string $directory 目录路径
     * @return array 函数定义数组
     */
    protected function loadFromDirectory($directory)
    {
        $functions = [];

        $functionFileDir = ADDON_PATH . 'dsassistant/functions/' . $directory . DS;

        if (!is_dir($functionFileDir)) {
            return $functions;
        }

        $files = glob($functionFileDir . '*.php');
        foreach ($files as $file) {
            try {
                $function = include $file;
                if (is_array($function) && isset($function['name'])) {
                    $function['source'] = $directory;
                    $functions[$function['name']] = $function;
                    Log::record("已加载函数定义: {$function['name']} 来自文件: " . basename($file), 'info');
                } else {
                    Log::error("函数定义格式错误: " . basename($file));
                }
            } catch (\Exception $e) {
                Log::error("加载函数定义文件失败: " . basename($file) . ", 错误: " . $e->getMessage());
            }
        }

        return $functions;
    }

    /**
     * 根据配置过滤和排序函数
     */
    protected function filterAndSortFunctions()
    {
        // 获取数据库中的函数配置
        $config = Contentconfig::getConfigAll();
        $enabledFunctions = isset($config['enabled_functions']) ? json_decode($config['enabled_functions'], true) : [];

        // 更新函数启用状态并过滤禁用的函数
        foreach ($this->functions as $name => $function) {
            // 如果配置中明确设置了启用状态，使用配置值
            if (isset($enabledFunctions[$name])) {
                $this->functions[$name]['enabled'] = (bool)$enabledFunctions[$name];
            }

            // 过滤掉禁用的函数
            if (isset($this->functions[$name]['enabled']) && $this->functions[$name]['enabled'] === false) {
                unset($this->functions[$name]);
                Log::record("函数已禁用: {$name}", 'info');
            }
        }

        // 按优先级排序
        $this->sortFunctions();

        Log::record("函数加载完成，共 " . count($this->functions) . " 个可用函数", 'info');
    }

    /**
     * 仅按优先级排序函数，不过滤，但更新enabled状态
     */
    protected function sortFunctions()
    {
        // 获取数据库中的函数配置
        $config = Contentconfig::getConfigAll(false);
        $enabledFunctions = isset($config['enabled_functions']) ? json_decode($config['enabled_functions'], true) : [];

        // 更新函数启用状态，但不过滤
        foreach ($this->functions as $name => $function) {
            // 如果配置中明确设置了启用状态，使用配置值
            if (isset($enabledFunctions[$name])) {
                $this->functions[$name]['enabled'] = (bool)$enabledFunctions[$name];
            }
        }

        // 按优先级排序
        uasort($this->functions, function($a, $b) {
            $priorityA = isset($a['priority']) ? $a['priority'] : 999;
            $priorityB = isset($b['priority']) ? $b['priority'] : 999;
            return $priorityA - $priorityB;
        });
    }

    /**
     * 获取用于DeepSeek API的函数定义
     *
     * @return array 适用于DeepSeek API的函数定义数组
     */
    public function getFunctionsForDeepSeek()
    {
        // 获取所有函数，包括禁用的
        $this->functions = null;
        $this->loadFunctions();

        $result = [];
        $config = Contentconfig::getConfigAll();
        $enabledFunctions = isset($config['enabled_functions']) ? json_decode($config['enabled_functions'], true) : [];

        if(is_array($this->functions)){
            foreach ($this->functions as $name => $function) {
                // 确定函数启用状态
                $enabled = true;
                if (isset($function['enabled'])) {
                    $enabled = (bool)$function['enabled'];
                }
                if (isset($enabledFunctions[$name])) {
                    $enabled = (bool)$enabledFunctions[$name];
                }

                // 只包含启用的函数
                if (!$enabled) {
                    continue;
                }

                // 移除非DeepSeek API需要的字段
                $apiFunction = $function;
                unset($apiFunction['enabled']);
                unset($apiFunction['priority']);
                unset($apiFunction['source']);

                $result[] = [
                    "type"=> "function",
                    "function" => $apiFunction
                ];
            }
        }

        return $result;
    }

    /**
     * 获取函数定义列表（用于管理界面）
     *
     * @return array 函数定义列表
     */
    public function getFunctionsList()
    {
        // 强制重新加载所有函数，包括禁用的
        $this->functions = null;
        // 加载所有函数，但不过滤禁用的函数
        $this->loadAllFunctions();

        $result = [];
        $config = Contentconfig::getConfigAll(false);
        $enabledFunctions = isset($config['enabled_functions']) ? json_decode($config['enabled_functions'], true) : [];

        if(is_array($this->functions)){
            foreach ($this->functions as $name => $function) {
                // 确定函数启用状态
                $enabled = true;
                if (isset($function['enabled'])) {
                    $enabled = (bool)$function['enabled'];
                }
                if (isset($enabledFunctions[$name])) {
                    $enabled = (bool)$enabledFunctions[$name];
                }

                // 管理列表页显示所有函数，不过滤禁用的函数

                $result[] = [
                    'name' => $name,
                    'description' => $function['description'] ?? '',
                    'enabled' => $enabled,
                    'priority' => $function['priority'] ?? 999,
                    'source' => $function['source'] ?? 'core'
                ];
            }
        }

        // 按优先级排序
        usort($result, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });

        return $result;
    }

    /**
     * 更新函数启用状态
     *
     * @param string $name 函数名称
     * @param bool $enabled 是否启用
     * @return bool 是否更新成功
     */
    public function updateFunctionStatus($name, $enabled)
    {
        $config = Contentconfig::getConfigAll(false);
        $enabledFunctions = isset($config['enabled_functions']) ? json_decode($config['enabled_functions'], true) : [];

        $enabledFunctions[$name] = (bool)$enabled;

        // 更新配置
        $result = Contentconfig::setConfig('enabled_functions', json_encode($enabledFunctions));

        if ($result) {
            Log::record("函数状态已更新: {$name} => " . ($enabled ? '启用' : '禁用'), 'info');
        } else {
            Log::error("函数状态更新失败: {$name}");
        }

        return $result;
    }
}
