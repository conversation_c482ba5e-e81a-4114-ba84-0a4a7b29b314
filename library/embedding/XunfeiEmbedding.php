<?php

namespace addons\dsassistant\library\embedding;

use think\Config;
use think\Log;

class XunfeiEmbedding implements EmbeddingInterface
{
    protected $appId;
    protected $apiKey;
    protected $apiSecret;
    
    public function __construct()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $this->appId = $config['xunfei_app_id'] ?? '';
        $this->apiKey = $config['xunfei_api_key'] ?? '';
        $this->apiSecret = $config['xunfei_api_secret'] ?? '';
    }
    
    public function getEmbedding($text)
    {
        $url = "https://spark-api.xf-yun.com/v2.1/embedding";
        
        // 生成RFC1123格式的时间戳
        $date = gmdate('D, d M Y H:i:s') . ' GMT';
        
        // 拼接字符串
        $signatureOrigin = "host: spark-api.xf-yun.com\n";
        $signatureOrigin .= "date: " . $date . "\n";
        $signatureOrigin .= "POST /v2.1/embedding HTTP/1.1";
        
        // 使用hmac-sha256进行加密
        $signature = base64_encode(hash_hmac('sha256', $signatureOrigin, $this->apiSecret, true));
        
        // 构造授权信息
        $authorizationOrigin = "api_key=\"{$this->apiKey}\", algorithm=\"hmac-sha256\", headers=\"host date request-line\", signature=\"{$signature}\"";
        $authorization = base64_encode($authorizationOrigin);
        
        // 请求数据
        $data = [
            'header' => [
                'app_id' => $this->appId
            ],
            'parameter' => [
                'embedding' => [
                    'text_type' => 'embedding'
                ]
            ],
            'payload' => [
                'text' => [$text]
            ]
        ];
        
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: ' . $authorization,
                'Date: ' . $date,
                'Host: spark-api.xf-yun.com'
            ]
        ];
        
        $curl = curl_init();
        curl_setopt_array($curl, $options);
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        
        if ($httpCode !== 200) {
            Log::error('讯飞API请求失败: ' . $response);
            throw new \Exception('讯飞API请求失败，HTTP状态码: ' . $httpCode);
        }
        
        $result = json_decode($response, true);
        if (!isset($result['payload']['embedding'][0]['vector'])) {
            Log::error('讯飞API返回格式错误: ' . $response);
            throw new \Exception('讯飞API返回格式错误');
        }
        
        return $result['payload']['embedding'][0]['vector'];
    }
    
    public function cosineSimilarity($a, $b)
    {
        $dotProduct = 0;
        $normA = 0;
        $normB = 0;
        
        for ($i = 0; $i < count($a); $i++) {
            $dotProduct += $a[$i] * $b[$i];
            $normA += $a[$i] * $a[$i];
            $normB += $b[$i] * $b[$i];
        }
        
        if ($normA == 0 || $normB == 0) {
            return 0;
        }
        
        return $dotProduct / (sqrt($normA) * sqrt($normB));
    }
}
