<?php

namespace addons\dsassistant\library\embedding;

use think\Config;
use think\Log;

class BaiduEmbedding implements EmbeddingInterface
{
    protected $apiKey;
    protected $secretKey;
    protected $accessToken;
    
    public function __construct()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $this->apiKey = $config['baidu_api_key'] ?? '';;
        $this->secretKey = $config['baidu_secret_key'] ?? '';;
        $this->accessToken = $this->getAccessToken();
    }
    
    protected function getAccessToken()
    {
        $cacheKey = 'baidu_access_token';
        $cachedToken = \think\Cache::get($cacheKey);
        
        if ($cachedToken) {
            return $cachedToken;
        }
        
        $url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={$this->apiKey}&client_secret={$this->secretKey}";
        
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($curl);
        curl_close($curl);
        
        $result = json_decode($response, true);
        if (isset($result['access_token'])) {
            $token = $result['access_token'];
            // 缓存29天
            \think\Cache::set($cacheKey, $token, 29 * 24 * 3600);
            return $token;
        } else {
            Log::error('获取百度访问令牌失败: ' . $response);
            throw new \Exception('获取百度访问令牌失败');
        }
    }
    
    public function getEmbedding($text)
    {
        $url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/embedding-v1?access_token={$this->accessToken}";
        
        $data = [
            'input' => $text,
        ];
        
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json'
            ]
        ];
        
        $curl = curl_init();
        curl_setopt_array($curl, $options);
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        
        if ($httpCode !== 200) {
            Log::error('百度API请求失败: ' . $response);
            throw new \Exception('百度API请求失败，HTTP状态码: ' . $httpCode);
        }
        
        $result = json_decode($response, true);
        if (!isset($result['data'][0]['embedding'])) {
            Log::error('百度API返回格式错误: ' . $response);
            throw new \Exception('百度API返回格式错误');
        }
        
        return $result['data'][0]['embedding'];
    }
    
    public function cosineSimilarity($a, $b)
    {
        $dotProduct = 0;
        $normA = 0;
        $normB = 0;
        
        for ($i = 0; $i < count($a); $i++) {
            $dotProduct += $a[$i] * $b[$i];
            $normA += $a[$i] * $a[$i];
            $normB += $b[$i] * $b[$i];
        }
        
        if ($normA == 0 || $normB == 0) {
            return 0;
        }
        
        return $dotProduct / (sqrt($normA) * sqrt($normB));
    }
}
