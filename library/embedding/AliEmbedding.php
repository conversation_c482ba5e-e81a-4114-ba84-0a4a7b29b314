<?php

namespace addons\dsassistant\library\embedding;

use think\Config;
use think\Log;

class Ali<PERSON>mbedding implements EmbeddingInterface
{
    protected $apiKey;
    
    public function __construct()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $this->apiKey = $config['ali_api_key'] ?? '';
    }
    
    public function getEmbedding($text)
    {
        $url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding";
        
        $data = [
            'input' => [
                'texts' => [$text]
            ],
            'parameters' => [
                'text_type' => 'query'
            ]
        ];
        
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ]
        ];
        
        $curl = curl_init();
        curl_setopt_array($curl, $options);
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        
        if ($httpCode !== 200) {
            Log::error('阿里API请求失败: ' . $response);
            throw new \Exception('阿里API请求失败，HTTP状态码: ' . $httpCode);
        }
        
        $result = json_decode($response, true);
        if (!isset($result['output']['embeddings'][0]['embedding'])) {
            Log::error('阿里API返回格式错误: ' . $response);
            throw new \Exception('阿里API返回格式错误');
        }
        
        return $result['output']['embeddings'][0]['embedding'];
    }
    
    public function cosineSimilarity($a, $b)
    {
        $dotProduct = 0;
        $normA = 0;
        $normB = 0;
        
        for ($i = 0; $i < count($a); $i++) {
            $dotProduct += $a[$i] * $b[$i];
            $normA += $a[$i] * $a[$i];
            $normB += $b[$i] * $b[$i];
        }
        
        if ($normA == 0 || $normB == 0) {
            return 0;
        }
        
        return $dotProduct / (sqrt($normA) * sqrt($normB));
    }
}
