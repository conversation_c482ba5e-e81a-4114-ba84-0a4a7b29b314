<?php

namespace addons\dsassistant\library\embedding;

use think\Config;
use think\Log;

/**
 * 华为云文本向量嵌入实现
 */
class HuaweiEmbedding implements EmbeddingInterface
{
    /**
     * 访问密钥ID
     * @var string
     */
    protected $ak;
    
    /**
     * 访问密钥
     * @var string
     */
    protected $sk;
    
    /**
     * 项目ID
     * @var string
     */
    protected $projectId;
    
    /**
     * 区域
     * @var string
     */
    protected $region;
    
    /**
     * 模型名称
     * @var string
     */
    protected $model;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $this->ak = $config['huawei_ak'] ?? '';
        $this->sk = $config['huawei_sk'] ?? '';
        $this->projectId = $config['huawei_project_id'] ?? '';
        $this->region = $config['huawei_region'] ?? 'cn-north-4';
        $this->model = $config['huawei_embedding_model'] ?? 'pangu-embedding';
    }
    
    /**
     * 获取文本的向量表示
     *
     * @param string $text 文本内容
     * @return array 向量数组
     */
    public function getEmbedding($text)
    {
        try {
            // 检查配置
            if (empty($this->ak) || empty($this->sk) || empty($this->projectId)) {
                throw new \Exception('华为云API配置不完整');
            }
            
            // 构建请求URL
            $url = "https://modelarts-inference.{$this->region}.myhuaweicloud.com/v1/{$this->projectId}/inferences/embedding";
            
            // 构建请求参数
            $params = [
                'text' => $text,
                'model' => $this->model
            ];
            
            // 发送请求
            $response = $this->sendRequest($url, $params);
            
            // 检查响应
            if (!isset($response['embedding']) || !is_array($response['embedding'])) {
                throw new \Exception('华为云Embedding API返回格式错误: ' . json_encode($response));
            }
            
            return $response['embedding'];
        } catch (\Exception $e) {
            Log::error('华为云Embedding API调用失败: ' . $e->getMessage());
            
            // 如果配置了备用服务，则尝试使用备用服务
            $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
            $backupProvider = $config['embedding_backup_provider'] ?? '';
            
            if (!empty($backupProvider) && $backupProvider != 'huawei') {
                Log::info('尝试使用备用Embedding服务: ' . $backupProvider);
                
                switch ($backupProvider) {
                    case 'tencent':
                        $backup = new TencentEmbedding();
                        break;
                    case 'baidu':
                        $backup = new BaiduEmbedding();
                        break;
                    case 'ali':
                        $backup = new AliEmbedding();
                        break;
                    case 'xunfei':
                        $backup = new XunfeiEmbedding();
                        break;
                    default:
                        throw new \Exception('未知的备用Embedding服务: ' . $backupProvider);
                }
                
                return $backup->getEmbedding($text);
            }
            
            throw $e;
        }
    }
    
    /**
     * 发送API请求
     *
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @return array 响应结果
     */
    protected function sendRequest($url, $params)
    {
        // 当前时间
        $timestamp = gmdate('Ymd\THis\Z');
        
        // 请求头
        $headers = [
            'Content-Type' => 'application/json',
            'X-Sdk-Date' => $timestamp,
            'Host' => parse_url($url, PHP_URL_HOST)
        ];
        
        // 请求体
        $body = json_encode($params);
        
        // 计算签名
        $signedHeaders = $this->calculateSignature('POST', parse_url($url, PHP_URL_PATH), $headers, $body);
        
        // 转换为cURL格式
        $curlHeaders = [];
        foreach ($signedHeaders as $key => $value) {
            $curlHeaders[] = "{$key}: {$value}";
        }
        
        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $curlHeaders);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($httpCode != 200) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \Exception("华为云API请求失败: HTTP {$httpCode}, {$error}, Response: {$response}");
        }
        
        curl_close($ch);
        
        return json_decode($response, true);
    }
    
    /**
     * 计算签名
     * 
     * @param string $method 请求方法
     * @param string $path 请求路径
     * @param array $headers 请求头
     * @param string $body 请求体
     * @return array 签名头
     */
    protected function calculateSignature($method, $path, $headers, $body)
    {
        // 规范请求
        $canonicalRequest = $this->buildCanonicalRequest($method, $path, $headers, $body);
        
        // 待签名字符串
        $stringToSign = "SDK-HMAC-SHA256\n{$headers['X-Sdk-Date']}\n" . hash('sha256', $canonicalRequest);
        
        // 签名
        $signature = hash_hmac('sha256', $stringToSign, $this->sk);
        
        // 授权头
        $signedHeaders = $this->getSignedHeaders($headers);
        $authHeader = "SDK-HMAC-SHA256 Access={$this->ak}, SignedHeaders={$signedHeaders}, Signature={$signature}";
        
        $headers['Authorization'] = $authHeader;
        
        return $headers;
    }
    
    /**
     * 构建规范请求
     * 
     * @param string $method 请求方法
     * @param string $path 请求路径
     * @param array $headers 请求头
     * @param string $body 请求体
     * @return string 规范请求
     */
    protected function buildCanonicalRequest($method, $path, $headers, $body)
    {
        // 规范URI
        $canonicalUri = $path;
        
        // 规范查询字符串
        $canonicalQueryString = '';
        
        // 规范请求头
        $canonicalHeaders = '';
        $signedHeaders = [];
        
        foreach ($headers as $key => $value) {
            $lowerKey = strtolower($key);
            $canonicalHeaders .= $lowerKey . ':' . trim($value) . "\n";
            $signedHeaders[] = $lowerKey;
        }
        
        sort($signedHeaders);
        $signedHeadersString = implode(';', $signedHeaders);
        
        // 请求体哈希
        $hashedPayload = hash('sha256', $body);
        
        // 构建规范请求
        return $method . "\n" . $canonicalUri . "\n" . $canonicalQueryString . "\n" . $canonicalHeaders . "\n" . $signedHeadersString . "\n" . $hashedPayload;
    }
    
    /**
     * 获取签名头
     * 
     * @param array $headers 请求头
     * @return string 签名头
     */
    protected function getSignedHeaders($headers)
    {
        $signedHeaders = [];
        
        foreach ($headers as $key => $value) {
            $signedHeaders[] = strtolower($key);
        }
        
        sort($signedHeaders);
        
        return implode(';', $signedHeaders);
    }
}
