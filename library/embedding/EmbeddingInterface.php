<?php

namespace addons\dsassistant\library\embedding;

interface EmbeddingInterface
{
    /**
     * 获取文本的向量表示
     * 
     * @param string $text 文本内容
     * @return array 向量数组
     */
    public function getEmbedding($text);
    
    /**
     * 计算两个向量的余弦相似度
     * 
     * @param array $a 向量A
     * @param array $b 向量B
     * @return float 相似度(0-1)
     */
    public function cosineSimilarity($a, $b);
}
