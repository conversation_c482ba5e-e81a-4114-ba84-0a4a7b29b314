<?php

namespace addons\dsassistant\library\embedding;

use think\Config;
use think\Log;

class EmbeddingFactory
{
    /**
     * 获取向量嵌入服务实例
     *
     * @return EmbeddingInterface 向量嵌入服务实例
     */
    public static function getInstance()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $provider = $config['embedding_provider'] ?: 'tencent';

        try {
            switch ($provider) {
                case 'baidu':
                    return new BaiduEmbedding();
                case 'xunfei':
                    return new XunfeiEmbedding();
                case 'ali':
                    return new AliEmbedding();
                case 'tencent':
                    return new TencentEmbedding();
                case 'huawei':
                    return new HuaweiEmbedding();
                default:
                    Log::error('不支持的向量嵌入服务提供商: ' . $provider . '，使用百度作为默认提供商');
                    return new BaiduEmbedding();
            }
        } catch (\Exception $e) {
            Log::error('创建向量嵌入服务实例失败: ' . $e->getMessage() . '，尝试使用备用提供商');

            // 尝试使用备用提供商
            $backupProvider = $config['embedding_backup_provider'] ?: 'ali';
            if ($backupProvider != $provider) {
                try {
                    switch ($backupProvider) {
                        case 'baidu':
                            return new BaiduEmbedding();
                        case 'xunfei':
                            return new XunfeiEmbedding();
                        case 'ali':
                            return new AliEmbedding();
                        case 'tencent':
                            return new TencentEmbedding();
                        case 'huawei':
                            return new HuaweiEmbedding();
                        default:
                            throw new \Exception('不支持的备用向量嵌入服务提供商: ' . $backupProvider);
                    }
                } catch (\Exception $e) {
                    Log::error('创建备用向量嵌入服务实例失败: ' . $e->getMessage());
                }
            }

            // 如果所有尝试都失败，抛出异常
            throw new \Exception('无法创建向量嵌入服务实例');
        }
    }
}
