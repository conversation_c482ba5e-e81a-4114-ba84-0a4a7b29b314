<?php

namespace addons\dsassistant\library\embedding;

use think\Config;
use think\Log;
use think\Exception;

class TencentEmbedding implements EmbeddingInterface
{
    /**
     * 腾讯云API密钥ID
     * @var string
     */
    protected $secretId;

    /**
     * 腾讯云API密钥
     * @var string
     */
    protected $secretKey;

    /**
     * 腾讯云区域
     * @var string
     */
    protected $region;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $config = \app\admin\model\dsassistant\Contentconfig::getConfigAll();
        $this->secretId = $config['tencent_secret_id'] ?? '';
        $this->secretKey = $config['tencent_secret_key'] ?? '';
        $this->region = $config['tencent_region'] ?: 'ap-guangzhou';
    }

    /**
     * 获取文本的向量表示
     *
     * @param string $text 文本内容
     * @return array 向量数组
     */
    public function getEmbedding($text)
    {
        try {
            // 基本参数
            $service = 'hunyuan';
            $host = $service . '.tencentcloudapi.com';
            $endpoint = 'https://' . $host;
            $version = '2023-09-01';
            $action = 'GetEmbedding';
            $timestamp = time();
            $date = gmdate('Y-m-d', $timestamp);

            // 请求参数
            $payload = json_encode(['Input' => $text]);

            // 1. 拼接规范请求串
            $httpRequestMethod = 'POST';
            $canonicalUri = '/';
            $canonicalQueryString = '';
            $canonicalHeaders = implode("\n", [
                'content-type:application/json; charset=utf-8',
                'host:' . $host,
                ''
            ]);
            $signedHeaders = 'content-type;host';
            $hashedRequestPayload = hash('SHA256', $payload);
            $canonicalRequest = $httpRequestMethod . "\n" .
                $canonicalUri . "\n" .
                $canonicalQueryString . "\n" .
                $canonicalHeaders . "\n" .
                $signedHeaders . "\n" .
                $hashedRequestPayload;

            // 2. 拼接待签名字符串
            $algorithm = 'TC3-HMAC-SHA256';
            $credentialScope = $date . '/' . $service . '/tc3_request';
            $hashedCanonicalRequest = hash('SHA256', $canonicalRequest);
            $stringToSign = $algorithm . "\n" .
                $timestamp . "\n" .
                $credentialScope . "\n" .
                $hashedCanonicalRequest;

            // 3. 计算签名
            $secretDate = hash_hmac('SHA256', $date, 'TC3' . $this->secretKey, true);
            $secretService = hash_hmac('SHA256', $service, $secretDate, true);
            $secretSigning = hash_hmac('SHA256', 'tc3_request', $secretService, true);
            $signature = hash_hmac('SHA256', $stringToSign, $secretSigning);

            // 4. 拼接 Authorization
            $authorization = $algorithm .
                ' Credential=' . $this->secretId . '/' . $credentialScope .
                ', SignedHeaders=' . $signedHeaders .
                ', Signature=' . $signature;

            // 设置请求头
            $headers = [
                'Authorization: ' . $authorization,
                'Content-Type: application/json; charset=utf-8',
                'Host: ' . $host,
                'X-TC-Action: ' . $action,
                'X-TC-Timestamp: ' . $timestamp,
                'X-TC-Version: ' . $version,
                'X-TC-Region: ' . $this->region
            ];

            // 记录请求信息，便于调试
            Log::info('腾讯云API请求参数: ' . json_encode([
                'url' => $endpoint,
                'action' => $action,
                'payload' => $payload,
                'canonicalRequest' => $canonicalRequest,
                'stringToSign' => $stringToSign
            ], JSON_UNESCAPED_UNICODE));

            // 发送请求
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $endpoint,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HEADER => false,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $payload,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            // 处理响应
            if ($curlError) {
                Log::error('腾讯云API请求CURL错误: ' . $curlError);
                throw new \Exception('请求腾讯云API时发生错误: ' . $curlError);
            }

            if ($httpCode !== 200) {
                Log::error('腾讯云API请求失败: ' . $response . ', HTTP状态码: ' . $httpCode);
                throw new \Exception('腾讯云API请求失败，HTTP状态码: ' . $httpCode);
            }

            $result = json_decode($response, true);

            // 检查API返回的错误信息
            if (isset($result['Response']['Error'])) {
                $errorCode = $result['Response']['Error']['Code'];
                $errorMessage = $result['Response']['Error']['Message'];
                Log::error("腾讯云API返回错误: [{$errorCode}] {$errorMessage}");
                throw new \Exception("腾讯云API返回错误: [{$errorCode}] {$errorMessage}");
            }

            // 检查返回的向量数据
            if (!isset($result['Response']['Data']) || !isset($result['Response']['Data'][0]['Embedding'])) {
                Log::error('腾讯云API返回格式错误: ' . $response);
                throw new \Exception('腾讯云API返回格式错误，未找到向量数据');
            }

            return $result['Response']['Data'][0]['Embedding'];
        } catch (\Exception $e) {
            Log::error('获取向量嵌入失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 计算两个向量的余弦相似度
     *
     * @param array $a 向量A
     * @param array $b 向量B
     * @return float 相似度(0-1)
     */
    public function cosineSimilarity($a, $b)
    {
        $dotProduct = 0;
        $normA = 0;
        $normB = 0;

        for ($i = 0; $i < count($a); $i++) {
            $dotProduct += $a[$i] * $b[$i];
            $normA += $a[$i] * $a[$i];
            $normB += $b[$i] * $b[$i];
        }

        if ($normA == 0 || $normB == 0) {
            return 0;
        }

        return $dotProduct / (sqrt($normA) * sqrt($normB));
    }
}
