<?php

namespace addons\dsassistant\library;

/**
 * 函数定义管理类
 * 
 * 用于管理DeepSeek Function Calling的函数定义
 */
class FunctionDefinitions
{
    /**
     * 获取所有函数定义
     * 
     * @return array 函数定义数组
     */
    public static function getAllFunctions()
    {
        return [
            self::getShowOnMapFunction(),
            self::getShowScenicSpotDetailsFunction()
        ];
    }
    
    /**
     * 获取显示地图位置的函数定义
     * 
     * @return array 函数定义
     */
    public static function getShowOnMapFunction()
    {
        return [
            "name" => "showOnMap",
            "description" => "显示景点在地图上的位置",
            "parameters" => [
                "type" => "object",
                "properties" => [
                    "spotId" => [
                        "type" => "string",
                        "description" => "景点ID"
                    ],
                    "spotName" => [
                        "type" => "string",
                        "description" => "景点名称"
                    ],
                    "latitude" => [
                        "type" => "number",
                        "description" => "纬度"
                    ],
                    "longitude" => [
                        "type" => "number",
                        "description" => "经度"
                    ]
                ],
                "required" => ["spotName"]
            ]
        ];
    }
    
    /**
     * 获取显示景点详情的函数定义
     * 
     * @return array 函数定义
     */
    public static function getShowScenicSpotDetailsFunction()
    {
        return [
            "name" => "showScenicSpotDetails",
            "description" => "显示景点详细信息",
            "parameters" => [
                "type" => "object",
                "properties" => [
                    "spotId" => [
                        "type" => "string",
                        "description" => "景点ID"
                    ],
                    "spotName" => [
                        "type" => "string",
                        "description" => "景点名称"
                    ]
                ],
                "required" => ["spotId"]
            ]
        ];
    }
    
    /**
     * 根据函数名获取函数定义
     * 
     * @param string $functionName 函数名
     * @return array|null 函数定义，如果不存在则返回null
     */
    public static function getFunctionByName($functionName)
    {
        $allFunctions = self::getAllFunctions();
        
        foreach ($allFunctions as $function) {
            if ($function['name'] === $functionName) {
                return $function;
            }
        }
        
        return null;
    }
}
