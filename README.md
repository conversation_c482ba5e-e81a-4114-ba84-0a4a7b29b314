# 景区智能助理插件

基于国内大模型技术的景区智能助理插件，为游客提供智能问答、实时预警、电子导览和失物招领等服务。

## 功能特点

- **智能问答**：基于国内大模型技术，解答游客各类问题，提供精准信息服务
- **实时预警**：及时推送人流高峰、极端天气等预警信息，保障游客安全
- **电子导览**：提供景区电子地图导航，让游客轻松找到目的地
- **失物招领**：提供失物登记与查询入口，帮助游客找回丢失物品
- **向量搜索**：支持多种向量数据库，提供高效的语义搜索能力
- **多模型支持**：支持百度、讯飞、阿里云、腾讯云、华为云等国内主流大模型

## 使用方式

### 微信公众号

1. 在FastAdmin后台配置微信公众号相关参数
2. 关注公众号，即可使用智能问答功能
3. 可通过公众号菜单访问其他功能

### 小程序

1. 将uniapp目录下的代码导入HBuilderX
2. 修改API地址配置
3. 编译发布小程序

### 官网集成

1. 在网站中嵌入智能助理聊天窗口
2. 访问相应页面使用其他功能

## 安装说明

1. 下载插件压缩包
2. 在FastAdmin后台插件管理中上传并安装
3. 安装完成后，进入插件配置页面，配置向量嵌入服务API密钥等参数
4. 根据需要配置微信公众号和腾讯地图等第三方服务
5. 配置向量数据库（可选），提高大规模知识库的搜索效率

详细安装步骤请参考 [INSTALL.md](INSTALL.md)

## 配置说明

### 向量嵌入服务配置

支持以下服务商，请选择一个主要提供商和一个备用提供商：

- **百度文心大模型**
  - 开通地址：https://cloud.baidu.com/product/wenxinworkshop
  - 所需密钥：API Key、Secret Key

- **讯飞星火大模型**
  - 开通地址：https://xinghuo.xfyun.cn/
  - 所需密钥：AppID、API Key、API Secret

- **阿里云通义千问**
  - 开通地址：https://help.aliyun.com/document_detail/2399480.html
  - 所需密钥：API Key

- **腾讯云混元大模型**
  - 开通地址：https://cloud.tencent.com/product/hunyuan
  - 所需密钥：SecretId、SecretKey

- **华为云盘古大模型**
  - 开通地址：https://www.huaweicloud.com/product/pangu.html
  - 所需密钥：AK、SK、Project ID

### 向量数据库配置

支持以下向量数据库，提高大规模知识库的搜索效率：

- **MySQL向量搜索**（默认）
  - 无需额外配置，使用现有MySQL数据库

- **腾讯云VectorDB**
  - 开通地址：https://cloud.tencent.com/product/vectordb
  - 所需密钥：SecretId、SecretKey

- **阿里云VectorSearch**
  - 开通地址：https://help.aliyun.com/document_detail/2510230.html
  - 所需密钥：AccessKey ID、AccessKey Secret

- **百度智能云VectorDB**
  - 开通地址：https://cloud.baidu.com/product/vectordb.html
  - 所需密钥：API Key、Secret Key

- **华为云VSS**
  - 开通地址：https://www.huaweicloud.com/product/vss.html
  - 所需密钥：AK、SK、Project ID

### 微信公众号配置

- **AppID**：微信公众号的AppID
- **AppSecret**：微信公众号的AppSecret
- **Token**：微信公众号的Token

### 地图配置

- **腾讯地图Key**：腾讯地图开发者Key（用于网页和小程序地图显示）

## 数据库表说明

- **ds_knowledge**：知识库表
- **ds_chat_log**：聊天记录表
- **ds_warning**：预警信息表
- **ds_lost_found**：失物招领表
- **ds_scenic_spot**：景点信息表
- **ds_feedback**：用户反馈表
- **ds_vector_data**：向量数据表（用于MySQL向量搜索）

## 开发说明

### 后端开发

- 基于FastAdmin框架
- 使用ThinkPHP 5.0
- 集成多种国内大模型API
- 支持多种向量数据库
- 使用向量搜索技术提高问答准确性

### 前端开发

- 网页端：基于Bootstrap 4
- 小程序：基于uni-app框架
- 公众号：基于微信公众号API
- 支持Markdown渲染
- 支持"思考中"状态指示

## 版本历史

- **v1.2.0**：添加华为云支持，优化用户标识策略，修复多个问题
- **v1.1.0**：添加向量数据库支持，提高大规模知识库搜索效率
- **v1.0.0**：初始版本，实现基础功能

## 联系方式

- 邮箱：<EMAIL>
- 官网：https://www.example.com

## 许可证

MIT
