{"gen": [{"name": "知识库", "addon": "dsassistant", "mtable": "ds_knowledge", "controller": "Knowledge", "model": "Knowledge", "menu_switch": 1, "delete_switch": 0, "import_switch": 1, "local_switch": 1, "tree_switch": 0, "relation": "ds_category_rule", "relationmodel": "<PERSON><PERSON><PERSON>", "relationforeignkey": "category_id", "relationcontroller": "dsassistant/categoryrule", "relationfields": "category", "selectpagefield": "category", "intdatesuffix": "_from,_until,time"}, {"name": "聊天记录表", "addon": "dsassistant", "mtable": "ds_chat_log", "controller": "<PERSON><PERSON><PERSON>", "model": "<PERSON><PERSON><PERSON>", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0}, {"name": "预警信息表", "addon": "dsassistant", "mtable": "ds_warning", "controller": "Warning", "model": "Warning", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0}, {"name": "预警推送日志", "addon": "dsassistant", "mtable": "ds_warning_log", "controller": "Warninglog", "model": "Warninglog", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0, "relation": "ds_warning", "relationmodel": "Warning", "relationforeignkey": "warning_id", "relationcontroller": "dsassistant/warning", "relationfields": "title", "selectpagefield": "title"}, {"name": "失物招领", "addon": "dsassistant", "mtable": "ds_lost_found", "controller": "Lostfound", "model": "Lostfound", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0, "relation": "ds_lost_found_category", "relationmodel": "Lostfoundcategory", "relationforeignkey": "category_id", "relationcontroller": "dsassistant/lostfoundcategory", "relationfields": "category", "selectpagefield": "category"}, {"name": "景点信息", "addon": "dsassistant", "mtable": "ds_scenic_spot", "controller": "Scenicspot", "model": "Scenicspot", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0, "relation": "ds_scenic", "relationmodel": "Scenic", "relationforeignkey": "scenic_id", "relationcontroller": "dsassistant/scenic", "relationfields": "name", "selectpagefield": "name", "sortfield": "weight"}, {"name": "向量管理", "addon": "dsassistant", "mtable": "ds_vector_embeddings", "controller": "Vectormanage", "model": "Vectoryembedings", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0, "relation": "ds_knowledge", "relationmodel": "Knowledge", "relationforeignkey": "knowledge_id", "relationcontroller": "dsassistant/knowledge", "relationfields": "question", "selectpagefield": "question"}, {"name": "分类规则", "addon": "dsassistant", "mtable": "ds_category_rule", "controller": "<PERSON><PERSON><PERSON>", "model": "<PERSON><PERSON><PERSON>", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0}, {"name": "关键词映射", "addon": "dsassistant", "mtable": "ds_keyword_map", "controller": "Keywordmap", "model": "Keywordmap", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0, "relation": "ds_category_rule", "relationmodel": "<PERSON><PERSON><PERSON>", "relationforeignkey": "category_id", "relationcontroller": "dsassistant/categoryrule", "relationfields": "category", "selectpagefield": "category"}, {"name": "全局配置", "addon": "dsassistant", "mtable": "ds_content_config", "controller": "Contentconfig", "model": "Contentconfig", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0}, {"name": "页面配置", "addon": "dsassistant", "mtable": "ds_page_content", "controller": "Pagecontent", "model": "Pagecontent", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0}, {"name": "公众号用户管理", "addon": "dsassistant", "mtable": "ds_user_wechat", "controller": "Userwechat", "model": "Userwechat", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0, "relation": "user", "relationcontroller": "user/user", "relationfields": "name"}, {"name": "景区信息", "addon": "dsassistant", "mtable": "ds_scenic", "controller": "Scenic", "model": "Scenic", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0}, {"name": "失物招领分类", "addon": "dsassistant", "mtable": "ds_lost_found_category", "controller": "Lostfoundcategory", "model": "Lostfoundcategory", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0}, {"name": "字典管理", "addon": "dsassistant", "mtable": "ds_dict", "controller": "Dict", "model": "Dict", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0, "relation": "ds_dict", "relationmodel": "Dict", "relationforeignkey": "parent", "relationprimarykey": "name", "relationcontroller": "dsassistant/dict", "relationfields": "title", "selectpagefield": "title"}, {"name": "数据源模板", "addon": "dsassistant", "mtable": "ds_converter_template", "controller": "ConverterTemplate", "model": "ConverterTemplate", "menu_switch": 1, "delete_switch": 0, "import_switch": 0, "local_switch": 1, "tree_switch": 0, "relation": "ds_category_rule", "relationmodel": "<PERSON><PERSON><PERSON>", "relationforeignkey": "category_id", "relationcontroller": "dsassistant/categoryrule", "relationfields": "category", "selectpagefield": "category"}], "__remark__": "[FastAdmin插件开发辅助增强插件]使用的代码生成模板备份文件"}