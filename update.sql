-- ----------------------------
-- 知识库表升级
-- ----------------------------

-- 添加向量状态字段
ALTER TABLE `fa_ds_knowledge`
  ADD COLUMN IF NOT EXISTS `has_vector` TINYINT(1) DEFAULT 0 COMMENT '是否已生成向量数据：0=未生成，1=已生成',
  ADD COLUMN IF NOT EXISTS `vector_time` INT(10) UNSIGNED DEFAULT NULL COMMENT '向量生成时间';

-- 添加索引
ALTER TABLE `fa_ds_knowledge` ADD INDEX IF NOT EXISTS `has_vector` (`has_vector`);

-- ----------------------------
-- 向量数据库表升级
-- ----------------------------

-- 检查并创建向量表（如果不存在）
CREATE TABLE IF NOT EXISTS `fa_ds_knowledge_vectors` (
  `id` VARCHAR(64) PRIMARY KEY COMMENT '向量ID，通常是知识库ID',
  `vector` LONGTEXT NOT NULL COMMENT '向量数据，JSON格式存储的浮点数数组',
  `metadata` TEXT COMMENT '元数据，JSON格式存储的附加信息',
  `category_id` INT COMMENT '分类ID，冗余字段用于快速过滤',
  `category` VARCHAR(50) COMMENT '分类名称，冗余字段用于显示',
  `tags` TEXT COMMENT '标签/关键词，逗号分隔的字符串',
  `weight` INT DEFAULT 0 COMMENT '权重值，用于调整搜索结果排序',
  `status` VARCHAR(20) DEFAULT 'normal' COMMENT '状态：normal=正常，hidden=隐藏',
  `valid_from` INT COMMENT '有效期开始时间戳',
  `valid_until` INT COMMENT '有效期结束时间戳',
  `createtime` INT(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `updatetime` INT(10) UNSIGNED NOT NULL COMMENT '更新时间',
  INDEX idx_category_id (category_id) COMMENT '分类ID索引',
  INDEX idx_status (status) COMMENT '状态索引',
  INDEX idx_weight (weight) COMMENT '权重索引',
  INDEX idx_valid (valid_from, valid_until) COMMENT '有效期索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库向量表 - 存储向量嵌入和元数据，用于语义搜索';

-- 添加缺失的字段（如果表已存在但缺少字段）
ALTER TABLE `fa_ds_knowledge_vectors`
  ADD COLUMN IF NOT EXISTS `category_id` INT COMMENT '分类ID，冗余字段用于快速过滤' AFTER `metadata`,
  ADD COLUMN IF NOT EXISTS `category` VARCHAR(50) COMMENT '分类名称，冗余字段用于显示' AFTER `category_id`,
  ADD COLUMN IF NOT EXISTS `tags` TEXT COMMENT '标签/关键词，逗号分隔的字符串' AFTER `category`,
  ADD COLUMN IF NOT EXISTS `weight` INT DEFAULT 0 COMMENT '权重值，用于调整搜索结果排序' AFTER `tags`,
  ADD COLUMN IF NOT EXISTS `status` VARCHAR(20) DEFAULT 'normal' COMMENT '状态：normal=正常，hidden=隐藏' AFTER `weight`,
  ADD COLUMN IF NOT EXISTS `valid_from` INT COMMENT '有效期开始时间戳' AFTER `status`,
  ADD COLUMN IF NOT EXISTS `valid_until` INT COMMENT '有效期结束时间戳' AFTER `valid_from`;

-- 添加缺失的索引（如果表已存在但缺少索引）
-- 注意：MySQL不支持"ADD INDEX IF NOT EXISTS"语法，需要先检查索引是否存在
-- 这里使用了一种变通方法，尝试添加索引，如果失败则忽略错误
ALTER TABLE `fa_ds_knowledge_vectors` ADD INDEX idx_category_id (category_id) COMMENT '分类ID索引';
ALTER TABLE `fa_ds_knowledge_vectors` ADD INDEX idx_status (status) COMMENT '状态索引';
ALTER TABLE `fa_ds_knowledge_vectors` ADD INDEX idx_weight (weight) COMMENT '权重索引';
ALTER TABLE `fa_ds_knowledge_vectors` ADD INDEX idx_valid (valid_from, valid_until) COMMENT '有效期索引';
