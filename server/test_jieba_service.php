<?php
/**
 * Jieba分词服务测试脚本
 *
 * 使用方法：php test_jieba_service.php [文本] [--port=8883] [--host=localhost]
 */

// 加载配置
require_once __DIR__ . '/vendor/autoload.php';
use JiebaService\Config;

// 加载配置文件
$config = Config::load();
$host = Config::get('service.host', 'localhost');
$port = Config::get('service.port', 8883);

// 解析参数
$testText = null;

foreach ($argv as $arg) {
    if (strpos($arg, '--port=') === 0) {
        $portArg = substr($arg, 7);
        if (is_numeric($portArg) && $portArg > 0 && $portArg < 65536) {
            $port = (int)$portArg;
        }
    } elseif (strpos($arg, '--host=') === 0) {
        $host = substr($arg, 7);
    } elseif ($arg !== $argv[0] && strpos($arg, '--') !== 0) {
        $testText = $arg;
    }
}

// 如果没有提供测试文本，使用默认文本
if ($testText === null) {
    $testText = "这是一段测试文本，用于测试Jieba分词服务是否正常工作。景区智能助理可以回答关于景区的各种问题。";
}

// 服务URL
$serviceUrl = "http://{$host}:{$port}/jieba";
$healthUrl = "http://{$host}:{$port}/health";

echo "测试Jieba分词服务...\n";
echo "服务主机: {$host}\n";
echo "服务端口: {$port}\n";
echo "健康检查URL: {$healthUrl}\n";
echo "分词服务URL: {$serviceUrl}\n";
echo "测试文本: {$testText}\n\n";

// 健康检查
echo "1. 执行健康检查...\n";
$ch = curl_init($healthUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);

$response = curl_exec($ch);
$error = curl_error($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($error || $httpCode != 200) {
    echo "健康检查失败: " . ($error ?: "HTTP状态码: {$httpCode}") . "\n";
    echo "请确保Jieba分词服务已启动，可以运行: ./start_jieba_service.sh workerman start\n";
    exit(1);
} else {
    $status = json_decode($response, true);
    echo "健康检查成功!\n";
    echo "服务状态: " . $status['message'] . "\n";
    echo "内存使用: " . $status['memory_usage'] . "\n";
    echo "峰值内存: " . $status['peak_memory'] . "\n\n";
}

// 分词测试
echo "2. 执行分词测试...\n";
$ch = curl_init($serviceUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['text' => $testText]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);
$duration = round(($endTime - $startTime) * 1000, 2);

$error = curl_error($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($error || $httpCode != 200) {
    echo "分词测试失败: " . ($error ?: "HTTP状态码: {$httpCode}") . "\n";
    exit(1);
} else {
    $result = json_decode($response, true);
    if (isset($result['code']) && $result['code'] == 200) {
        echo "分词测试成功!\n";
        echo "分词结果: " . $result['data']['segmented'] . "\n";
        echo "分词耗时: " . $result['data']['duration_ms'] . " 毫秒 (服务端)\n";
        echo "请求耗时: " . $duration . " 毫秒 (客户端)\n";
        echo "原文长度: " . $result['data']['original_length'] . " 字符\n";
        echo "分词数量: " . $result['data']['segments_count'] . " 个\n\n";
    } else {
        echo "分词测试失败: " . json_encode($result) . "\n";
        exit(1);
    }
}

// 性能测试
echo "3. 执行性能测试 (10次连续请求)...\n";
$totalTime = 0;
$times = [];

for ($i = 0; $i < 10; $i++) {
    $ch = curl_init($serviceUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['text' => $testText]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);

    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    $requestTime = round(($endTime - $startTime) * 1000, 2);

    curl_close($ch);

    $result = json_decode($response, true);
    $serviceTime = $result['data']['duration_ms'] ?? 0;

    $times[] = [
        'request' => $requestTime,
        'service' => $serviceTime
    ];

    $totalTime += $requestTime;
    echo ".";
}

echo "\n";
$avgTime = round($totalTime / 10, 2);
echo "平均请求耗时: {$avgTime} 毫秒\n";

// 计算服务端平均耗时
$serviceTotalTime = 0;
foreach ($times as $time) {
    $serviceTotalTime += $time['service'];
}
$avgServiceTime = round($serviceTotalTime / 10, 2);
echo "平均服务端处理耗时: {$avgServiceTime} 毫秒\n";

// 计算网络延迟
$avgNetworkTime = round($avgTime - $avgServiceTime, 2);
echo "平均网络延迟: {$avgNetworkTime} 毫秒\n\n";

echo "测试完成，Jieba分词服务工作正常!\n";
