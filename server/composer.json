{"name": "dsassistant/jieba-service", "description": "Jieba Chinese word segmentation service with HTTP API", "type": "project", "license": "MIT", "authors": [{"name": "dungang", "email": "<EMAIL>"}], "require": {"php": ">=7.0", "fukuball/jieba-php": "^0.33", "workerman/workerman": "^4.0", "ext-json": "*", "ext-curl": "*"}, "suggest": {"ext-swoole": "Required for Swoole implementation"}, "autoload": {"psr-4": {"JiebaService\\": "src/"}}, "scripts": {"start-workerman": "php jieba_service.php start", "stop-workerman": "php jieba_service.php stop", "restart-workerman": "php jieba_service.php restart", "status-workerman": "php jieba_service.php status", "start-swoole": "php jieba_service_swoole.php", "test": "php test_jieba_service.php"}, "config": {"process-timeout": 0}}