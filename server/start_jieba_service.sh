#!/bin/bash

# Jieba分词服务启动脚本
# 使用方法: ./start_jieba_service.sh [workerman|swoole] [start|stop|restart|status] [--port=8883]

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 默认使用workerman
ENGINE=${1:-workerman}
ACTION=${2:-start}
PORT_ARG=""

# 解析参数
for arg in "$@"; do
    if [[ $arg == --port=* || $arg == --host=* || $arg == --workers=* ]]; then
        PORT_ARG="$PORT_ARG $arg"
    fi
done

# 检查PHP是否安装
if ! command -v php &> /dev/null; then
    echo "错误: 未找到PHP命令，请确保PHP已安装并添加到PATH中"
    exit 1
fi

# 检查依赖
if [ "$ENGINE" = "workerman" ]; then
    if ! php -r "exit(extension_loaded('pcntl') ? 0 : 1);" ; then
        echo "错误: PHP pcntl扩展未安装，Workerman需要此扩展"
        exit 1
    fi

    if ! php -r "exit(extension_loaded('posix') ? 0 : 1);" ; then
        echo "错误: PHP posix扩展未安装，Workerman需要此扩展"
        exit 1
    fi

    if ! php -r "exit(class_exists('Workerman\\Worker') ? 0 : 1);" ; then
        echo "错误: Workerman未安装，请运行: composer require workerman/workerman"
        exit 1
    fi
elif [ "$ENGINE" = "swoole" ]; then
    if ! php -r "exit(extension_loaded('swoole') ? 0 : 1);" ; then
        echo "错误: PHP swoole扩展未安装，请安装swoole扩展"
        exit 1
    fi
else
    echo "错误: 不支持的引擎类型: $ENGINE，请使用 workerman 或 swoole"
    exit 1
fi

# 检查Jieba-PHP是否安装
if ! php -r "exit(class_exists('Fukuball\\Jieba\\Jieba') ? 0 : 1);" ; then
    echo "错误: Jieba-PHP未安装，请运行: composer require fukuball/jieba-php"
    exit 1
fi

# 创建数据目录
mkdir -p ../data/dictionary

# 如果自定义词典不存在，创建一个空的
if [ ! -f "../data/dictionary/custom_dict.txt" ]; then
    echo "# 自定义词典，每行一个词" > ../data/dictionary/custom_dict.txt
    echo "景区智能助理" >> ../data/dictionary/custom_dict.txt
    echo "人工智能" >> ../data/dictionary/custom_dict.txt
    echo "创建了默认自定义词典: ../data/dictionary/custom_dict.txt"
fi

# 根据引擎类型执行相应操作
if [ "$ENGINE" = "workerman" ]; then
    case "$ACTION" in
        start)
            echo "启动Workerman Jieba分词服务..."
            if [ -n "$PORT_ARG" ]; then
                php jieba_service.php start -d $PORT_ARG
            else
                php jieba_service.php start -d
            fi
            ;;
        stop)
            echo "停止Workerman Jieba分词服务..."
            php jieba_service.php stop
            ;;
        restart)
            echo "重启Workerman Jieba分词服务..."
            if [ -n "$PORT_ARG" ]; then
                php jieba_service.php restart -d $PORT_ARG
            else
                php jieba_service.php restart -d
            fi
            ;;
        status)
            echo "Workerman Jieba分词服务状态:"
            php jieba_service.php status
            ;;
        *)
            echo "错误: 不支持的操作: $ACTION，请使用 start, stop, restart 或 status"
            exit 1
            ;;
    esac
elif [ "$ENGINE" = "swoole" ]; then
    PID_FILE="jieba_service_swoole.pid"

    case "$ACTION" in
        start)
            echo "启动Swoole Jieba分词服务..."
            if [ -f "$PID_FILE" ]; then
                PID=$(cat "$PID_FILE")
                if ps -p "$PID" > /dev/null; then
                    echo "服务已经在运行，PID: $PID"
                    exit 0
                else
                    rm "$PID_FILE"
                fi
            fi

            if [ -n "$PORT_ARG" ]; then
                nohup php jieba_service_swoole.php $PORT_ARG > jieba_service_swoole.log 2>&1 &
            else
                nohup php jieba_service_swoole.php > jieba_service_swoole.log 2>&1 &
            fi
            echo $! > "$PID_FILE"
            echo "服务已启动，PID: $(cat "$PID_FILE")"
            ;;
        stop)
            echo "停止Swoole Jieba分词服务..."
            if [ -f "$PID_FILE" ]; then
                PID=$(cat "$PID_FILE")
                if ps -p "$PID" > /dev/null; then
                    kill "$PID"
                    rm "$PID_FILE"
                    echo "服务已停止"
                else
                    echo "服务未运行"
                    rm "$PID_FILE"
                fi
            else
                echo "PID文件不存在，服务可能未运行"
            fi
            ;;
        restart)
            echo "重启Swoole Jieba分词服务..."
            if [ -f "$PID_FILE" ]; then
                PID=$(cat "$PID_FILE")
                if ps -p "$PID" > /dev/null; then
                    kill "$PID"
                    rm "$PID_FILE"
                    echo "服务已停止"
                else
                    rm "$PID_FILE"
                fi
            fi

            if [ -n "$PORT_ARG" ]; then
                nohup php jieba_service_swoole.php $PORT_ARG > jieba_service_swoole.log 2>&1 &
            else
                nohup php jieba_service_swoole.php > jieba_service_swoole.log 2>&1 &
            fi
            echo $! > "$PID_FILE"
            echo "服务已重启，PID: $(cat "$PID_FILE")"
            ;;
        status)
            echo "Swoole Jieba分词服务状态:"
            if [ -f "$PID_FILE" ]; then
                PID=$(cat "$PID_FILE")
                if ps -p "$PID" > /dev/null; then
                    echo "服务正在运行，PID: $PID"
                    echo "内存使用: $(ps -o rss= -p "$PID" | awk '{print $1/1024 " MB"}')"
                    echo "运行时间: $(ps -o etime= -p "$PID")"
                else
                    echo "服务未运行，但PID文件存在"
                fi
            else
                echo "服务未运行"
            fi
            ;;
        *)
            echo "错误: 不支持的操作: $ACTION，请使用 start, stop, restart 或 status"
            exit 1
            ;;
    esac
fi

# 检查服务是否正常运行
if [ "$ACTION" = "start" ] || [ "$ACTION" = "restart" ]; then
    echo "等待服务启动..."
    sleep 2

    # 尝试访问健康检查接口
    if command -v curl &> /dev/null; then
        HEALTH_CHECK=$(curl -s http://localhost:8883/health)
        if [ $? -eq 0 ]; then
            echo "服务健康检查成功: $HEALTH_CHECK"
        else
            echo "警告: 服务健康检查失败，请检查日志"
        fi
    else
        echo "提示: 未找到curl命令，无法执行健康检查"
    fi

    echo "分词服务API地址: http://localhost:8883/jieba"
    echo "测试命令: curl -X POST -H \"Content-Type: application/json\" -d '{\"text\":\"这是一段需要分词的文本\"}' http://localhost:8883/jieba"
fi

exit 0
