#!/bin/bash

# Jieba分词服务安装脚本
# 使用方法: ./install.sh [--with-swoole]

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 检查是否安装Swoole
WITH_SWOOLE=0
if [ "$1" = "--with-swoole" ]; then
    WITH_SWOOLE=1
fi

# 检查PHP是否安装
if ! command -v php &> /dev/null; then
    echo "错误: 未找到PHP命令，请确保PHP已安装并添加到PATH中"
    exit 1
fi

# 检查PHP版本
PHP_VERSION=$(php -r "echo PHP_VERSION;")
echo "检测到PHP版本: $PHP_VERSION"

# 检查PHP扩展
echo "检查PHP扩展..."
MISSING_EXTENSIONS=()

# 检查必要的扩展
if ! php -r "exit(extension_loaded('json') ? 0 : 1);" ; then
    MISSING_EXTENSIONS+=("json")
fi

if ! php -r "exit(extension_loaded('curl') ? 0 : 1);" ; then
    MISSING_EXTENSIONS+=("curl")
fi

# 检查Workerman所需扩展
if ! php -r "exit(extension_loaded('pcntl') ? 0 : 1);" ; then
    MISSING_EXTENSIONS+=("pcntl")
fi

if ! php -r "exit(extension_loaded('posix') ? 0 : 1);" ; then
    MISSING_EXTENSIONS+=("posix")
fi

# 如果需要Swoole，检查Swoole扩展
if [ $WITH_SWOOLE -eq 1 ]; then
    if ! php -r "exit(extension_loaded('swoole') ? 0 : 1);" ; then
        MISSING_EXTENSIONS+=("swoole")
    fi
fi

# 如果有缺失的扩展，提示安装
if [ ${#MISSING_EXTENSIONS[@]} -gt 0 ]; then
    echo "警告: 以下PHP扩展未安装:"
    for ext in "${MISSING_EXTENSIONS[@]}"; do
        echo "  - $ext"
    done
    
    echo "请安装缺失的扩展，例如:"
    echo "  Ubuntu/Debian: sudo apt-get install php-{extension}"
    echo "  CentOS/RHEL: sudo yum install php-{extension}"
    echo "  macOS (Homebrew): brew install php@7.4 (包含大部分扩展)"
    echo "  Windows: 在php.ini中启用扩展"
    
    if [[ " ${MISSING_EXTENSIONS[*]} " =~ " swoole " ]]; then
        echo "安装Swoole扩展: pecl install swoole"
    fi
    
    read -p "是否继续安装? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查Composer是否安装
if ! command -v composer &> /dev/null; then
    echo "Composer未安装，正在下载安装脚本..."
    php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    php composer-setup.php --quiet
    php -r "unlink('composer-setup.php');"
    echo "Composer安装完成，使用本地composer.phar"
    COMPOSER_CMD="php composer.phar"
else
    echo "检测到Composer已安装"
    COMPOSER_CMD="composer"
fi

# 创建数据目录
echo "创建数据目录..."
mkdir -p ../data/dictionary

# 如果自定义词典不存在，创建一个空的
if [ ! -f "../data/dictionary/custom_dict.txt" ]; then
    echo "# 自定义词典，每行一个词" > ../data/dictionary/custom_dict.txt
    echo "景区智能助理" >> ../data/dictionary/custom_dict.txt
    echo "人工智能" >> ../data/dictionary/custom_dict.txt
    echo "创建了默认自定义词典: ../data/dictionary/custom_dict.txt"
fi

# 安装依赖
echo "安装Composer依赖..."
$COMPOSER_CMD install --no-interaction

# 添加执行权限
echo "添加执行权限..."
chmod +x start_jieba_service.sh

# 安装完成
echo "安装完成!"
echo "您可以使用以下命令启动服务:"
echo "  ./start_jieba_service.sh workerman start"

if [ $WITH_SWOOLE -eq 1 ]; then
    echo "  或者使用Swoole实现:"
    echo "  ./start_jieba_service.sh swoole start"
fi

echo "使用以下命令测试服务:"
echo "  php test_jieba_service.php"
echo ""
echo "现在要启动服务吗? (y/n)"
read -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    ./start_jieba_service.sh workerman start
fi
