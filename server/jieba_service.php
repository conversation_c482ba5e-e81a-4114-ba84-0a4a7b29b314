<?php
/**
 * Jieba分词服务 - Workerman实现
 *
 * 使用方法：
 * 1. 安装依赖：composer install
 * 2. 启动服务：php jieba_service.php start -d
 * 3. 调用API：curl -X POST -H "Content-Type: application/json" -d '{"text":"这是一段需要分词的文本"}' http://localhost:8883/jieba
 */

require_once __DIR__ . '/vendor/autoload.php';
use Workerman\Worker;
use JiebaService\JiebaSegmenter;
use JiebaService\StopWords;
use JiebaService\Config;

// 检查是否安装了pcntl和posix扩展
if (!extension_loaded('pcntl') || !extension_loaded('posix')) {
    die("请安装pcntl和posix扩展\n");
}

// 加载配置
$config = Config::load();
$port = Config::get('service.port');
$workers = Config::get('service.workers');

// 获取停用词
$stopWords = StopWords::getDefaultStopWords();

// 初始化分词器
echo "正在初始化Jieba分词器...\n";
$segmenter = new JiebaSegmenter($stopWords);

// 创建HTTP服务器
$http_worker = new Worker("http://0.0.0.0:{$port}");
$http_worker->count = $workers; // 进程数，从配置获取

echo "将使用端口: {$port}，工作进程数: {$workers}\n";

// 处理请求
$http_worker->onMessage = function($connection, $request) use ($segmenter) {
    // 解析请求
    $method = $request->method();
    $path = $request->path();

    // 处理健康检查
    if ($path === '/health' && $method === 'GET') {
        $memoryInfo = $segmenter->getMemoryUsage();
        $connection->send(json_encode([
            'code' => 200,
            'message' => 'Jieba service is running',
            'memory_usage' => $memoryInfo['current'],
            'peak_memory' => $memoryInfo['peak']
        ]));
        return;
    }

    // 只处理/jieba路径的POST请求
    if ($path !== '/jieba' || $method !== 'POST') {
        $connection->send(json_encode([
            'code' => 400,
            'message' => 'Invalid request. Use POST /jieba or GET /health'
        ]));
        return;
    }

    // 获取POST数据
    $data = json_decode($request->rawBody(), true);
    $text = $data['text'] ?? '';

    if (empty($text)) {
        $connection->send(json_encode([
            'code' => 400,
            'message' => 'Text parameter is required'
        ]));
        return;
    }

    // 执行分词
    $startTime = microtime(true);
    $segments = $segmenter->segment($text);
    $segmentedText = implode(' ', $segments);
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);

    // 返回结果
    $connection->send(json_encode([
        'code' => 200,
        'data' => [
            'segmented' => $segmentedText,
            'duration_ms' => $duration,
            'original_length' => mb_strlen($text, 'UTF-8'),
            'segments_count' => count($segments)
        ]
    ]));
};

// 启动服务
echo "Jieba分词服务已启动，监听端口: {$port}\n";
echo "健康检查: http://localhost:{$port}/health\n";
echo "分词API: http://localhost:{$port}/jieba (POST)\n";

// 如果直接运行脚本，则启动服务
if (!defined('GLOBAL_START')) {
    Worker::runAll();
}
