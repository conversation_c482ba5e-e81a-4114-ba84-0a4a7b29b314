<?php

namespace JiebaService;

use Fukuball\Jieba\Jieba;
use Fukuball\Jieba\Finalseg;

/**
 * Jieba分词器类
 * 封装Jieba-PHP的分词功能
 */
class JiebaSegmenter
{
    /**
     * 停用词列表
     * @var array
     */
    protected $stopWords = [];
    
    /**
     * 是否已初始化
     * @var bool
     */
    protected static $initialized = false;
    
    /**
     * 构造函数
     * 
     * @param array $stopWords 停用词列表
     */
    public function __construct(array $stopWords = [])
    {
        // 初始化Jieba分词（如果尚未初始化）
        if (!self::$initialized) {
            $startTime = microtime(true);
            Jieba::init();
            Finalseg::init();
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            echo "Jieba初始化完成，耗时: {$duration}秒\n";
            self::$initialized = true;
        }
        
        // 设置停用词
        $this->stopWords = $stopWords;
        
        // 加载自定义词典（如果有）
        $this->loadCustomDictionary();
    }
    
    /**
     * 加载自定义词典
     */
    protected function loadCustomDictionary()
    {
        $customDictPath = dirname(dirname(__DIR__)) . '/data/dictionary/custom_dict.txt';
        if (file_exists($customDictPath)) {
            $words = file($customDictPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $count = 0;
            foreach ($words as $word) {
                if (!empty($word) && substr($word, 0, 1) !== '#') {
                    Jieba::add_word($word);
                    $count++;
                }
            }
            echo "已加载 {$count} 个自定义词语\n";
        }
    }
    
    /**
     * 分词处理
     * 
     * @param string $text 需要分词的文本
     * @param bool $filterStopWords 是否过滤停用词
     * @param bool $filterSingleChar 是否过滤单字词
     * @return array 分词结果数组
     */
    public function segment($text, $filterStopWords = true, $filterSingleChar = true)
    {
        if (empty($text)) {
            return [];
        }
        
        // 执行分词
        $segments = Jieba::cut($text);
        
        // 过滤处理
        if ($filterStopWords || $filterSingleChar) {
            $segments = array_filter($segments, function($word) use ($filterStopWords, $filterSingleChar) {
                // 过滤单字词
                if ($filterSingleChar && mb_strlen($word, 'UTF-8') < 2) {
                    return false;
                }
                
                // 过滤停用词
                if ($filterStopWords && in_array($word, $this->stopWords)) {
                    return false;
                }
                
                return true;
            });
        }
        
        return array_values($segments);
    }
    
    /**
     * 提取关键词
     * 
     * @param string $text 文本
     * @param int $topK 返回的关键词数量
     * @return array 关键词数组
     */
    public function extractKeywords($text, $topK = 5)
    {
        if (empty($text)) {
            return [];
        }
        
        // 使用Jieba的TF-IDF提取关键词
        $keywords = Jieba::extractTags($text, $topK);
        
        return array_keys($keywords);
    }
    
    /**
     * 获取内存使用情况
     * 
     * @return array 内存使用信息
     */
    public function getMemoryUsage()
    {
        return [
            'current' => round(memory_get_usage() / 1024 / 1024, 2) . ' MB',
            'peak' => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB'
        ];
    }
}
