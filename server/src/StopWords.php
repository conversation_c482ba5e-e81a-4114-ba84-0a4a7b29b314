<?php

namespace JiebaService;

/**
 * 停用词管理类
 */
class StopWords
{
    /**
     * 获取默认停用词列表
     * 
     * @return array 停用词数组
     */
    public static function getDefaultStopWords()
    {
        return [
            // 常用虚词
            '的', '了', '和', '与', '或', '是', '在', '有', '这', '那',
            '你', '我', '他', '她', '它', '们', '个', '为', '以', '及',
            '上', '下', '前', '后', '左', '右', '中', '内', '外', '吗',
            '啊', '呢', '吧', '呀', '哪', '什么', '怎么', '如何', '为什么',
            
            // 常用连接词
            '因为', '所以', '但是', '而且', '如果', '虽然', '即使', '不过',
            '然而', '因此', '于是', '其实', '反而', '只是', '不仅', '还有',
            
            // 常用副词
            '很', '非常', '特别', '十分', '极其', '更加', '越来越', '略微',
            '稍微', '几乎', '差不多', '大约', '也许', '可能', '一定', '必须',
            
            // 常用介词
            '从', '向', '对', '把', '被', '给', '让', '比', '像', '随着',
            '通过', '根据', '按照', '除了', '关于', '由于', '至于', '针对',
            
            // 常用量词
            '个', '只', '条', '张', '片', '块', '本', '册', '部', '台',
            '辆', '颗', '粒', '座', '幢', '栋', '间', '件', '篇', '首',
            
            // 常用数词
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '百', '千', '万', '亿', '第', '每', '某', '几', '多', '少',
            
            // 常用时间词
            '年', '月', '日', '时', '分', '秒', '天', '周', '星期', '季度',
            '今天', '明天', '昨天', '早上', '上午', '中午', '下午', '晚上', '夜里',
            
            // 常用代词
            '这个', '那个', '这些', '那些', '自己', '别人', '其他', '其它',
            '谁', '哪个', '哪些', '任何', '所有', '每个', '各个', '各种',
            
            // 常用助词
            '的', '地', '得', '着', '过', '了', '来着', '起来', '下去',
            
            // 标点符号
            '。', '，', '、', '；', '：', '？', '！', '…', '—', '·',
            '"', '"', ''', ''', '（', '）', '《', '》', '【', '】',
            '[', ']', '{', '}', '「', '」', '『', '』', '〈', '〉'
        ];
    }
    
    /**
     * 从文件加载停用词
     * 
     * @param string $filePath 停用词文件路径
     * @return array 停用词数组
     */
    public static function loadFromFile($filePath)
    {
        if (!file_exists($filePath)) {
            return self::getDefaultStopWords();
        }
        
        $stopWords = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        // 过滤注释行和空行
        $stopWords = array_filter($stopWords, function($line) {
            return !empty($line) && substr($line, 0, 1) !== '#';
        });
        
        return $stopWords;
    }
    
    /**
     * 合并停用词列表
     * 
     * @param array $lists 停用词列表数组
     * @return array 合并后的停用词数组
     */
    public static function merge(array ...$lists)
    {
        $result = [];
        
        foreach ($lists as $list) {
            $result = array_merge($result, $list);
        }
        
        return array_unique($result);
    }
}
