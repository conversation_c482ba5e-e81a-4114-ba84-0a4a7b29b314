<?php

namespace JiebaService;

/**
 * 配置管理类
 */
class Config
{
    /**
     * 配置数据
     * @var array
     */
    protected static $config = [];
    
    /**
     * 是否已加载配置
     * @var bool
     */
    protected static $loaded = false;
    
    /**
     * 加载配置
     * 
     * @param string $configFile 配置文件路径
     * @return array 配置数据
     */
    public static function load($configFile = null)
    {
        if (self::$loaded) {
            return self::$config;
        }
        
        // 默认配置文件路径
        if ($configFile === null) {
            $configFile = dirname(__DIR__) . '/config.php';
        }
        
        // 加载配置文件
        if (file_exists($configFile)) {
            self::$config = require $configFile;
        } else {
            // 默认配置
            self::$config = [
                'service' => [
                    'host' => 'localhost',
                    'port' => 8883,
                    'workers' => 1,
                ],
                'segmentation' => [
                    'filter_stop_words' => true,
                    'filter_single_char' => true,
                    'custom_dict_path' => dirname(dirname(__DIR__)) . '/data/dictionary/custom_dict.txt',
                ],
                'cache' => [
                    'enabled' => true,
                    'ttl' => 86400,
                    'prefix' => 'jieba_',
                ],
                'log' => [
                    'enabled' => true,
                    'path' => dirname(__DIR__) . '/logs',
                    'level' => 'info',
                ],
            ];
        }
        
        // 处理命令行参数覆盖
        self::processCommandLineArgs();
        
        self::$loaded = true;
        return self::$config;
    }
    
    /**
     * 获取配置项
     * 
     * @param string $key 配置键名，使用点号分隔多级配置，如 'service.port'
     * @param mixed $default 默认值
     * @return mixed 配置值
     */
    public static function get($key, $default = null)
    {
        // 确保配置已加载
        if (!self::$loaded) {
            self::load();
        }
        
        // 解析键名
        $keys = explode('.', $key);
        $value = self::$config;
        
        // 逐级查找配置
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * 设置配置项
     * 
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @return void
     */
    public static function set($key, $value)
    {
        // 确保配置已加载
        if (!self::$loaded) {
            self::load();
        }
        
        // 解析键名
        $keys = explode('.', $key);
        $lastKey = array_pop($keys);
        $config = &self::$config;
        
        // 逐级查找配置
        foreach ($keys as $k) {
            if (!isset($config[$k]) || !is_array($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        // 设置配置值
        $config[$lastKey] = $value;
    }
    
    /**
     * 处理命令行参数
     * 
     * @return void
     */
    protected static function processCommandLineArgs()
    {
        global $argv;
        
        if (!isset($argv) || !is_array($argv)) {
            return;
        }
        
        // 处理端口参数
        foreach ($argv as $arg) {
            if (strpos($arg, '--port=') === 0) {
                $portArg = substr($arg, 7);
                if (is_numeric($portArg) && $portArg > 0 && $portArg < 65536) {
                    self::set('service.port', (int)$portArg);
                }
            }
            
            // 处理主机参数
            if (strpos($arg, '--host=') === 0) {
                $hostArg = substr($arg, 7);
                if (!empty($hostArg)) {
                    self::set('service.host', $hostArg);
                }
            }
            
            // 处理工作进程数参数
            if (strpos($arg, '--workers=') === 0) {
                $workersArg = substr($arg, 10);
                if (is_numeric($workersArg) && $workersArg > 0) {
                    self::set('service.workers', (int)$workersArg);
                }
            }
        }
    }
}
