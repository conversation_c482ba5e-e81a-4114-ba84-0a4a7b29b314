# Jieba分词服务

这是一个基于Jieba-PHP的分词服务，提供HTTP API接口，用于在PHP应用中高效地进行中文分词。

## 特点

- **高效初始化**：Jieba分词只需初始化一次，避免每个请求都重新加载词典
- **内存优化**：常驻进程模式，避免重复加载词典消耗内存
- **简单API**：提供简单的HTTP API，易于集成
- **双引擎支持**：支持Workerman和Swoole两种实现
- **健康检查**：提供健康检查接口，方便监控
- **自定义词典**：支持加载自定义词典，提高分词准确性

## 安装

### 1. 克隆项目

```bash
git clone <repository-url>
cd server
```

### 2. 安装依赖

使用安装脚本（推荐）：

```bash
chmod +x install.sh
./install.sh          # 安装基本依赖
./install.sh --with-swoole  # 安装包括Swoole在内的依赖
```

或者手动安装：

```bash
# 安装Composer依赖
composer install

# 如果使用Swoole实现，需要安装Swoole扩展
pecl install swoole
```

## 目录结构

```
server/
├── jieba_service.php           # Workerman实现的分词服务
├── jieba_service_swoole.php    # Swoole实现的分词服务
├── start_jieba_service.sh      # 启动脚本
├── test_jieba_service.php      # 测试脚本
├── install.sh                  # 安装脚本
├── composer.json               # Composer配置
├── src/                        # 源代码目录
│   ├── JiebaSegmenter.php      # 分词器类
│   └── StopWords.php           # 停用词管理类
└── README.md                   # 说明文档

../data/dictionary/
└── custom_dict.txt             # 自定义词典
```

## 配置

服务使用`config.php`文件进行配置，主要配置项包括：

```php
return [
    // 服务配置
    'service' => [
        'host' => 'localhost',     // 服务主机地址
        'port' => 8883,            // 服务端口
        'workers' => 1,            // 工作进程数（Workerman）
    ],

    // 分词配置
    'segmentation' => [
        'filter_stop_words' => true,   // 是否过滤停用词
        'filter_single_char' => true,  // 是否过滤单字词
        'custom_dict_path' => '...',   // 自定义词典路径
    ],

    // 缓存配置
    'cache' => [
        'enabled' => true,         // 是否启用缓存
        'ttl' => 86400,            // 缓存时间（秒）
        'prefix' => 'jieba_',      // 缓存前缀
    ],

    // 日志配置
    'log' => [
        'enabled' => true,         // 是否启用日志
        'path' => '...',           // 日志目录
        'level' => 'info',         // 日志级别
    ],
];
```

修改配置文件后，需要重启服务才能生效。

## 使用方法

### 1. 启动服务

使用启动脚本：

```bash
# 使用Workerman实现（推荐）
./start_jieba_service.sh workerman start

# 使用Swoole实现
./start_jieba_service.sh swoole start

# 命令行参数会覆盖配置文件中的设置
./start_jieba_service.sh workerman start --port=8884 --host=0.0.0.0 --workers=4

# 查看服务状态
./start_jieba_service.sh workerman status

# 停止服务
./start_jieba_service.sh workerman stop

# 重启服务
./start_jieba_service.sh workerman restart
```

或者使用Composer脚本：

```bash
# 使用Workerman
composer start-workerman

# 使用Swoole
composer start-swoole

# 使用自定义端口
composer start-workerman-custom-port  # 使用8884端口
composer start-swoole-custom-port     # 使用8884端口
```

### 2. 测试服务

```bash
# 使用测试脚本
php test_jieba_service.php "这是一段测试文本，用于测试Jieba分词服务"

# 指定自定义端口
php test_jieba_service.php "这是一段测试文本" --port=8884

# 或者使用Composer脚本
composer test
composer test-custom-port  # 使用8884端口

# 使用curl命令
curl -X POST -H "Content-Type: application/json" -d '{"text":"这是一段测试文本"}' http://localhost:8883/jieba

# 健康检查
curl http://localhost:8883/health

# 自定义端口
curl -X POST -H "Content-Type: application/json" -d '{"text":"这是一段测试文本"}' http://localhost:8884/jieba
curl http://localhost:8884/health
```

### 3. 在应用中使用

在您的应用中，使用`JiebaClient`类调用分词服务：

```php
// 创建客户端实例（自动从配置文件读取主机和端口）
$jiebaClient = new \addons\dsassistant\library\JiebaClient();

// 也可以手动指定主机和端口，覆盖配置文件
$jiebaClient = new \addons\dsassistant\library\JiebaClient('localhost', 8884);

// 分词处理
$segmented = $jiebaClient->segment('这是一段需要分词的文本');

// 检查服务状态
if (!$jiebaClient->isServiceAvailable()) {
    echo "分词服务不可用";
}
```

客户端会自动读取服务端的`config.php`配置文件，因此只需修改配置文件，客户端和服务端就能保持一致的配置。

## API接口

### 1. 分词接口

- **URL**: `http://localhost:8883/jieba`
- **方法**: POST
- **请求体**:
  ```json
  {
    "text": "需要分词的文本"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "data": {
      "segmented": "需要 分词 文本",
      "duration_ms": 5.23,
      "original_length": 7,
      "segments_count": 3
    }
  }
  ```

### 2. 健康检查接口

- **URL**: `http://localhost:8883/health`
- **方法**: GET
- **响应**:
  ```json
  {
    "code": 200,
    "message": "Jieba service is running",
    "memory_usage": "120.5 MB",
    "peak_memory": "150.2 MB"
  }
  ```

## 自定义词典

您可以通过编辑`../data/dictionary/custom_dict.txt`文件来添加自定义词汇，每行一个词：

```
景区智能助理
人工智能
自然语言处理
```

修改词典后，需要重启服务才能生效：

```bash
./start_jieba_service.sh workerman restart
```

## 性能优化

1. **增加工作进程数**：修改`jieba_service.php`中的`$http_worker->count`值
2. **调整内存限制**：如果词典较大，可能需要增加PHP内存限制
3. **使用Swoole实现**：在高并发场景下，Swoole实现可能有更好的性能

## 故障排除

1. **服务无法启动**：
   - 检查PHP版本（需要PHP 7.0+）
   - 检查扩展是否安装（pcntl、posix或swoole）
   - 检查端口是否被占用（默认8883）

2. **分词结果不准确**：
   - 检查自定义词典是否正确加载
   - 考虑添加更多领域相关词汇到自定义词典

3. **服务内存占用过高**：
   - 减少自定义词典大小
   - 调整PHP内存限制
   - 定期重启服务释放内存

## 许可证

MIT
