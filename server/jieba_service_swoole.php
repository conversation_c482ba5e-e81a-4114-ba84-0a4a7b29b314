<?php
/**
 * Jieba分词服务 - Swoole实现
 *
 * 使用方法：
 * 1. 安装依赖：composer install
 * 2. 确保安装了Swoole扩展：pecl install swoole
 * 3. 启动服务：php jieba_service_swoole.php
 * 4. 调用API：curl -X POST -H "Content-Type: application/json" -d '{"text":"这是一段需要分词的文本"}' http://localhost:8883/jieba
 */

require_once __DIR__ . '/vendor/autoload.php';
use JiebaService\JiebaSegmenter;
use JiebaService\StopWords;
use JiebaService\Config;

// 检查是否安装了Swoole扩展
if (!extension_loaded('swoole')) {
    die("请安装Swoole扩展\n");
}

// 加载配置
$config = Config::load();
$port = Config::get('service.port');
$workers = Config::get('service.workers');

// 获取停用词
$stopWords = StopWords::getDefaultStopWords();

// 初始化分词器
echo "正在初始化Jieba分词器...\n";
$segmenter = new JiebaSegmenter($stopWords);

// 创建HTTP服务器
$server = new Swoole\HTTP\Server('0.0.0.0', $port);

echo "将使用端口: {$port}，工作进程数: {$workers}\n";

// 配置选项
$server->set([
    'worker_num' => $workers, // 进程数，从配置获取
    'max_request' => 10000, // 每个工作进程处理10000个请求后重启，避免内存泄漏
    'daemonize' => false, // 是否作为守护进程运行
    'log_file' => __DIR__ . '/jieba_service.log', // 日志文件
]);

// 处理请求
$server->on('request', function ($request, $response) use ($segmenter) {
    // 处理健康检查
    if ($request->server['request_uri'] === '/health' && $request->server['request_method'] === 'GET') {
        $memoryInfo = $segmenter->getMemoryUsage();
        $response->header('Content-Type', 'application/json');
        $response->end(json_encode([
            'code' => 200,
            'message' => 'Jieba service is running',
            'memory_usage' => $memoryInfo['current'],
            'peak_memory' => $memoryInfo['peak']
        ]));
        return;
    }

    // 只处理/jieba路径的POST请求
    if ($request->server['request_uri'] !== '/jieba' || $request->server['request_method'] !== 'POST') {
        $response->status(400);
        $response->header('Content-Type', 'application/json');
        $response->end(json_encode([
            'code' => 400,
            'message' => 'Invalid request. Use POST /jieba or GET /health'
        ]));
        return;
    }

    // 获取POST数据
    $data = json_decode($request->rawContent(), true);
    $text = $data['text'] ?? '';

    if (empty($text)) {
        $response->status(400);
        $response->header('Content-Type', 'application/json');
        $response->end(json_encode([
            'code' => 400,
            'message' => 'Text parameter is required'
        ]));
        return;
    }

    // 执行分词
    $startTime = microtime(true);
    $segments = $segmenter->segment($text);
    $segmentedText = implode(' ', $segments);
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);

    // 返回结果
    $response->header('Content-Type', 'application/json');
    $response->end(json_encode([
        'code' => 200,
        'data' => [
            'segmented' => $segmentedText,
            'duration_ms' => $duration,
            'original_length' => mb_strlen($text, 'UTF-8'),
            'segments_count' => count($segments)
        ]
    ]));
});

// 启动服务
echo "Jieba分词服务已启动，监听端口: {$port}\n";
echo "健康检查: http://localhost:{$port}/health\n";
echo "分词API: http://localhost:{$port}/jieba (POST)\n";
$server->start();
