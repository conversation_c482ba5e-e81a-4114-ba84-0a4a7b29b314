# 景区智能助理插件安装指南

## 系统要求

- FastAdmin 1.2.0+
- PHP 7.1+
- MySQL 5.7+
- 已安装Composer

## 安装步骤

### 1. 下载插件

从官方渠道下载插件压缩包。

### 2. 安装插件

#### 方法一：通过FastAdmin后台安装

1. 登录FastAdmin后台
2. 进入插件管理
3. 点击"上传插件"按钮
4. 选择下载好的插件压缩包
5. 等待安装完成

#### 方法二：手动安装

1. 解压插件压缩包
2. 将解压后的文件夹上传到FastAdmin的`/addons/`目录下
3. 登录FastAdmin后台
4. 进入插件管理
5. 找到"景区DeepSeek智能助理"插件
6. 点击"安装"按钮

### 3. 配置插件

1. 安装完成后，点击插件列表中的"配置"按钮
2. 填写以下配置信息：
   - 向量嵌入服务配置（选择一个主要提供商和一个备用提供商）
   - 向量数据库配置（可选，默认使用MySQL）
   - 系统提示词
   - 缓存时间
   - 置信度阈值
   - 微信公众号配置（如需使用）
   - 腾讯地图Key（如需使用）
   - 客服电话

### 4. 微信公众号配置（可选）

如需使用微信公众号功能，请按以下步骤配置：

1. 登录微信公众平台
2. 进入"开发 > 基本配置"
3. 设置服务器配置：
   - URL：`https://your-domain.com/addons/dsassistant/wechat`
   - Token：与插件配置中的Token保持一致
   - EncodingAESKey：可自行生成
4. 进入"自定义菜单"配置菜单项，建议添加以下菜单：
   - 智能问答：链接到 `https://your-domain.com/addons/dsassistant/index/chat`
   - 景点导览：链接到 `https://your-domain.com/addons/dsassistant/index/guide`
   - 失物招领：链接到 `https://your-domain.com/addons/dsassistant/index/lostfound`
   - 预警信息：链接到 `https://your-domain.com/addons/dsassistant/index/warning`

### 5. 小程序配置（可选）

如需使用小程序功能，请按以下步骤配置：

1. 将插件目录下的`uniapp/dsassistant`导入HBuilderX
2. 修改`main.js`中的API地址配置，将`baseUrl`修改为您的网站地址：
   ```javascript
   Vue.prototype.$baseUrl = 'https://your-domain.com';
   ```
3. 在微信小程序管理后台添加腾讯地图SDK：
   - 登录微信小程序管理后台
   - 进入"设置 > 第三方设置 > 腾讯地图SDK"
   - 启用腾讯地图SDK，填写腾讯地图Key
4. 编译发布小程序

### 6. 数据初始化

1. 进入后台管理界面
2. 点击"景区智能助理 > 知识库管理"
3. 添加景区相关知识
4. 点击"景区智能助理 > 景点管理"
5. 添加景区内的景点信息
6. 点击"景区智能助理 > 预警管理"
7. 添加预警信息模板
8. 点击"景区智能助理 > 向量管理"
9. 初始化向量数据库（如果使用外部向量数据库）

## 常见问题

### 1. 安装失败

- 检查PHP版本是否满足要求
- 检查FastAdmin版本是否满足要求
- 检查插件压缩包是否完整

### 2. API调用失败

- 检查向量嵌入服务API密钥是否正确
- 检查网络连接是否正常
- 检查服务器是否能访问相应的API服务
- 检查向量数据库配置是否正确（如使用外部向量数据库）

### 3. 微信公众号配置失败

- 检查URL是否能正常访问
- 检查Token是否与公众平台配置一致
- 检查公众号是否有接口权限

### 4. 小程序编译失败

- 检查HBuilderX版本是否最新
- 检查uni-app依赖是否完整
- 检查API地址配置是否正确

### 5. 向量搜索效率低下

- 考虑使用外部向量数据库提高效率
- 检查MySQL服务器配置是否合理
- 优化知识库内容，避免过多重复信息

### 6. 用户识别问题

- 检查小程序和网页端是否正确生成和传递设备ID
- 检查服务器端是否正确处理用户标识
- 在局域网环境中，确保使用设备ID而非IP地址作为用户标识

## 技术支持

如遇到安装或使用问题，请联系技术支持：

- 邮箱：<EMAIL>
- 电话：400-123-4567
- 工作时间：周一至周五 9:00-18:00
